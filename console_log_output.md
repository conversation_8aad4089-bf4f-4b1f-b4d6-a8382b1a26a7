--- Processing 1185/1257: "Custom Bullseye - Salad 3rd floor (*)" ---
VM2714:70     ✅ items[0].inventory_group: 5738823 → 20433710 ("Chopped Wedge" → "Chopped Wedge (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Custom Bullseye - Salad 3rd floor (*)" with deep reference changes
VM2714:50
--- Processing 1186/1257: "Fried Chicken Sliders & Truffle Fries (*)" ---
VM2714:74     ⚠️ items[0].inventory_group: 6274986 ("house pickles | roasted garlic aioli | lettuce | truffle & herb fries") - no (*) version found
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1187/1257: "Charcuterie (*)" ---
VM2714:70     ✅ items[0].inventory_group: 7736225 → 20432698 ("assortment of cured & smoked meats & cheeses | spiced & candied nuts | breads | honeys" → "assortment of cured & smoked meats & cheeses | spiced & candied nuts | breads | honeys (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Charcuterie (*)" with deep reference changes
VM2714:50
--- Processing 1188/1257: "Mini Jack Daniel's Chocolate Mousse (*)" ---
VM2714:74     ⚠️ items[0].inventory_group: 2036359 (" Glassware- Shot Glass ") - no (*) version found
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1189/1257: "Mango Napoleon (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1190/1257: "Key Lime Pie (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1191/1257: "Jack Daniel’s Mousse (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1192/1257: "Plated Desserts Choice List - 1 Selection  (*)" ---
VM2714:70     ✅ items[0].choices[0].inventory_group: 1699754 → 20433988 ("Banana Pudding" → "Banana Pudding (*)")
VM2714:70     ✅ items[0].choices[1].inventory_group: 1699758 → 20433960 ("Chilled Citrus Soufflé" → "Chilled Citrus Soufflé (*)")
VM2714:70     ✅ items[0].choices[2].inventory_group: 1699766 → 20433933 ("Dark Chocolate Mousse" → "Dark Chocolate Mousse (*)")
VM2714:70     ✅ items[0].choices[3].inventory_group: 1699759 → 20433959 ("Deconstructed Pecan Pie" → "Deconstructed Pecan Pie (*)")
VM2714:70     ✅ items[0].choices[4].inventory_group: 1699750 → 20433964 ("Flourless Chocolate Cake" → "Flourless Chocolate Cake (*)")
VM2714:70     ✅ items[0].choices[5].inventory_group: 1699763 → 20433934 ("French Toast Bread Pudding" → "French Toast Bread Pudding (*)")
VM2714:70     ✅ items[0].choices[6].inventory_group: 1699762 → 20433957 ("Gingerbread Shortcake" → "Gingerbread Shortcake (*)")
VM2714:70     ✅ items[0].choices[7].inventory_group: 1699764 → 20433944 ("Housemade Goo Goo Cluster" → "Housemade Goo Goo Cluster (*)")
VM2714:70     ✅ items[0].choices[8].inventory_group: 1699757 → 20433961 ("Jack Daniel’s Chocolate Bread Pudding" → "Jack Daniel’s Chocolate Bread Pudding (*)")
VM2714:70     ✅ items[0].choices[9].inventory_group: 1699760 → 20433983 ("Jack Daniel’s Mousse" → "Jack Daniel’s Mousse (*)")
VM2714:70     ✅ items[0].choices[10].inventory_group: 1699751 → 20433963 ("Key Lime Pie" → "Key Lime Pie (*)")
VM2714:70     ✅ items[0].choices[11].inventory_group: 1699753 → 20433962 ("Mango Napoleon" → "Mango Napoleon (*)")
VM2714:70     ✅ items[0].choices[12].inventory_group: 1699761 → 20432886 ("Port Pear Cobbler" → "Port Pear Cobbler (*)")
VM2714:70     ✅ items[0].choices[13].inventory_group: 1699768 → 20433954 ("Pumpkin Creme Brûlée" → "Pumpkin Creme Brûlée (*)")
VM2714:70     ✅ items[0].choices[14].inventory_group: 1699755 → 20433946 ("Seasonal Cobbler" → "Seasonal Cobbler (*)")
VM2714:70     ✅ items[0].choices[15].inventory_group: 1699770 → 20433968 ("Southern Confections" → "Southern Confections (*)")
VM2714:70     ✅ items[0].choices[16].inventory_group: 1699771 → 20433942 ("Southern Trio" → "Southern Trio (*)")
VM2714:70     ✅ items[0].choices[17].inventory_group: 1699752 → 20433919 ("Strawberry Shortcake" → "Strawberry Shortcake (*)")
VM2714:70     ✅ items[0].choices[18].inventory_group: 1699756 → 20433868 ("Tiramisu" → "Tiramisu (*)")
VM2714:70     ✅ items[0].choices[19].inventory_group: 1699769 → 20433943 ("Tropical Trio" → "Tropical Trio (*)")
VM2714:70     ✅ items[0].choices[20].inventory_group: 1699767 → 20433955 ("Whisper Creek Pots De Creme" → "Whisper Creek Pots De Creme (*)")
VM2714:70     ✅ items[0].choices[21].inventory_group: 1699765 → 20433956 ("White Chocolate Mousse Cup" → "White Chocolate Mousse Cup (*)")
VM2714:70     ✅ items[0].choices[22].inventory_group: 1699809 → 20433935 ("Miniature Pecan Pie" → "Miniature Pecan Pie (*)")
VM2714:70     ✅ items[0].choices[23].inventory_group: 5498128 → 20433953 ("Vanilla Creme Brulee" → "Vanilla Creme Brulee (*)")
VM2714:70     ✅ items[0].choices[24].inventory_group: 5772055 → 20433967 ("Cheesecake" → "Cheesecake (*)")
VM2714:70     ✅ items[0].choices[25].inventory_group: 6693423 → 20433966 ("Mixed Berries & Cream" → "Mixed Berries & Cream (*)")
VM2714:70     ✅ items[0].choices[26].inventory_group: 5666723 → 20433973 ("Sorbet (vegan, gluten free)" → "Sorbet (vegan, gluten free) (*)")
VM2714:70     ✅ items[0].choices[27].inventory_group: 11979828 → 20433977 ("Apple Pie Slice" → "Apple Pie Slice (*)")
VM2714:70     ✅ items[0].choices[28].inventory_group: 1699766 → 20433933 ("Dark Chocolate Mousse" → "Dark Chocolate Mousse (*)")
VM2714:70     ✅ items[0].choices[29].inventory_group: 19814114 → 20433982 ("Irish Coffee Cake" → "Irish Coffee Cake (*)")
VM2714:70     ✅ items[0].choices[30].inventory_group: 20082733 → 20433981 ("Carrot Cake" → "Carrot Cake (*)")
VM2714:70     ✅ items[0].choices[31].inventory_group: 20085291 → 20433963 ("Key Lime Pie" → "Key Lime Pie (*)")
VM2714:70     ✅ items[0].choices[32].inventory_group: 20111221 → 20433868 ("Tiramisu" → "Tiramisu (*)")
VM2714:70     ✅ items[0].choices[33].inventory_group: 20111230 → 20433986 ("S'mores Cake" → "S'mores Cake (*)")
VM2714:70     ✅ items[0].choices[34].inventory_group: 20111519 → 20433919 ("Strawberry Shortcake" → "Strawberry Shortcake (*)")
VM2714:70     ✅ items[0].choices[35].inventory_group: 20111525 → 20433984 ("Pecan Pie Cake" → "Pecan Pie Cake (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Plated Desserts Choice List - 1 Selection  (*)" with deep reference changes
VM2714:50
--- Processing 1193/1257: "Banana Pudding (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1194/1257: "Roasted Red Pepper Potato Mash  (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1195/1257: "Southern Style Haricot Verts (*)" ---
VM2714:70     ✅ items[0].inventory_group: 6074062 → 20432589 ("grilled cipollini onions | bacon vinaigrette | sweety drop peppers" → "grilled cipollini onions | bacon vinaigrette | sweety drop peppers (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Southern Style Haricot Verts (*)" with deep reference changes
VM2714:50
--- Processing 1196/1257: "Sweet Potato Gnocchi (*)" ---
VM2714:70     ✅ items[0].inventory_group: 6073997 → 20432519 ("patty pan | sunburst squash | shiitake mushroom | red pepper romesco sauce" → "patty pan | sunburst squash | shiitake mushroom | red pepper romesco sauce (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Sweet Potato Gnocchi (*)" with deep reference changes
VM2714:50
--- Processing 1197/1257: "Chopped Beef Steak (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18620731 → 20432570 ("Tomato & mushroom demi" → "Tomato & mushroom demi (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Chopped Beef Steak (*)" with deep reference changes
VM2714:50
--- Processing 1198/1257: "Mushroom Ravioli (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18620740 → 20432571 ("Wild Mushroom | baby kale | pecorino romano | cream sauce" → "Wild Mushroom | baby kale | pecorino romano | cream sauce (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Mushroom Ravioli (*)" with deep reference changes
VM2714:50
--- Processing 1199/1257: "Spinach & Strawberry Salad (*)" ---
VM2714:70     ✅ items[0].inventory_group: 1782901 → 20434604 ("Dressing" → "Dressing (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Spinach & Strawberry Salad (*)" with deep reference changes
VM2714:50
--- Processing 1200/1257: "Gem Lettuce Salad (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18620480 → 20434627 ("Petite Honey Gem Lettuce | Frisee | Ricotta Salata | roasted sunflower seeds | Pomegranate Arils | snap peas | Saba | Lemon EVOO" → "Petite Honey Gem Lettuce | Frisee | Ricotta Salata | roasted sunflower seeds | Pomegranate Arils | snap peas | Saba | Lemon EVOO (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Gem Lettuce Salad (*)" with deep reference changes
VM2714:50
--- Processing 1201/1257: "Iceberg Salad (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18620476 → 20434624 ("Wedge of Iceberg | Green Goddess sour cream | pickled shallots | applewood bacon | Spiced Crispy Chickpeas | Grated Cured Egg yolk | Grape Tomato | Ranch" → "Wedge of Iceberg | Green Goddess sour cream | pickled shallots | applewood bacon | Spiced Crispy Chickpeas | Grated Cured Egg yolk | Grape Tomato | Ranch (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Iceberg Salad (*)" with deep reference changes
VM2714:50
--- Processing 1202/1257: "Caprese Salad* (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18894428 → 20434635 ("burrata pesto cream | herbed roasted tomatoes | basil | fresh mozzarella | baby arugula | balsamic glaze | EVOO" → "burrata pesto cream | herbed roasted tomatoes | basil | fresh mozzarella | baby arugula | balsamic glaze | EVOO (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Caprese Salad* (*)" with deep reference changes
VM2714:50
--- Processing 1203/1257: "Seared Scallops (*)" ---
VM2714:70     ✅ items[0].inventory_group: 5840314 → 20432590 ("truffle potato pureé" → "truffle potato pureé (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Seared Scallops (*)" with deep reference changes
VM2714:50
--- Processing 1204/1257: "Turkey & Roast Beef Sandwich (*)" ---
VM2714:70     ✅ items[0].inventory_group: 6218977 → 20434740 ("toasted sourdough | pepperjack | mesclun mix | red onion | sun dried tomato mayo" → "toasted sourdough | pepperjack | mesclun mix | red onion | sun dried tomato mayo (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Turkey & Roast Beef Sandwich (*)" with deep reference changes
VM2714:50
--- Processing 1205/1257: "Grilled Portabella Mushroom (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1206/1257: "Grilled Chicken Breast w/ Butternut Ravioli (*)" ---
VM2714:70     ✅ items[0].inventory_group: 8284496 → 20432533 ("grilled chicken breast | roasted butternut squash | shaved brussels | apple fennel veloute | sage brown butter" → "grilled chicken breast | roasted butternut squash | shaved brussels | apple fennel veloute | sage brown butter (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Grilled Chicken Breast w/ Butternut Ravioli (*)" with deep reference changes
VM2714:50
--- Processing 1207/1257: "Plated "A" Choice List DUO (*)" ---
VM2714:70     ✅ items[0].choices[0].inventory_group: 1697162 → 20434990 ("Blackberry Chicken" → "Blackberry Chicken (*)")
VM2714:70     ✅ items[0].choices[1].inventory_group: 1697166 → 20434947 ("Blackberry Pork Chop " → "Blackberry Pork Chop  (*)")
VM2714:70     ✅ items[0].choices[2].inventory_group: 1697167 → 20434977 ("Blackberry Pork Loin " → "Blackberry Pork Loin  (*)")
VM2714:70     ✅ items[0].choices[3].inventory_group: 1697171 → 20434943 ("Bourbon Glazed Salmon " → "Bourbon Glazed Salmon  (*)")
VM2714:70     ✅ items[0].choices[4].inventory_group: 1697114 → 20434956 ("Buttermilk Fried Chicken " → "Buttermilk Fried Chicken  (*)")
VM2714:70     ✅ items[0].choices[5].inventory_group: 1697141 → 20434959 ("Chicken Caponata " → "Chicken Caponata  (*)")
VM2714:70     ✅ items[0].choices[6].inventory_group: 1697143 → 20434952 ("Chicken Cordon Bleu " → "Chicken Cordon Bleu  (*)")
VM2714:70     ✅ items[0].choices[7].inventory_group: 1697140 → 20434999 ("Chicken Piccata" → "Chicken Piccata (*)")
VM2714:70     ✅ items[0].choices[8].inventory_group: 1697168 → 20434946 ("Cider Roasted Pork Loin " → "Cider Roasted Pork Loin  (*)")
VM2714:70     ✅ items[0].choices[9].inventory_group: 1697170 → 20434502 ("Créole Shrimp & Grits" → "Créole Shrimp & Grits (*)")
VM2714:70     ✅ items[0].choices[10].inventory_group: 1697161 → 20434951 ("Tomato Basil Chicken " → "Tomato Basil Chicken  (*)")
VM2714:70     ✅ items[0].choices[11].inventory_group: 5746378 → 20434964 ("Roasted Fennel & Rosemary Encrusted Salmon with tomato fondue" → "Roasted Fennel & Rosemary Encrusted Salmon with tomato fondue (*)")
VM2714:70     ✅ items[0].choices[12].inventory_group: 5305239 → 20434984 ("Lemon & Herb Salmon" → "Lemon & Herb Salmon (*)")
VM2714:70     ✅ items[0].choices[13].inventory_group: 1697111 → 20434992 ("Lemon & Herb Baked Chicken" → "Lemon & Herb Baked Chicken (*)")
VM2714:70     ✅ items[0].choices[14].inventory_group: 1697163 → 20434950 ("Herb Chicken " → "Herb Chicken  (*)")
VM2714:70     ✅ items[0].choices[15].inventory_group: 1697169 → 20434945 ("Herb Roasted Pork Loin " → "Herb Roasted Pork Loin  (*)")
VM2714:70     ✅ items[0].choices[16].inventory_group: 1697112 → 20434974 ("Maple Mustard Glazed Chicken " → "Maple Mustard Glazed Chicken  (*)")
VM2714:70     ✅ items[0].choices[17].inventory_group: 1697173 → 20435023 ("Smoked Brisket (gf)" → "Smoked Brisket (gf) (*)")
VM2714:70     ✅ items[0].choices[18].inventory_group: 1697139 → 20434954 ("Italian Chicken " → "Italian Chicken  (*)")
VM2714:70     ✅ items[0].choices[19].inventory_group: 1697113 → 20434957 ("Honey Balsamic Chicken " → "Honey Balsamic Chicken  (*)")
VM2714:70     ✅ items[0].choices[20].inventory_group: 1697164 → 20434949 ("Rosemary Maple Pork Chop " → "Rosemary Maple Pork Chop  (*)")
VM2714:70     ✅ items[0].choices[21].inventory_group: 8506693 → 20434982 ("Coriander Crusted Corvina" → "Coriander Crusted Corvina (*)")
VM2714:70     ✅ items[0].choices[22].inventory_group: 1697179 → 20434941 ("Rigatoni Primavera " → "Rigatoni Primavera  (*)")
VM2714:70     ✅ items[0].choices[23].inventory_group: 6010046 → 20435000 ("Butternut Ravioli" → "Butternut Ravioli (*)")
VM2714:70     ✅ items[0].choices[24].inventory_group: 18620547 → 20435029 ("Bacon Wrapped Chicken Roulade" → "Bacon Wrapped Chicken Roulade (*)")
VM2714:70     ✅ items[0].choices[25].inventory_group: 20110001 → 20435025 ("Sliced Petite Tender Beef" → "Sliced Petite Tender Beef (*)")
VM2714:70     ✅ items[0].choices[26].inventory_group: 18620549 → 20435031 ("Double Cooked Pork Belly" → "Double Cooked Pork Belly (*)")
VM2714:70     ✅ items[0].choices[27].inventory_group: 19167538 → 20434514 ("Maple Mustard Chicken" → "Maple Mustard Chicken (*)")
VM2714:70     ✅ items[0].choices[28].inventory_group: 18893880 → 20435032 ("Smoked Brisket*" → "Smoked Brisket* (*)")
VM2714:70     ✅ items[1].inventory_group: 1782966 → 20434108 ("1. Sides Choice List - 2 Selections " → "1. Sides Choice List - 2 Selections  (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Plated "A" Choice List DUO (*)" with deep reference changes
VM2714:50
--- Processing 1208/1257: "Platter(s) - Passed Hors d'Oeuvres  (*)" ---
VM2714:70     ✅ items[0].choices[11].inventory_group: 5629248 → 20433035 ("Platter - Wooden Egg Tray 14in" → "Platter - Wooden Egg Tray 14in (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Platter(s) - Passed Hors d'Oeuvres  (*)" with deep reference changes
VM2714:50
--- Processing 1209/1257: "Stations B - Music City Slider Station - Serviceware (*)" ---
VM2714:70     ✅ items[0].inventory_group: 1819285 → 20433292 ("Serving Spoon - Sauce " → "Serving Spoon - Sauce  (*)")
VM2714:70     ✅ items[1].inventory_group: 1819273 → 20433346 ("Tong - Paddle " → "Tong - Paddle  (*)")
VM2714:70     ✅ items[2].inventory_group: 1819275 → 20433288 ("Tong - Small " → "Tong - Small  (*)")
VM2714:70     ✅ items[3].inventory_group: 1819291 → 20433336 ("Flatware Sorter - Round " → "Flatware Sorter - Round  (*)")
VM2714:70     ✅ items[4].inventory_group: 2032560 → 20432993 ("Shelf - 3 Tier Bread Stand" → "Shelf - 3 Tier Bread Stand (*)")
VM2714:70     ✅ items[5].inventory_group: 2608067 → 20433411 ("3- Tier Round Wok Stand" → "3- Tier Round Wok Stand (*)")
VM2714:70     ✅ items[6].inventory_group: 2955232 → 20433299 ("Hanging Cradle for Wok Sterno - Long" → "Hanging Cradle for Wok Sterno - Long (*)")
VM2714:70     ✅ items[7].inventory_group: 2608068 → 20432956 ("Hanging Cradle for Wok Sterno - Tabletop" → "Hanging Cradle for Wok Sterno - Tabletop (*)")
VM2714:70     ✅ items[8].inventory_group: 2608066 → 20433412 ("Round Wok Stainless Steel" → "Round Wok Stainless Steel (*)")
VM2714:70     ✅ items[9].inventory_group: 1819361 → 20433001 ("Bowl - Oval Boat - Large White Melamine 16in " → "Bowl - Oval Boat - Large White Melamine 16in  (*)")
VM2714:70     ✅ items[10].inventory_group: 1819367 → 20433281 ("Bowl - Round Melamine White 6in " → "Bowl - Round Melamine White 6in  (*)")
VM2714:70     ✅ items[11].inventory_group: 2038473 → 20433142 ("Ladle - Sauce" → "Ladle - Sauce (*)")
VM2714:74     ⚠️ items[12].inventory_group: 2032458 ("Wine Crate - Dark Wood") - no (*) version found
VM2714:70     ✅ items[13].inventory_group: 1818261 → 20433344 ("3-Bulb Heat Lamp " → "3-Bulb Heat Lamp  (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Stations B - Music City Slider Station - Serviceware (*)" with deep reference changes
VM2714:50
--- Processing 1210/1257: "Miller Lite (*)" ---
VM2714:74     ⚠️ items[0].inventory_group: 5820515 ("DET") - no (*) version found
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1211/1257: "Cabernet (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1212/1257: "Chardonnay (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1213/1257: "Classic Tomato Bruschetta (vegetarian) (*)" ---
VM2714:70     ✅ items[0].inventory_group: 5743946 → 20432638 ("baguette | garlic | basil | balsamic reduction" → "baguette | garlic | basil | balsamic reduction (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Classic Tomato Bruschetta (vegetarian) (*)" with deep reference changes
VM2714:50
--- Processing 1214/1257: "Limoncello compressed Cantaloupe (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18620172 → 20432800 ("basil crystals | hawaiian salt" → "basil crystals | hawaiian salt (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Limoncello compressed Cantaloupe (*)" with deep reference changes
VM2714:50
--- Processing 1215/1257: "Fried Hearts of Palm (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18620165 → 20432805 ("panko crusted | lemon vermouth aioli | micro greens" → "panko crusted | lemon vermouth aioli | micro greens (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Fried Hearts of Palm (*)" with deep reference changes
VM2714:50
--- Processing 1216/1257: "Shrimp Cocktail* (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18894313 → 20432821 ("poached jumbo shrimp | horseradish + tomato sauce" → "poached jumbo shrimp | horseradish + tomato sauce (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Shrimp Cocktail* (*)" with deep reference changes
VM2714:50
--- Processing 1217/1257: "Fried Mac & Cheese (*)" ---
VM2714:70     ✅ items[0].inventory_group: 19168359 → 20432825 ("parmesan crust | ditalini | four cheese | pickled sweet peppers" → "parmesan crust | ditalini | four cheese | pickled sweet peppers (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Fried Mac & Cheese (*)" with deep reference changes
VM2714:50
--- Processing 1218/1257: "Southern Fried Green Tomato (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18893780 → 20432817 ("parmesan crust | pimento cheese | remoulade" → "parmesan crust | pimento cheese | remoulade (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Southern Fried Green Tomato (*)" with deep reference changes
VM2714:50
--- Processing 1219/1257: "Sweet Pepper Hummus (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18894213 → 20432819 ("sweet pepper | hummus | peruvian pepper | pomegranate molasses | cucumber" → "sweet pepper | hummus | peruvian pepper | pomegranate molasses | cucumber (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Sweet Pepper Hummus (*)" with deep reference changes
VM2714:50
--- Processing 1220/1257: "Spiced Rum Compressed Pineapple (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18620173 → 20432801 ("tajin | hot honey" → "tajin | hot honey (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Spiced Rum Compressed Pineapple (*)" with deep reference changes
VM2714:50
--- Processing 1221/1257: "Beef Sausage Puff (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18893783 → 20432790 ("puff pastry | sesame seeds | comeback sauce" → "puff pastry | sesame seeds | comeback sauce (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Beef Sausage Puff (*)" with deep reference changes
VM2714:50
--- Processing 1222/1257: "Savory Cannoli (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18620169 → 20432809 ("ricotta salata filling | pistachio crumble | crispy prosciutto | basil" → "ricotta salata filling | pistachio crumble | crispy prosciutto | basil (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Savory Cannoli (*)" with deep reference changes
VM2714:50
--- Processing 1223/1257: "Ahi Tuna* (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18893788 → 20432791 ("sesame crusted ahi tuna | wonton shell | lemongrass XO" → "sesame crusted ahi tuna | wonton shell | lemongrass XO (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Ahi Tuna* (*)" with deep reference changes
VM2714:50
--- Processing 1224/1257: "Candied Bacon (GF) (*)" ---
VM2714:70     ✅ items[0].inventory_group: 5743862 → 20432618 ("carmelized sugar | applewood smoked bacon" → "carmelized sugar | applewood smoked bacon (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Candied Bacon (GF) (*)" with deep reference changes
VM2714:50
--- Processing 1225/1257: "Beet Pickled Deviled Egg (*)" ---
VM2714:70     ✅ items[0].inventory_group: 20022692 → 20432831 ("pickled mustard seeds | chive" → "pickled mustard seeds | chive (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Beet Pickled Deviled Egg (*)" with deep reference changes
VM2714:50
--- Processing 1226/1257: "Stuffed Peppadew (*)" ---
VM2714:70     ✅ items[0].inventory_group: 18620396 → 20432813 ("herbed roasted garlic cream cheese | crispy onions | peach coulis" → "herbed roasted garlic cream cheese | crispy onions | peach coulis (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Stuffed Peppadew (*)" with deep reference changes
VM2714:50
--- Processing 1227/1257: "Jamaican Chicken Crisp* (*)" ---
VM2714:70     ✅ items[0].inventory_group: 5743753 → 20432827 ("fried plantain chip | jerk spiced chicken salad | charred pineapple | curried walnut crumble" → "fried plantain chip | jerk spiced chicken salad | charred pineapple | curried walnut crumble (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Jamaican Chicken Crisp* (*)" with deep reference changes
VM2714:50
--- Processing 1228/1257: "Mimosa Bar (*)" ---
VM2714:70     ✅ items[0].inventory_group: 2036354 → 20433205 ("Glassware- Champagne Flute " → "Glassware- Champagne Flute  (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Mimosa Bar (*)" with deep reference changes
VM2714:50
--- Processing 1229/1257: "testing 1.25.22 (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1230/1257: "Rush Fee - Heating/AC (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1231/1257: "Late Fee for Tent Request (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1232/1257: "Bananas Foster Station (*)" ---
VM2714:70     ✅ items[0].inventory_group: 5739456 → 20433582 ("Chef Manned Action Station" → "Chef Manned Action Station (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Bananas Foster Station (*)" with deep reference changes
VM2714:50
--- Processing 1233/1257: "Cumberland & Observatory (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1234/1257: "Cumberland & Dyer (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1235/1257: "Observatory  (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1236/1257: "The Bell Tower Board Room (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1237/1257: "Main Patio Tent  (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1238/1257: "The Bridge Building Office  (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1239/1257: "The Bell Tower Private Tasting Room  (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1240/1257: "Cumberland  (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1241/1257: "Riverfront Terrace (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1242/1257: "Dyer - 2025 (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1243/1257: "Observatory  - 2025 (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1244/1257: "Infinity - 2025 (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1245/1257: "The Bridge Building Office  - 2025 (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1246/1257: "The Bell Tower Private Tasting Room  - 2025 (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1247/1257: "Cumberland  - 2025 (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1248/1257: "Riverfront Terrace - 2025 (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1249/1257: "Cake Stand - White - Round - With Lip - Small (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1250/1257: "Cake Stand - White - Round - With Lip - Medium (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1251/1257: "Votive Holder - Gold Mercury Glass (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1252/1257: "LED's--Tealights bag of 50 (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1253/1257: "Easels - Black Collapsible (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1254/1257: "Daily use of the Mansion from 9:00AM to 12:00AM on an event day (*)" ---
VM2714:91 Changes needed: NO
VM2714:50
--- Processing 1255/1257: "Martini Bar (*)" ---
VM2714:70     ✅ items[0].inventory_group: 2036338 → 20433319 ("Glassware-Martini Glass Stemless " → "Glassware-Martini Glass Stemless  (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Martini Bar (*)" with deep reference changes
VM2714:50
--- Processing 1256/1257: "Mules of the World (*)" ---
VM2714:70     ✅ items[0].inventory_group: 12056121 → 20436245 ("ginger beer | mint sprigs | crystalized ginger | bamboo picks | copper mugs" → "ginger beer | mint sprigs | crystalized ginger | bamboo picks | copper mugs (*)")
VM2714:91 Changes needed: YES
VM2714:107 [DRY RUN] Would update "Mules of the World (*)" with deep reference changes
VM2714:50
--- Processing 1257/1257: "Bourbon Tasting Bar (*)" ---
VM2714:74     ⚠️ items[0].inventory_group: 2036359 (" Glassware- Shot Glass ") - no (*) version found
VM2714:91 Changes needed: NO
VM2714:44
========== DEEP UPDATE COMPLETE ==========
