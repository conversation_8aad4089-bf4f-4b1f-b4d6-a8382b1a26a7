<?php

define("PBKDF2_HASH_ALGORITHM", "sha256");
define("PBKDF2_ITERATIONS", 2000);
define("PBKDF2_SALT_BYTE_SIZE", 24);
define("PBKDF2_HASH_BYTE_SIZE", 24);
define("HASH_SECTIONS", 4);
define("HASH_ALGORITHM_INDEX", 0);
define("HASH_ITERATION_INDEX", 1);
define("HASH_SALT_INDEX", 2);
define("HASH_PBKDF2_INDEX", 3);

// requires dbConn, objects, communications, stripe
date_default_timezone_set('America/Chicago');

require_once("_excel.php");

class App
{

	public $fromEmailAddress = '<EMAIL>';
	private $easyPDFCloudClientID = '885720ce6bbb49c0b5d85bf8f9fabd37';
	private $easyPDFCloudSecretKey = 'C56A8C06188E86C196136AB8C598A50A8754E82CFAB55A02BC462384BC78AB74';
	public $stripeSecretKey = null;
	private $zapierSecretKey = '8c22f0be7ad5eed56788a3a09dbaf8365d173addf11c1cb5126eef408783d05e';
	private $zapierClientId = '80e64005a8815784d011fb7dc5845149';
	public $stripeProcessingFee = .00;
	public $filesBucketUrl = 'https://pagoda.nyc3.digitaloceanspaces.com/_instances';

	public function getUrl()
	{
		$url = ($_SERVER['HTTPS'] && $_SERVER['HTTPS'] != 'off') ? 'https://' . $_SERVER['HTTP_HOST'] : 'http://' . $_SERVER['HTTP_HOST'];
		return $url;
	}

	function __construct($dbConn, $objects, $comms, $stripe, $inventory, $pgObjects, $cookies, $filesApi, $instance = null)
	{

		if ($appConfig['systemName']) {
			$nrAppName = $appConfig['systemName'] . ';Pagoda';
		} else {
			$nrAppName = 'Pagoda';
		}

		if (extension_loaded('newrelic')) {
			newrelic_set_appname($nrAppName);
		}

		/*
		$instance = $this->pgObjects->where('instances', array('instance' => $_REQUEST['instance']))[0];

		if ( isset($instance['stripe_account_id']) === true && $instance['stripe_account_id'] !== '') {

			$stripeSecretKey =  $instance['stripe_account_id'];

		}
*/

		$this->stripeSecretKey = getenv('STRIPE_SK');
		$this->db = $dbConn;
		$this->obj = $objects;
		$this->comm = $comms;
		$this->inventory = $inventory;
		$this->pgObjects = $pgObjects;
		$this->cookies = $cookies;
		$this->files = $filesApi;
		$this->instance = $instance;
	}

	public function sendData($data, $json)
	{

		if ($GLOBALS['speed-test'] === true) {
			// $data['memory_get_peak_usage'] = memory_get_peak_usage(true);

			echo ('END: ' . (microtime(true) - $GLOBALS['req-start-time']) . 's<br />'
			);
			die();
		}

		if ($json === 1) {

			header("Content-Type: application/json");
			header('Content-Encoding: gzip');

			// If messages for the user exist, add them to the response
			if (
				!empty($GLOBALS)
				&& array_key_exists('_resp', $GLOBALS)
				&& array_key_exists('_msg', $GLOBALS['_resp'])
			) {

				if (
					!empty($data)
					&& !empty($data['msg'])
				) {

					if (!is_array($data['msg']['messages'])) {
						$data['msg']['messages'] = [];
					}

					$data['msg']['messages'] = array_merge(
						$data['msg'],
						$GLOBALS['_resp']['_msg']['messages']
					);
				} else {

					$data = [
						'msg' => 			[
							'messages' => 	$GLOBALS['_resp']['_msg']
						], 'response' => 	$data, '_notify_usr' => 	true
					];
				}
			}

			$ret =  json_encode($data);

			$this->pgObjects->closeDBConnection();
			echo gzencode($ret);
			die();
		} else {
			return $data;
		}
	}

	public function test($offset = 300)
	{

		error_reporting(E_ALL);
		ini_set('display_errors', '1');

		//$this->comm->createMailchimpCompany('thelifebook', 'The Life Book', '5983ed1a31', 'https://thelifebook.com', '<EMAIL>', 'USD');
		//$this->comm->createMailchimpProduct('thelifebook', '3020', 'Mark');

		//$delete = $this->comm->mailchimp->delete("ecommerce/stores/thelifebook/customers/1234567");
		//$result = $this->comm->mailchimp->patch("ecommerce/stores/thelifebook/", array('is_syncing'=>false));

		//var_dump($result);
		function date_compare($a, $b)
		{
			$t1 = strtotime($a['date_created']);
			$t2 = strtotime($b['date_created']);
			return $t1 - $t2;
		}

		// $res = $this->comm->mailchimpAPI('post', 'automations/76f057a66d/emails/ddfdce533b/queue/', [
		// 	'email_address' => '<EMAIL>'
		// ]);

		var_dump($res);

		/*
		function loop($scope, $pageLength = 50, $offset = 0, $limit = 1, $count = 0, $members = array()){

			$contacts = $scope->pgObjects->getAll('contacts', 3, true, $offset, 'date_created', 'desc', $pageLength, 'string');

			if(count($contacts) == 0){

				return $members;

			}else{

				$count++;

				foreach($contacts as $contact){

					$requests = $scope->getObjectsWhere('requests', array('contact'=>$contact['id']), false, 0);

					usort($requests, 'date_compare');

					$approved = 'No';
					$shipped = 'No';

					if($requests[0]['status'] == 'Shipped'){

						$approved = 'Yes';
						$shipped = 'Yes';

					}

					unset($memberObj);

					// these should be removed before submitting to Mail Chimp
					$memberObj['id'] = $contact['id'];
					$memberObj['company'] = $contact['company']['name'];
					$memberObj['orders_count'] = count($requests);
					$memberObj['total_spent'] = __::chain($requests)->pluck('books')->reduce(function($memo, $num){ return $memo + $num; }, 0)->value();
					$memberObj['requests'] = $requests;

					$memberObj['status'] = 'subscribed';
					$memberObj['merge_fields']['FNAME'] = $contact['fname'];
					$memberObj['merge_fields']['LNAME'] = $contact['lname'];
					$memberObj['merge_fields']['SHIPPED'] = $shipped;
					$memberObj['merge_fields']['APPROVED'] = $approved;
					$memberObj['merge_fields']['TYPE'] = $contact['type']['name'];
					$memberObj['merge_fields']['REQUESTS'] = count($requests);
					$memberObj['merge_fields']['MANAGER'] = $contact['manager']['fname'].' '.$contact['manager']['lname'];
					$memberObj['merge_fields']['RECENT'] = date('Y-m-d h:i:s', strtotime($requests[0]['date_created']));
					$memberObj['merge_fields']['RECENTSIZE'] = $requests[0]['books'];

					foreach($contact['contact_info'] as $info){

						if($info['is_primary'] == 'yes'){

							switch($info['type']['data_type']){

								case 'other':

									switch($info['title']){

										case 'Adults':

											$memberObj['merge_fields']['ADULTS'] = $info['info'];

											break;

										case 'Students':

											$memberObj['merge_fields']['STUDENTS'] = $info['info'];

											break;

										case 'Denomination':

											$memberObj['merge_fields']['DENOM'] = $info['info'];

											break;

									}

									break;

								case 'email':

									$memberObj['email_address'] = $info['info'];

									break;

								case 'phone':

									if($info['title'] == 'Church Phone'){

										$memberObj['merge_fields']['CHURCHPHON'] = $info['info'];

									}else{

										$memberObj['merge_fields']['PHONE'] = $info['info'];

									}

									break;

								case 'address':

									if($info['title'] == 'Church Address'){

										$memberObj['merge_fields']['CHURCHADDR']['addr1'] = $info['street'];
										$memberObj['merge_fields']['CHURCHADDR']['addr2'] = '';
										$memberObj['merge_fields']['CHURCHADDR']['city'] = $info['city'];
										$memberObj['merge_fields']['CHURCHADDR']['state'] = $info['state'];
										$memberObj['merge_fields']['CHURCHADDR']['zip'] = $info['zip'];
										$memberObj['merge_fields']['ZIP'] = $info['zip'];

									}else{

										$memberObj['merge_fields']['SHIPPING']['addr1'] = $info['street'];
										$memberObj['merge_fields']['SHIPPING']['addr2'] = '';
										$memberObj['merge_fields']['SHIPPING']['city'] = $info['city'];
										$memberObj['merge_fields']['SHIPPING']['state'] = $info['state'];
										$memberObj['merge_fields']['SHIPPING']['zip'] = $info['zip'];

									}

									break;

							}

						}

					}

					if($memberObj){

						array_push($members, $memberObj);

					}

				}

				if($limit != 0 and $limit <= $count){

					return $members;

				}else{

					$offset = $offset + $pageLength;

					return loop($scope, $pageLength, $offset, $limit, $count, $members);

				}

			}

		}

		function uploadLoop($scope, $offset = 0){

			$members = loop($scope, 100, $offset, 2);

			echo 'CREATING '. count($members) . ' LIST MEMBERS<br />';

			$customers = [];
			$orders = [];

			foreach($members as $member){

				$customer['id'] = (string) $member['id'];
				$customer['email_address'] = $member['email_address'];
				$customer['opt_in_status'] = true;
				$customer['company'] = $member['company'];
				$customer['first_name'] = $member['merge_fields']['FNAME'];
				$customer['last_name'] = $member['merge_fields']['LNAME'];
				$customer['orders_count'] = $member['orders_count'];
				$customer['total_spent'] = $member['total_spent'];
				$customer['address'] = array(
					'address1' => $member['merge_fields']['CHURCHADDR']['addr1'],
					'address2' => $member['merge_fields']['CHURCHADDR']['addr2'],
					'city' => $member['merge_fields']['CHURCHADDR']['city'],
					'province' => $member['merge_fields']['CHURCHADDR']['state'],
					'province_code' => $member['merge_fields']['CHURCHADDR']['state'],
					'postal_code' => $member['merge_fields']['CHURCHADDR']['zip']
				);

				array_push($customers, $customer);

				foreach($member['requests'] as $req){

					$bookVersion = '3010';
					if($req['book_version']){
						$bookVersion = (string) $req['book_version'];
					}

					$order['id'] = (string) $req['id'];
					$order['customer'] = array(
						'id' => (string) $member['id']
					);
					$order['currency_code'] = 'USD';
					$order['order_total'] = $req['books'];
					$order['lines'] = array(
						array(
							'id' => (string) $req['id'],
							'product_id' => $bookVersion,
							'product_variant_id' => $bookVersion,
							'quantity' => $req['books'],
							'price' => 1
						)
					);

				}

				array_push($orders, $order);

			}

			$mcBatch = $scope->comm->mailchimp->new_batch();

			$count = 0;

			foreach($members as $member){

				unset($member['id']);
				unset($member['company']);
				unset($member['orders_count']);
				unset($member['total_spent']);
				unset($member['requests']);

				$mcBatch->post('op'.$count, "lists/5983ed1a31/members", $member);

				$count++;

			}

			foreach($customers as $customer){

				$mcBatch->post('op'.$count, "ecommerce/stores/thelifebook/customers", $customer);

				$count++;

			}

			foreach($orders as $order){

				$mcBatch->post('op'.$count, "ecommerce/stores/thelifebook/orders", $order);

				$count++;

			}

			return $mcBatch->execute();

		}
*/

		/*
		$res = $this->comm->checkMailchimpAPIKey();

		print_r($res);
*/

		/*
		$statusCheck = $this->comm->mailchimp->new_batch('7fbdcb1daf');

var_dump($statusCheck);

		$res = $statusCheck->check_status();

var_dump($res);
*/


		//var_dump($customers);
		//var_dump($members[0]['requests'][0]);
		//var_dump($orders[0]);

		/*
		$retMember = $this->comm->mailchimp->post('/lists/5983ed1a31/members', $members[2]);
		$retCustomer = $this->comm->mailchimp->post('/ecommerce/stores/thelifebook/customers', $customers[2]);
		$retOrder = $this->comm->mailchimp->post('/ecommerce/stores/thelifebook/orders', $orders[2]);
*/

		/*
		var_dump($retMember);
		var_dump($retCustomer);
		var_dump($retOrder);
*/


		//$res = $this->pgObjects->getAll('contacts', 3, true, 0, 'date_created', 'desc', 10, 'string');
		/*

		// Turn off output buffering
		ini_set('output_buffering', 'off');
		// Turn off PHP output compression
		ini_set('zlib.output_compression', false);

		//Flush (send) the output buffer and turn off output buffering
		//ob_end_flush();
		while (@ob_end_flush());

		// Implicitly flush the buffer(s)
		ini_set('implicit_flush', true);
		ob_implicit_flush(true);

		//prevent apache from buffering it for deflate/gzip
		header("Content-type: text/plain");
		header('Cache-Control: no-cache'); // recommended to prevent caching of event data.

		for($i = 0; $i < 1000; $i++)
		{
		echo ' ';
		}

		ob_flush();
		flush();

		/// Now start the program output

		$total = 46258;
		$offset = 0;

		while($offset < $total){

			$res = uploadLoop($this, $offset);

			$offset = $offset + 200;

			var_dump($res);

			ob_flush();
			flush();

		}

*/

		//var_dump($res);

		//echo '<br /><br />CURRENT STACK SIZE: '. count($members);

		/*
		if(count($members) == 200){

			$offset = $offset + 200;

			return $this->test($offset);

		}else{

			return;

		}
*/


		/*
		sleep(10);

		$statusCheck = $this->comm->mailchimp->new_batch($res['id']);
		$status = $statusCheck->check_status();

		var_dump($status);
*/
	}

	public function staffToUsers()
	{

		// echo 'test';
		// die();
		$page = 0;
		$batchSize = 200;

		// get all staff
		$staffList = $this->pgObjects->getAll('staff', 0, true, $page * $batchSize, 'date_created', 'asc', $batchSize, 'string', true);
		echo count($staffList) . ' staff objs loaded.<br />';

		// get users linked to these staff
		$currentLinkedUsers = $this->pgObjects->getAll('users');
		echo count($currentLinkedUsers) . ' linked users detected.<br />';

		$usedEmails = __::pluck($currentLinkedUsers, 'email');

		// get staff paperwork linked to these staff
		$staffPaperWork = $this->pgObjects->where(
			'staff_paperwork',
			[
				'staff' => [
					'type' => 'or',
					'values' => __::pluck($staffList, 'id')
				]
			]
		);
		echo count($staffPaperWork) . ' linked paperwork documents detected.<br />';

		$usersBlueprint = $this->pgObjects->getBlueprint('users');

		$toUpdate = [];
		$linkedUsersToUpdate = [];
		$docsToUpdate = [];

		// sort into staff that need to be changed into user vs. staff that need to update the user obj
		if (is_array($staffList)) {

			foreach ($staffList as $i => $staff) {

				$linkedUser = false;
				if (intval($staff['user']) > 0) {

					$linkedUser = __::find($currentLinkedUsers, function ($user) use ($staff) {

						return intval($user['id']) === intval($staff['user']);
					});
				}

				if (intval($staff['related_object']) > 0) {

					if (empty($linkedUser)) {

						__::find($currentLinkedUsers, function ($user) use ($staff) {

							return intval($user['id']) === intval($staff['related_object']);
						});
					}
				}

				if (intval($staff['parent']) > 0) {

					if (empty($linkedUser)) {

						__::find($currentLinkedUsers, function ($user) use ($staff) {

							return intval($user['id']) === intval($staff['parent']);
						});
					}
				}

				// if updating the already linked user
				if ($linkedUser) {

					echo 'linked user exists! ' . $linkedUser['fname'] . '<br />';

					$temp = [];

					foreach ($usersBlueprint as $key => $propertyDef) {

						if (
							$linkedUser[$key] == null && $key !== 'password' && $key !== 'id'
						) {
							$temp[$key] = $staff[$key];
							// 							echo 'TAKEN FROM STAFF: '. $key .'<br />';
						} else {
							// 							echo 'MAINTAINED: '. $key .'<br />';
						}
					}

					$temp['id'] = $linkedUser['id'];
					$temp['nick_name'] = $staff['nickname'];

					$temp['data_source'] = 920421;
					$temp['data_source_id'] = $staff['id'];

					if (__::isNull($linkedUser['type'])) {
						$linkedUser['type'] = 'staff';
					}

					array_push($linkedUsersToUpdate, $temp);

					// update staff paperwork objs to reflect the user id instead of staff id
					if (is_array($staffPaperWork)) {

						foreach ($staffPaperWork as $j => $doc) {

							if ($doc['staff'] === $staff['id']) {

								array_push($docsToUpdate, [
									'id' => $doc['id'],
									'staff' => $linkedUser['id']
								]);

								echo 'document linked!<br />';
							}
						}
					}


					// if changing into a user
				} else {

					$emailToUse = $staff['email'];
					if (__::includ($usedEmails, $emailToUse)) {
						$emailToUse = '';
					} else {
						array_push($usedEmails, $emailToUse);
					}

					// change nickname to nick_name
					// set enabled to 0
					// set type to 'staff';
					// $this->pgObjects->changeObjectType($staff, 'users');
					$temp = [
						'id' => $staff['id'],
						'nick_name' => $staff['nickname'],
						'type' => 'staff',
						'data_source' => 920421,
						'data_source_id' => $staff['id'],
						'email' => $emailToUse,
						'enabled' => 0
					];

					array_push($toUpdate, $temp);
				}
			}
		}

		// 		echo '--- staff to turn into users: ---<br />';
		//		var_dump($toUpdate);
		// 		die();

		// 		echo '--- users to update w/staff info: ---<br />';
		// 		var_dump($linkedUsersToUpdate);
		// 		die();

		//		echo '--- staff paperwork updates: ---<br />';
		//		var_dump($docsToUpdate);

		//		echo '--- used emails: ---<br />';
		//		var_dump($usedEmails);
		die();

		// update batch
		if (is_array($toUpdate)) {

			// 			$this->pgObjects->changeObjectType(__::pluck($toUpdate, 'id'), 'users');

			foreach ($toUpdate as $i => $update) {
				$this->pgObjects->changeObjectType($update['id'], 'users');
			}

			$this->pgObjects->update('users', $toUpdate);
			echo count($toUpdate) . ' staff objs turned into users.<br />';
		}

		if (is_array($linkedUsersToUpdate)) {

			$this->pgObjects->update('users', $linkedUsersToUpdate);
			$this->pgObjects->update('staff_paperwork', $docsToUpdate);

			echo count($linkedUsersToUpdate) . ' users updated with their staff info.';
		}

		return true;
	}

	public function tlbtest()
	{

		function getLoop($scope, $offset, $pageLength, $loops = 0, $count = 0, $ret = null)
		{

			if ($ret === null) {
				$ret = array();
			}

			$contacts = $scope->pgObjects->getAll('contacts', 0, true, $offset, 'date_created', 'desc', $pageLength, 'string');

			if (count($contacts) > 0) {

				$requests = $scope->pgObjects->where('requests', array(
					'contact' => $contacts[0]['id']
				));

				foreach ($requests as $req) {

					$scope->pgObjects->update('requests', array('id' => $req['id'], 'manager' => $contacts[0]['manager']));
				}

				$ret = array_merge($ret, $requests);

				$count++;

				if ($count >= $loops and $loops != 0) {

					return $ret;
				} else {

					$offset = $offset + $pageLength;

					return getLoop($scope, $offset, $pageLength, $loops, $count, $ret);
				}
			} else {

				return false;
			}
		}

		$contacts = getLoop($this, 0, 1);

		//var_dump($contacts);

	}

	public function addAccountToConnectAccount($accountObj = null, $accountId = null, $json = 1)
	{

		//error_reporting(E_ALL);
		//ini_set('display_errors', '1');

		if ($accountObj == null && $_POST->accountObj) {
			$accountObj = $_POST->accountObj;
		} else {
			return false;
		}

		if ($accountId == null && $_POST->accountId) {
			$accountId = $_POST->accountId;
		} else {
			return false;
		}

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		$account = \Stripe\Account::retrieve($accountId);

		$account->external_accounts->create(array("external_account" => json_decode(json_encode($accountObj), true)));
		//$account->external_account = json_decode(json_encode($accountObj), true);

		$account->save();

		return $this->sendData($account, $json);
	}

	public function addStripeSourceToCustomer($token = null, $customer = null, $json = 1)
	{

		if ($token == null) {
			$token = $_POST->token;
		}

		if ($customer == null) {
			$customer = $_POST->customer;
		}

		if (!$_REQUEST['pagodaAPIKey']) {
			$apiKey = $_POST->pagodaAPIKey;
			$_REQUEST['pagodaAPIKey'] = $apiKey;
		} else {
			$apiKey = $_REQUEST['pagodaAPIKey'];
		}

		$instance = $this->pgObjects->where('instances', array('instance' => $apiKey))[0];

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		$cu = \Stripe\Customer::retrieve(
			$customer,
			["stripe_account" => $instance['stripe_account_id']]
		);

		$cu->sources->create(array("source" => $token->id));
		$cu->default_source = $token->id;

		return $this->sendData($cu, $json);
	}

	public function addTagToObject($tagObj = null, $json = 1)
	{

		$ret = null;

		if ($tagObj == null) {

			if ($_POST) {

				$type = $_POST->type;
				$typeId = $_POST->typeId;
				$tag = $_POST->tag;
				$tagColor = $_POST->color;
			} else {

				$type = 'church';
				$typeId = 90000;
				$tag = 'testing';
			}
		} else {

			$type = $tagObj['type'];
			$typeId = $tagObj['type_id'];
			$tag = $tagObj['tag'];
			$tagColor = $tagColor['color'];
		}

		if (
			is_numeric($_POST->tagId)
			&& intval($_POST->tagId) > 0
		) {

			if (
				$type === 'contacts'
				&& intval($typeId) > 0
			) {

				$ret = $this->pgObjects->tagObject(
					$typeId,
					intval($_POST->tagId),
					'shared_with',
					true
				);

				return $this->sendData($ret, $json);
			} elseif (intval($typeId) > 0) {

				$ret = $this->pgObjects->tagObject(
					$typeId,
					intval($_POST->tagId),
					'tagged_with',
					true
				);

				return $this->sendData($ret, $json);
			} else {

				$systemTag = $this->pgObjects->getById('', array(
					'id' => $_POST->tagId
				));

				return $this->sendData($systemTag[0], $json);
			}
		}

		$systemTag = $this->pgObjects->where('system_tags', array(
			'tag' => $tag
		));

		if (count($systemTag) == 0) {

			$systemTag = $this->createNewObject('system_tags', array('tag' => $tag, 'color' => $tagColor), 0);

			if (intval($typeId) > 0) {

				$ret = $this->pgObjects->tagObject(
					$typeId,
					$systemTag['id'],
					'tagged_with',
					true
				);
			} else {
				$ret = $systemTag;
			}
		} else {

			if (intval($typeId) > 0) {
				$ret = $this->pgObjects->tagObject($typeId, $systemTag[0]['id']);
			} else {
				$ret = $systemTag;
			}
		}

		return $this->sendData($ret, $json);
	}

	public function balanceProjectInvoices($projectId = null, $json = 1)
	{

		if (!$projectId) {
			$projectId = $_POST->projectId;
		}

		if (!$projectId) {
			return;
		}

		$project = $this->pgObjects->getById('groups', $projectId);
		$proposal = $this->pgObjects->getByid('proposals', $project['proposal']);
		$projectPricing = $this->pgObjects->where('inventory_menu_pricing_breakdown', array('space' => $projectId))[0];

		// Getting the invoices and comparing the total of the project to the total of the total of the invoices.amount property.
		// If the two are in balance, we will return the invoices and exit.
		$invoices = $this->pgObjects->where('invoices', array('related_object' => $proposal['id']));

		// Infinity/Event Management specific check to be used for new invoices:
		$isInfinityEventManagement = (($project["instance"] === "infinity" && $project["type"] === 1625566) ? true : false);

		if ($projectPricing['breakdown']['total'] !== 0 && $projectPricing['breakdown']['total'] > 0) {

			$totalPrice = $projectPricing['breakdown']['total'];
		} else {

			$totalPrice = $projectPricing['breakdown']['subTotal'];
		}

		$runningBalanceInt = $totalPrice;
		$invoiceTotals = 0;

		// Add up all of the invoices
		foreach ($invoices as $key => $invoice) {
			$invoiceTotals += $invoice['amount'];
		}

		$diff = $totalPrice - $invoiceTotals;

		if ($diff === 0 and !$proposal['invoice_template']) {
		}

		if ($proposal['invoice_template']) { // payment template selected

			$invoiceTemplate = $this->pgObjects->getById('payment_schedule_template', $proposal['invoice_template']);

			// We need to delete the invoices if the total amount of invoices do not match the amount of invoices in the template.
			if (count($invoiceTemplate['templates']) != count($invoices) && count($invoices) > 0) {

				foreach ($invoices as $inv) {

					$this->pgObjects->delete('invoices', $inv['id']);
				}

				$invoices = [];
			}

			$runningBalance = $projectPricing['breakdown']['chartOfAccts'];

			foreach ($projectPricing['breakdown']['vendorChartOfAccts'] as $coaArray) {

				foreach ($coaArray as $coaId => $value) {

					$runningBalance[$coaId] += $value;
				}
			}

			$templateAdjustments = [];

			usort($invoiceTemplate['templates'], function ($a, $b) {

				if ($a['payment_type'] == $b['payment_type']) {
					return 0;
				}

				return $a['payment_type'] > $b['payment_type'];
			});

			foreach ($invoiceTemplate['templates'] as $templateInvoice) {

				$invoiceTemplateTotal = 0;
				$templateAdjustments[$templateInvoice['id']] = 0;

				foreach ($templateInvoice['chart_of_accounts'] as $categoryId) {

					$invoiceTemplateTotal += $runningBalance[$categoryId];
				}

				switch ($templateInvoice['payment_type']) {

					case 'flatRate':

						$templateAdjustments[$templateInvoice['id']] += $templateInvoice['flat_rate'];
						$runningBalance[$categoryId] -= $templateAdjustments[$templateInvoice['id']];
						$runningBalanceInt -= $templateInvoice['flat_rate'];

						break;

					case 'percentOfTotal':

						$templateAdjustments[$templateInvoice['id']] += round($invoiceTemplateTotal * ($templateInvoice['percent_of_total'] / 100));
						$runningBalance[$categoryId] -= $templateAdjustments[$templateInvoice['id']];
						$runningBalanceInt -= $templateAdjustments[$templateInvoice['id']];

						break;

					case 'remainingBalance':

						$templateAdjustments[$templateInvoice['id']] = 'working';

						break;
				}
			}

			// Now we need to calculate and apply the remainingBalance payment_type templates.
			foreach ($invoiceTemplate['templates'] as $templateInvoice) {

				switch ($templateInvoice['payment_type']) {

					case 'remainingBalance':

						$invoiceTemplateTotal = 0;
						foreach ($templateInvoice['chart_of_accounts'] as $categoryId) {

							$invoiceTemplateTotal += $runningBalance[$categoryId];
							$runningBalance[$categoryId] = 0;
						}

						if ($templateAdjustments[$templateInvoice['id']] == 'working') {
							$templateAdjustments[$templateInvoice['id']] = 0;
						}

						$templateAdjustments[$templateInvoice['id']] = $invoiceTemplateTotal;

						break;
				}
			}

			// debug
			if (!empty($_REQUEST['paymentDebug'])) {

				print_r($projectPricing['breakdown']);
				echo 'Adjustments';
				print_r($templateAdjustments);
				die();
			}

			// Now we need to apply the balances for each template invoice to the actual invoice objects.
			foreach ($templateAdjustments as $templateId => $amountToBill) {

				// Is there an actual invoice for this template, or do we need to create one?
				$invoice = __::filter($invoices, function ($inv) use ($templateId) {

					return $templateId == $inv['invoice_template'];
				})[0];

				$currentTemplate = __::filter($invoiceTemplate['templates'], function ($inv) use ($templateId) {

					return $templateId == $inv['id'];
				})[0];

				if ($invoice) {

					switch ($currentTemplate['before_after_type']) {

						case 'proposal':
						case 'project':

							$projectDate = new DateTime($project['start_date']);

							$modifier = 'P' . $currentTemplate['due_date'] . 'D';

							if ($currentTemplate['before_after'] == 'before') {

								$dueDate = $projectDate->sub(new DateInterval($modifier));
							} else {

								$dueDate = $projectDate->add(new DateInterval($modifier));
							}

							break;

						case 'today':

							$today = new DateTime();
							$modifier = 'P' . $currentTemplate['due_date'] . 'D';
							$dueDate = $today->add(new DateInterval($modifier));

							break;

						default:

							$dueDate = new DateTime();
					}

					$invoice['amount'] = $amountToBill;
					$invoice['balance'] = $amountToBill - $invoice['paid'];
					$invoice['due_date'] = $dueDate->format('Y-m-d');

					$this->updateObject($invoice, 'invoices', false, false);
				} else {

					$mainContact = $this->pgObjects->getById('contacts', $project['main_contact']);

					if (!$project['owner']) {
						$projectOwner = $mainContact['manager'];
					} else {
						$projectOwner = $project['owner'];
					}

					if ($currentTemplate['before_after'] == 'before') {

						switch ($currentTemplate['before_after_type']) {

							case 'proposal':
							case 'project':

								$projectDate = new DateTime($project['start_date']);

								$modifier = 'P' . $currentTemplate['due_date'] . 'D';
								$dueDate = $projectDate->sub(new DateInterval($modifier));

								break;

							case 'today':

								$today = new DateTime();
								$modifier = 'P' . $currentTemplate['due_date'] . 'D';
								$dueDate = $today->sub(new DateInterval($modifier));

								break;

							default:

								$dueDate = new DateTime();
						}
					} else {

						switch ($currentTemplate['before_after_type']) {

							case 'proposal':
							case 'project':

								$projectDate = new DateTime($project['start_date']);

								$modifier = 'P' . $currentTemplate['due_date'] . 'D';
								$dueDate = $projectDate->add(new DateInterval($modifier));

								break;

							case 'today':

								$today = new DateTime();
								$modifier = 'P' . $currentTemplate['due_date'] . 'D';
								$dueDate = $today->add(new DateInterval($modifier));

								break;

							default:

								$dueDate = new DateTime();
						}
					}

					$newInvoice['invoice_template'] = $templateId;
					$newInvoice['due_date'] = $dueDate->format('Y-m-d');
					$newInvoice['amount'] = $amountToBill;
					$newInvoice['balance'] = $amountToBill;
					$newInvoice['paid'] = 0;
					$newInvoice['name'] = $currentTemplate['name'];
					$newInvoice['memo'] = $currentTemplate['name'];
					$newInvoice['related_object'] = $proposal['id'];
					$newInvoice['main_contact'] = $project['main_contact'];
					$newInvoice['main_client'] = $mainContact['company'];
					$newInvoice['locked'] = 'not_locked';
					$newInvoice['owner'] = $projectOwner;

					$this->createNewObject('invoices', $newInvoice, false, false);
				}
			}

			$invoices = $this->pgObjects->where('invoices', array('related_object' => $proposal['id']));
			return $this->sendData($invoices, $json);
		} else { // no payment template selected

			// Check the current invoices to see if there are any with a $0 balance. If found, delete them.
			$nonZeroInvoices = [];
			foreach ($invoices as $invoice) {

				if ($invoice['amount'] < 1 && $invoice['amount'] > -1) {

					$this->pgObjects->delete('invoices', $invoice['id']);
				} else {

					array_push($nonZeroInvoices, $invoice);
				}
			}

			$invoices = [];
			$invoices = $nonZeroInvoices;

			// We need to add the difference to the next due, unlocked invoice,
			// or create a new invoice if we can't find an invoice to update.
			usort($invoices, function ($a, $b) {

				if ($a['due_date'] == $b['due_date']) {
					return 0;
				}

				return $a['due_date'] > $b['due_date'];
			});

			$updated = 0;

			foreach ($invoices as $invoice) {

				if ($invoice['locked'] != 'locked') {

					if (count($invoices) == 1) {
						$dueDate = new DateTime($project['start_date']);
						$invoice['due_date'] = $dueDate->format('Y-m-d');
					}

					$invoice['amount'] += $diff;
					$invoice['balance'] += $diff;

					$this->updateObject($invoice, 'invoices', false);

					$updated++;

					break;
				}
			}

			if ($updated === 0) {

				// We need to create a new invoice with the difference as the balance and amount.
				$mainContact = $this->pgObjects->getById('contacts', $project['main_contact']);

				if (!$project['owner']) {
					$projectOwner = $mainContact['manager'];
				} else {
					$projectOwner = $project['owner'];
				}

				$dueDate = new DateTime($project['start_date']);

				// change due date on new invoice to 14 days before project date if Infinity Event Management:
				if ($isInfinityEventManagement) {

					$dueDate = $dueDate->sub(new DateInterval('P14D'));
				}

				$newInvoice = $invoices[0];
				$newInvoice['due_date'] = $dueDate->format('Y-m-d');
				$newInvoice['amount'] = $diff;
				$newInvoice['balance'] = $diff;
				$newInvoice['paid'] = 0;
				$newInvoice['name'] = 'Payment for ' . $project['name'];
				$newInvoice['memo'] = 'Invoice payment';
				$newInvoice['related_object'] = $proposal['id'];
				$newInvoice['main_contact'] = $project['main_contact'];
				$newInvoice['main_client'] = $mainContact['company'];
				$newInvoice['locked'] = 'not_locked';
				$newInvoice['owner'] = $projectOwner;

				if ($diff != 0) {
					$invoices[] = $this->createNewObject('invoices', $newInvoice, false, false);
				}
			}

			$invoices = $this->pgObjects->where('invoices', array('related_object' => $proposal['id']));
			return $this->sendData($invoices, $json);
		}
	}

	public function batchTagObjects($objectType = null, $tags = [], $json = 1)
	{

		if ($_POST->objectType) {
			$objectType = $_POST->objectType;
		}
		if ($_POST->tags) {
			$tags = $_POST->tags;
		}

		if (!$objectType or !$tags) {
			return false;
		}

		$ret = $this->pgObjects->tagObjectType($objectType, $tags);

		return $this->sendData($ret, $json);
	}

	public function changeStripeDefaultSource($token = null, $customer = null, $json = 1)
	{

		if ($token == null) {
			$token = $_POST->token;
		}

		if ($customer == null) {
			$customer = $_POST->customer;
		}

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		$cu = \Stripe\Customer::retrieve($customer->id);
		$cu->default_source = $token->id;
		$cu->save();

		return $this->sendData($cu, $json);
	}

	public function chargeStripeConnectCustomer($amount = null, $connectAccountId = null, $customer = null, $json = 1)
	{

		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		if ($amount == null) {
			$amount = $_POST->amount;
		} else {
			return false;
		}

		if ($customer == null) {
			$customer = $_POST->customer;
		} else {
			return false;
		}

		if ($connectAccountId == null) {
			$connectAccountId = $_POST->connectAccountId;
		} else {
			return false;
		}

		// calculate fees and total to charge
		$incomingChargeAmount = $amount;
		$feeAmount = round($incomingChargeAmount * $this->stripeProcessingFee);
		$toBill = $incomingChargeAmount + $feeAmount;

		$charge = \Stripe\Charge::create(
			[
				"amount" => $amount,
				"currency" => "usd",
				"customer" => $customer->id,
				"source" => $_POST->sourceId
				//"application_fee_amount" => $feeAmount
			],
			[
				"stripe_account" => $connectAccountId
			]

		);

		$this->logGoSquaredTransaction($charge->id, $toBill, false);

		return $this->sendData($charge, $json);
	}

	public function chargeStripeCustomer($amount = null, $customer = null, $json = 1)
	{

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		if ($amount == null) {
			$amount = $_POST->amount;
		}

		if ($customer == null) {
			$customer = $_POST->customer->id;
		}

		// Charge the Customer instead of the card:
		$charge = \Stripe\Charge::create(array(
			"amount" => $amount,
			"currency" => "usd",
			"customer" => $customer
		));

		$this->logGoSquaredTransaction($charge->id, $amount, false);

		return $this->sendData($charge, $json);
	}

	public function checkLoginCreds($loginObj = null, $json = 1)
	{

		$email = $_POST->email;
		$password = $_POST->password;

		if ($_POST->multi) {
			$multi = true;
		}

		if ($multi) {

			$staffInfo = $this->pgObjects->whereAllCaseInsensitive('users', array('email' => $email, 'enabled' => 1));

			if (count($staffInfo) > 0) {

				$login = false;
				foreach ($staffInfo as $staff) {

					if ($this->validate_password($password, $staff['password'])) {

						$login = $staff;
					}
				}

				// Get instance names
				$instances = $this->pgObjects->whereAll(
					'instances',
					[
						'instance' => [
							'type' => 'or', 'values' => 		__::pluck(
								$staffInfo,
								'instance'
							)
						]
					]
				);

				foreach ($staffInfo as $i => $staff) {

					foreach ($instances as $instance) {

						if ($instance['instance'] === $staff['instance']) {

							$staffInfo[$i]['instanceName'] = $instance['systemName'];
						}
					}
				}

				if ($login != false) {

					return $this->sendData($staffInfo, $json);
				} else {

					return false;
				}
			} else {

				return $this->sendData(false, $json);
			}
		} else {

			if ($staffInfo = $this->pgObjects->where('users', array('email' => $email, 'enabled' => 1))) {

				if (1 == 1) {
					//if($staffInfo[0]['enabled'] == 1){

					if ($this->validate_password($password, $staffInfo[0]['password'])) {

						if ($staffInfo[0]['access_level'] == 20) {
							$type = 'event-staff';
						} else {
							$type = 'staff';
						}

						$cookie = $this->cookies->createCookie($staffInfo[0]['id'], $type, getIPAddress(false), $_POST->fingerprint, $_POST->platform, 1);

						return $staffInfo[0]['id'];
					}
				} else {

					return false;
				}
			} else {

				return false;
			}
		}
	}

	public function checkInstanceSetupCode($json = 1)
	{

		$email = $_POST->email;
		$code = $_POST->code;

		$instanceSetup = $this->pgObjects->whereAll('instance_setup', array('email' => $email, 'account_signup_code' => $code));

		if (count($instanceSetup) > 0) {
			return $this->sendData($instanceSetup[0], $json);
		} else {
			return false;
		}
	}

	public function countObjs($json = 1)
	{

		$objectType = $_POST->objectType;
		$groupBy = $_POST->groupBy;
		$where = $this->objToArr($_POST->where);

		$dateField = $_POST->dateField;
		$range = [
			'start' => $_POST->dateRange->start, 'end' => $_POST->dateRange->end
		];

		$ret = $this->pgObjects->countObjs(
			$objectType,
			$where,
			$groupBy,
			$dateField
		);

		return $this->sendData($ret, $json);
	}

	public function checkConditionsList($json = 1)
	{

		$proj = intval($_POST->project);
		$conditions = $_POST->conditions;
		$ret = [];
		// 		error_reporting(E_ALL);
		// 		ini_set('display_errors', '1');
		if ($proj) {

			// Check for conditions to transition to this state
			$conditions = $this->pgObjects->getById(
				'condition',
				$conditions
			);

			if (is_array($conditions)) {
				foreach ($conditions as $condition) {

					if (is_array($condition['conditions'])) {

						$checks = $condition['conditions'];
					}

					array_push(
						$ret,
						[
							'id' => $condition['id'], 'passed' => $this->pgObjects->runSteps(
								$proj,
								[
									'_if' => $checks
								],
								true
							)['run']
						]
					);
				}
			}
		}

		return $this->sendData($ret, $json);
	}

	public function createObjectBlueprint($json = 1)
	{

		$object = $_POST->blueprint;
		$blueprintName = $_POST->objectInfo->name;

		if ($newBlueprintId = $this->obj->createObjectBlueprint()) {

			$this->obj->update('blueprints', $newBlueprintId, 'instance', 'pagodadev');
			$this->obj->update('blueprints', $newBlueprintId, 'blueprint_type', 'object');
			$this->obj->update('blueprints', $newBlueprintId, 'blueprint_name', $blueprintName);

			if ($updatedObj = $this->obj->update('blueprints', $newBlueprintId, 'blueprint', json_encode($object->blueprint))) {

				return $this->sendData($updatedObj, $json);
			} else {

				return false;
			}
		} else {

			return false;
		}
	}

	public function createCookie($staffId = null, $json = 1)
	{

		if ($staffId == null and $_POST->staffId) {
			$staffId = $_POST->staffId;
		}

		$type = 'users';

		switch ($_POST->type) {

			case 'clientPortal':
				$type = 'portal_access_token';
				break;

			default:
				$type = 'users';
				break;
		}

		$cookie = $this->cookies->createCookie(
			$staffId,
			$type,
			$_POST->ip_address,
			$_POST->fingerprint,
			$_POST->platform,
			1
		);

		return $this->sendData($cookie, $json);
	}

	public function createEventInvoice($invoiceObj = null, $json = 1)
	{

		if ($invoiceObj == null) {

			$eventId = $_POST->eventId;
			$paid = $_POST->payments;
			$dueDate = $_POST->dueDate;
			$amount = $_POST->amount;
			$type = $_POST->type;
			$locked = $_POST->locked;
			$oldDueDate = $_POST->oldDueDate;
			$approved = $_POST->approved;
		}

		// create invoice array
		$invoice = array();

		$invoice['eventId'] = $eventId;
		$invoice['amount'] = $amount;
		$invoice['payments'] = $paid;
		$invoice['dueDate'] = $dueDate;
		$invoice['type'] = $type;
		$invoice['locked'] = $locked;
		$invoice['oldDueDate'] = $oldDueDate;
		$invoice['approved'] = $approved;

		$invoice = $this->pgObjects->create('invoice', $invoice);

		return $this->sendData($invoice, $json);
	}

	public function createEventInvoicePDF($invoiceId = null, $viewPDF = 0)
	{

		if ($invoiceId == null and $_POST->invoiceId) {
			$invoiceId = $_POST->invoiceId;
		}

		if (!$invoiceId) {
			$invoiceId = 1881;
			//$viewPDF = 1;
		}

		$invoice = $this->getObjectById('invoice', $invoiceId, 0);
		$invoiceObj = $invoice;

		$eventId = $invoiceObj['eventId'];

		$eventInfo = $this->getObject('events', $eventId, 'id', 0)[0];
		//var_dump($eventInfo);

		$guestCount = $eventInfo['guest_count'];
		$invoiceObj['dueDate'] = new DateTime($invoiceObj['dueDate']);
		$eventStartDate = new DateTime($eventInfo['event_start_date']);
		$eventEndDate = new DateTime($eventInfo['event_end_date']);
		$eventInfo['venue'] = $this->getVenueOptions('name', $eventInfo['venue']);
		$eventInfo['event_type'] = $this->getEventTypes('name', $eventInfo['event_type']);
		$eventInfo['client_info'] = $this->getObject('clients', $eventInfo['client_id'], 'id', 0)[0];
		$eventInfo['event_specialist'] = $this->getObject('staff', $eventInfo['event_specialist'], 'id', 0)[0];
		$eventInfo['payment_schedule'] = $this->getObjectById('invoice', $eventId, 'eventId', 0);

		$timelineEvents = $this->getObject('timeline_events', $eventId, 'eventId', 0);

		$sectionsArray = array();

		$timelineEventsTotal = 0;

		$totalInvoicePayments = 0;

		if ($viewPDF == 1) {
			$width = '630px';
		} else {
			$width = '100%';
		}

		// build the html for each section/table
		$beoSections = '';
		foreach ($sectionsArray as $sectionHTML) {

			if ($sectionHTML['section'] != '') {

				$beoSections .=

					'<h5 class="text-left">' . $sectionHTML['title'] . '</h5>
					<table style="width: 100%; class="table" id="food-choices">
						<tr style="width: 100%; font-size: 10px;">
							<td class="col-sm-5" style="width: 100%; border-bottom: 1px solid;">Food /Service Item</td>
						</tr>
						' . $sectionHTML['section'] . '
					</table>
					<br /><br />';
			}
		}

		// build the site locations
		foreach ($timelineEvents as $timelineEvent) {

			$eventTimelineStartDate = new DateTime($timelineEvent['startTime']);

			$siteLocations['table'] .=

				'<tr style="width:100%; border: 1px solid black !important; font-size: 12px;">
					<td class="col-sm-5" style="width: 43.27%; border-bottom: 1px dotted gray;">' . ucwords(strtolower($timelineEvent['name'])) . '</td>
					<td class="col-sm-2" style="width: 11%; border-bottom: 1px dotted gray;">' . date("g:i a", strtotime($timelineEvent['startTime'])) . '</td>
					<td class="col-sm-2" style="width: 11%; border-bottom: 1px dotted gray;">' . date("g:i a", strtotime($timelineEvent['endTime'])) . '</td>
					<td class="col-sm-1" style="width: 34.68%; border-bottom: 1px dotted gray;">' . $this->getVenueRoomOptions('name', $timelineEvent['rooms'], $eventInfo['venue_id'], 0) . '</td>
				</tr>';
		}

		$siteLocationsSection .=

			'<table style="width: 100%; class="table" id="">
				<tr style="width: 100%; font-size: 10px;">
					<td class="col-sm-5" style="width: 43.27%; border-bottom: 1px solid;">Description</td>
					<td class="col-sm-2" style="width: 11%; border-bottom: 1px solid;">Start</td>
					<td class="col-sm-2" style="width: 11%; border-bottom: 1px solid;">End</td>
					<td class="col-sm-2" style="width: 21.34%; border-bottom: 1px solid;">Banquet Room</td>
				</tr>
				' . $siteLocations['table'] . '
			</table>
			<br /><br />';

		$eventPayments = $this->getObject('payments', $eventId, 'event_id', 0);

		$totalPayments = 0;

		foreach ($invoiceObj['payments'] as $payment) {

			$totalPayments += +$payment['amount'];

			$paymentDate = new DateTime($payment['transaction_details']['ssl_txn_time']);

			$paymentHistorySection .=

				'<tr>
					<td class="col-sm-3" style="width: 60%; border-bottom: 1px dotted gray;"></td>
					<td class="col-sm-3" style="width: 20%; border-bottom: 1px dotted gray;">' . $paymentDate->format('Y-m-d') . '</td>
					<td class="col-sm-2" style="width: 20%; border-bottom: 1px dotted gray;">$' . number_format($payment['amount'], 2) . '</td>
				</tr>';

			$paymentHistoryTotalSection =

				'<tr style="width: 100%; background-color: lightgray;">
						<td class="col-sm-7" style="width: 60%;">TOTAL</td>
						<td class="col-sm-7" style="width: 20%;">TOTAL</td>
						<td class="col-sm-2" style="width: 20%;">$' . number_format($totalPayments, 2) . '</td>
					</tr>';
		}

		$paymentsMade = $totalPayments;

		$totalEventPayments = $totalPayments;

		$nextDue = [];
		$nextDue['amount'] = 0;
		$nextDue['date'] = '';
		$nextDue['type'] = '';

		foreach ($eventInfo['payment_schedule'] as $payment) {

			$totalDue = ($payment['tax'] + $payment['amount']);

			if ($totalDue - $totalPayments > 0) {

				$paymentsMade = $totalPayments;

				$totalDue = $totalDue - $totalPayments;

				$totalPayments = 0;
			} else {

				$totalPayments = $totalPayments - $totalDue;

				$paymentsMade = $totalDue;

				$totalDue = 0;
			}

			switch ($payment['type']) {

				case 'venuefee':

					$paymentTypeName = 'Venue Fee';

					break;

				case 'ConvenienceFee':

					$paymentTypeName = 'Convenience Fee';

					$convenienceLine =

						'<tr style="width: 100%;">
							<td class="col-sm-7" style="width: 75%;">Convenience Fee</td>
							<td class="col-sm-2" style="width: 25%;">$' . number_format(($payment['amount']), 2) . '</td>
						</tr>';

					break;

				default:

					$paymentTypeName = ucwords($payment['type']);
			}

			$totalDue = $totalDue / 100;

			if ($totalDue == 0) {
				$paymentStatus = 'Yes';
			} else {
				$paymentStatus = 'No';
			}

			if ($payment['type'] == 'total') {
				$totalDue = $runningTotal;
			} else {
				$runningTotal += $totalDue;
			}

			if ($rentalContract == 1) {

				if ($payment['type'] == 'venuefee') {

					$nextDue['amount'] += ($payment['tax'] + $payment['amount']) - $paymentsMade;

					$paymentSection .=

						'<tr>
								<td class="col-sm-3" style="width: 30%; border-bottom: 1px dotted gray;">' . $paymentTypeName . '</td>
								<td class="col-sm-2" style="width: 25%; border-bottom: 1px dotted gray;">' . $payment['date'] . '</td>
								<td class="col-sm-2" style="width: 15%; border-bottom: 1px dotted gray;">$' . number_format(($payment['tax'] + $payment['amount']) / 100, 2) . '</td>
								<td class="col-sm-2" style="width: 15%; border-bottom: 1px dotted gray;">$' . number_format($paymentsMade / 100, 2) . '</td>
								<td class="col-sm-1" style="width: 15%; border-bottom: 1px dotted gray;">$' . number_format($totalDue, 2) . '</td>
							</tr>';
				}
			} else {

				if ($payment['type'] == 'total') {

					$totalRemainingBalance = (($payment['tax'] + $payment['amount']) - $totalEventPayments) / 100;

					$paymentSection .=

						'<tr>
								<td class="col-sm-3" style="width: 30%; border-bottom: 1px dotted gray;">' . $paymentTypeName . '</td>
								<td class="col-sm-2" style="width: 25%; border-bottom: 1px dotted gray;">' . $payment['date'] . '</td>
								<td class="col-sm-2" style="width: 15%; border-bottom: 1px dotted gray;">$' . number_format(($payment['tax'] + $payment['amount']) / 100, 2) . '</td>
								<td class="col-sm-2" style="width: 15%; border-bottom: 1px dotted gray;">$' . number_format($totalEventPayments / 100, 2) . '</td>
								<td class="col-sm-1" style="width: 15%; border-bottom: 1px dotted gray;">$' . number_format($totalRemainingBalance, 2) . '</td>
							</tr>';
				} else {

					/*
					if($nextDue['amount'] == 0){
						$nextDue['date'] = new DateTime($payment['dueDate']);
						$nextDue['type'] = $paymentTypeName;
					}
*/

					$nextDue['amount'] += $totalDue;

					$paymentSection .=

						'<tr>
								<td class="col-sm-3" style="width: 30%; border-bottom: 1px dotted gray;">' . $paymentTypeName . '</td>
								<td class="col-sm-2" style="width: 25%; border-bottom: 1px dotted gray;">' . $payment['date'] . '</td>
								<td class="col-sm-2" style="width: 15%; border-bottom: 1px dotted gray;">$' . number_format(($payment['tax'] + $payment['amount']) / 100, 2) . '</td>
								<td class="col-sm-2" style="width: 15%; border-bottom: 1px dotted gray;">$' . number_format($paymentsMade / 100, 2) . '</td>
								<td class="col-sm-1" style="width: 15%; border-bottom: 1px dotted gray;">$' . number_format($totalDue, 2) . '</td>
							</tr>';
				}
			}

			if ($totalDue - $totalPayments > 0) {

				$paymentsMade = 0;
			}
		}

		$eventPriceArray = $this->priceEvent($eventId, 0);

		if ($eventPriceArray['discount_amount'] < 0 and $eventInfo['discount_approval'] == 1) {

			$discountLine =

				'<tr style="width: 100%;">
					<td class="col-sm-7" style="width: 75%;">Discount</td>
					<td class="col-sm-2" style="width: 25%;">$' . number_format(($eventPriceArray['discount_amount'] / 100), 2) . '</td>
				</tr>';
		} else {

			$discountLine = '';
		}

		if (!$convenienceLine) {
			$convenienceLine = '';
		}

		if (!$paymentHistorySection) {
			$paymentHistorySection = '<tr><td>No Payments Yet</td><td></td><td></td></tr>';
		}

		$subtotalTable .=
			'
			<tr style="width: 100%;">
				<td class="col-sm-7" style="width: 75%; border-bottom: 1px dotted gray;">Invoice Name</td>
				<td class="col-sm-2" style="width: 25%; border-bottom: 1px dotted gray;">$' . number_format(($invoiceObj['amount'] / 100), 2) . '</td>
			</tr>';

		switch ($invoiceObj['type']) {

			case 'venuefee':

				$paymentType = 'Venue Fee';

				break;

			default:

				$paymentType = $invoiceObj['type'];
		}

		$eventInvoices = $this->getEventInvoices($eventId, 0);
		$totalInvoices = 0;
		$totalPayments2 = 0;

		$totalInvoicePayments += $totalPayments;
		$totalEventPayments = 0;

		foreach ($eventInvoices as $eventInvoice) {

			$totalInvoices += $eventInvoice['amount'];
			$invoicePayments = 0;

			foreach ($eventInvoice['payments'] as $payment) {

				$totalPayments2 += (int)$payment['amount'];
				$invoicePayments += $payment['amount'];
			}

			$totalEventPayments += $invoicePayments;

			$paymentDate = new DateTime($eventInvoice['dueDate']);

			switch ($eventInvoice['type']) {

				case 'venuefee':

					$invoiceType = 'Venue Fee';

					break;

				default:

					$invoiceType = ucwords($eventInvoice['type']);
			}

			$eventInvoiceSection .=

				'<tr>
					<td class="col-sm-3" style="width: 20%; border-bottom: 1px dotted gray;">' . $invoiceType . '</td>
					<td class="col-sm-3" style="width: 20%; border-bottom: 1px dotted gray;">' . $paymentDate->format('Y-m-d') . '</td>
					<td class="col-sm-3" style="width: 20%; border-bottom: 1px dotted gray;">$' . number_format($eventInvoice['amount'] / 100, 2) . '</td>
					<td class="col-sm-2" style="width: 20%; border-bottom: 1px dotted gray;">$' . number_format($invoicePayments, 2) . '</td>
					<td class="col-sm-2" style="width: 20%; border-bottom: 1px dotted gray;">$' . number_format(($eventInvoice['amount'] / 100) - $invoicePayments, 2) . '</td>
				</tr>';

			$eventInvoiceTotalSection =

				'<tr style="width: 100%; background-color: lightgray;">
						<td class="col-sm-7" style="width: 20%;"></td>
						<td class="col-sm-7" style="width: 20%;">TOTAL</td>
						<td class="col-sm-7" style="width: 20%;">$' . number_format($totalInvoices / 100, 2) . '</td>
						<td class="col-sm-2" style="width: 20%;">$' . number_format($totalEventPayments, 2) . '</td>
						<td class="col-sm-2" style="width: 20%;">$' . number_format(($totalInvoices / 100) - $totalEventPayments, 2) . '</td>
					</tr>';
		}

		$invoicePayments = 0;
		foreach ($invoiceObj['payments'] as $payment) {
			$invoicePayments += $payment['amount'];
		}

		// build the actual beo HTML string for conversion to PDF
		$beoHTML =

			'<div style="border: 2px solid lightgray; padding: 20px; width:' . $width . ';">

			<img src="http://staff.infinityhospitality.net/_images/InfinityLogo.png" alt="InfinityLogo" width="100%" style="max-width:200px;" align="left">

			<h2 style="text-align:right;">Invoice</h2>

			<h3 style="text-align:right;">' . $eventInfo['venue'] . '<br />

				' . $eventStartDate->format('l, F jS, Y') . '</h3>

			<br /><br />

			<table style="width:100%; margin-top: 0px; padding-top: 0px;">

				<tr>

					<td style="width: 48%;">
						<p>Event Name: ' . $eventInfo['event_name'] . '<br />
						Client Name: ' . $eventInfo['client_info']['fname'] . ' ' . $eventInfo['client_info']['lname'] . '<br />
						Telephone: ' . $eventInfo['client_info']['phone'] . '<br />
						Guests: ' . $eventInfo['guest_count'] . '</p>
					</td>

					<td style="width: 48%;">
						<p>Memo: ' . $invoiceObj['memo'] . '<br />
						Event #: ' . $eventInfo['id'] . '<br />
						Event Specialist: ' . $eventInfo['event_specialist']['fname'] . ' ' . $eventInfo['event_specialist']['lname'] . '<br />
						Theme: ' . $eventInfo['event_type'] . '</p>
					</td>

				</tr>

			</table>

			<h3>Next Payment Due: ' . $invoiceObj['dueDate']->format('m/d/Y') . '<br />
			Payment Type: ' . ucwords($paymentType) . '</h3>

			<h3 class="text-left">Items</h3>

			<table style="width: 100%; border: 2px solid;" class="table" id="summary-table">

					' . $subtotalTable . '

			</table>

			<br />

			<h3 style="text-align:right;">Due Amount: $' . number_format(($invoiceObj['amount'] / 100) - abs($invoicePayments), 2) . '</h3>

			<br />


			<h3 class="text-left">Event Invoices</h3>

			<table style="width: 100%; border: 2px solid;" class="table" id="payment-history-table">

				<tr>
					<td class="col-sm-3" style="width: 20%; border-bottom: 1px solid;">TYPE</td>
					<td class="col-sm-3" style="width: 20%; border-bottom: 1px solid;">DUE DATE</td>
					<td class="col-sm-3" style="width: 20%; border-bottom: 1px solid;">INVOICE TOTAL</td>
					<td class="col-sm-2" style="width: 20%; border-bottom: 1px solid;">TOTAL PAID</td>
					<td class="col-sm-2" style="width: 20%; border-bottom: 1px solid;">TOTAL BALANCE</td>
				</tr>
				' . $eventInvoiceSection . '
				' . $eventInvoiceTotalSection . '
			</table>

			<br />

			<div style="text-align:center;" class="">
				<p class="">All payments are due on the dates indicated above in the Payment Schedule.</p>
				<p class="">Infinity Events & Catering</p>
				<p class="">201 Blanton Ave Nashville, TN 37210</p>
			</div>

			</div>';

		if ($viewPDF == 1) {

			$this->createPDF($beoHTML, '', '', 1, 0);
		} else {

			return $beoHTML;
		}
	}

	public function createInstanceFromSetupObject($setupObj = null, $json = 1)
	{

		if (!$setupObj) {
			$setupObj = json_decode(json_encode($_POST->setupObj), true);
		}

		if (!$setupObj) {
			return;
		}

		// create instance
		$setupObj['instance'] = $this->generateRandomString(10);
		//$setupObj->displayName = $instanceName;

		$newInstance = $this->pgObjects->createInstance('instances', $setupObj);

		// copy settings objects
		$this->copyInstanceSettingsObjects($setupObj['version'], $newInstance['instance'], 0);

		return $this->sendData($newInstance, $json);
	}

	public function createInstanceUser($userObj = null, $json = 1)
	{

		if (!$userObj) {
			$userObj = json_decode(json_encode($_POST->userObj), true);
		}

		if (!$userObj) {
			return;
		}

		$instance = $userObj['instance'];

		$this->pgObjects->changeInstance($instance);

		$newUserObj = $this->pgObjects->create('users', $userObj, 0, 0);

		return $this->sendData($newUserObj, $json);
	}

	public function createNewInstanceObject($json = 1)
	{

		$objArray = json_decode(json_encode($_POST), true);
		//var_dump($objArray);
		$ret = $this->pgObjects->createInstance('instances', $objArray['instanceObj']);

		$contact = $this->getObjectById('contacts', $objArray['user'], false);

		$user['fname'] = $contact['fname'];
		$user['lname'] = $contact['lname'];
		$user['email'] = $objArray['email'];
		$user['enabled'] = 1;
		$user['phone'] = $objArray['phone'];
		$user['type'] = 'admin';

		//$user =  $objArray['user'];

		unset($user['id']);

		$user['instance'] = $objArray['instanceObj']['instance'];
		$user['type'] = 'Admin';
		//var_dump($user);
		$this->pgObjects->setInstance($objArray['instanceObj']['instance']);

		$newUser = $this->pgObjects->create('users', $user, 0, 0);

		$this->resetPassword(false, $objArray['email']);

		//var_dump($newUser);
		foreach ($user as $k => $v) {

			if ($k != 'id') {
				$newUser[$k] = $v;
			}
		}

		//$this->updateObject($newUser, 'users', false);

		// create files bucket
		//$this->files->createBucket('../../../../_production/pagoda/_files/_instances/testing'. $objArray['instance']);

		return $this->sendData($ret, $json);
	}

	public function recordPayment($request = null, $json = 1)
	{

		$ret = []; 			// The func's response
		$newPayment = []; 	// The data for the first new payment to record
		$newPayments = []; 	// Sometimes, there are multiple payments to
		// record across different invoice objects
		$areMultiplePayments = false;
		$invoice = []; 		// The invoice to make the payment on
		$testEnv = false;

		// Parse input, if pulling in from $_POST
		if ($request === null) {

			$newPayment = $this->objToArr($_POST);
		}

		// If there are multiple payments to record, use the first to run
		// first-payment-trigger-checks, and use the list for the creation
		// process.
		$newPayments = $newPayment;

		if (
			is_array($newPayment)
			&& !empty($newPayment[0])
			&& is_int($newPayment[0]['invoice'])
		) {

			$newPayments = $newPayment;
			$newPayment = $newPayments[0];
			$areMultiplePayments = true;
		}

		// Get the invoice obj
		$invoice = $this->pgObjects->getById(
			'invoices',
			$newPayment['invoice']
		);

		// Verify the inputs
		if (
			empty($invoice)
		) {

			return $this->sendData(
				false,
				$json
			);
		}

		// set testEnv variable for recordPayment function
		if (strpos(getenv('STRIPE_SK'), 'sk_test') !== false) {

			$testEnv = true;
		}

		// set test_payment to true if testing environment
		if ($testEnv) {

			foreach ($newPayments as $index => $payment) {

				$newPayments[$index]["test_payment"] = true;
			}
		}

		// Create the new payment object
		$ret = $this->pgObjects->create(
			'payments',
			$newPayments,
			$areMultiplePayments
		);

		// If this is the first payment on the project, AND the
		// project_type is configured to transition to another state on first
		// payment (project_type.onFirstPayment), then transition the project
		// to the state specified in project_type.onFirstPayment.
		$proposal = $this->pgObjects->getById('', $invoice['related_object']);
		$project = $this->pgObjects->getById('', $proposal['main_object'], 1);

		// send email to accounting
		if ($project['instance'] == 'infinity' || $project['instance'] == 'nlp' || $project['instance'] == 'dreamcatering') {

			// email variables
			$date = date("Y/m/d");
			$startDate = empty(trim($project['start_date'])) ? "No Set Start Date" : DateTime::createFromFormat('Y-m-d H:i:s', trim($project['start_date']))->format('m/d/y h:iA');
			$totalPaymentValue = number_format(floatVal(array_reduce($newPayments, function ($total, $payment) {
				return $total + $payment["amount"];
			})) / 100, 2);
			$projectName = $project['name'];
			$projectId = $project['id'];
			$eventId = $project["object_uid"];
			$instanceName = $project['instance'];
			$urlText = "bento.infinityhospitality.net/app/" . $instanceName . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $projectId . "-" . urlencode($projectName);
			$managersArray = $project["managers"];
			$managerEmails = [];
			foreach ($managersArray as $manager) {
				array_push($managerEmails, $manager["email"]);
			}
			$paymentCreatorId = $ret[0]["created_by"];
			$paymentCreator = $this->pgObjects->getById('', $paymentCreatorId, 1);

			!in_array($paymentCreator["email"], $managerEmails) ? array_push($managerEmails, $paymentCreator["email"]) : '';

            if ($project['instance'] == 'infinity' || $project['instance'] == 'nlp') {

                array_push($managerEmails, "<EMAIL>");

            } elseif ($project['instance'] == 'dreamcatering'){

                array_push($managerEmails, "<EMAIL>", "<EMAIL>");

            }
			// Let accounting know that the payment was made
			$mergevars = array(
				'TITLE' => 'New Payment',
				'SUBJECT' => 'A new manual payment has been posted to "' . $projectName . '"',
				'BODY' => "" . $date . "<br/><br/>
				Project Name: " . $projectName . "<br/><br/>
				Event Id: " . $eventId . "<br/><br/>
				Start Date: " . $startDate . "<br/><br/>
				Payment Amount: $" . $totalPaymentValue . "<br/><br/>
				Project Link: <a href='" . $urlText . "'>" . $urlText . "</a>",
				'INSTANCE_NAME' => $project['instance']
			);

            $managerEmails = __::uniq($managerEmails);

			$this->sendEmail($managerEmails, '<EMAIL>', 'A new manual payment has been posted to "' . $projectName . '"', $mergevars, 'New Payment Email', false);

        }

		// If the related object is a project..
		if (
			$project['object_bp_type'] === 'groups'
			&& $project['group_type'] === 'Project'
		) {

			$projectType = $project['type'];

			// ..and the project type has a state transition set when the
			// first payment is made..
			if (is_int($projectType['onFirstFullPayment'])) {

				// ..and there are no other paid invoices..
				$hasAPaidInvoice = false;
				$invoices = $this->pgObjects->where(
					'invoices',
					[
						'related_object' => $proposal['id']
					]
				);

				foreach ($invoices as $i => $inv) {

					if (
						$inv['amount'] > 0
						&& $inv['balance'] < $inv['amount']
					) {
						$hasAPaidInvoice = true;
					}
				}

				if (!$hasAPaidInvoice) {

					// indicates first payment on project
					// ..then trigger the state change on the related project.
					$transitionResponse = $this->updateState(
						$project['id'],
						null,
						$projectType['onFirstFullPayment'],
						$this->getUrl() . '/app/' . $project['instance'] . '#mystuff&1=o-project-' . $project['id'] . '-' . rawurlencode($project['name']),
						'',
						false,
						0,
						function ($response) use ($ret) {

							return $ret;
						}
					);
				}
			}
		}

		$this->sendData($ret, 1);
	}

	public function resetPassword($json = 1, $email = null)
	{

		/*
		error_reporting(E_ALL);
		ini_set('display_errors', '1');
*/

		if ($email == null) {
			$email = $_POST->email;
		}

		$password = null;
		if ($_POST->password) {
			$password = $_POST->password;
		}

		// create the new password
		$pass = $this->createNewPassword(0);

		// get all user objects
		$staffInfo = $this->pgObjects->whereAll('users', array('email' => $email));

		// apply the new password
		$count = 			0;
		$emailFrom = 		'<EMAIL>';
		$hostInstance = 	false;
		$contactId = 		null;
		$hostInstanceName = 	'Login Page';
		$loginPageUrl = 'https://bento.infinityhospitality.net';

		foreach ($staffInfo as $staff) {

			$this->pgObjects->setInstance($staff['instance']);
			$usersInstance = $this->pgObjects->getAll('instances')[0];

			// Check if the user is a portal user, and if it is, swap the 'sendFrom'
			// email address w/the host instance's sendFrom email address.
			if (!empty($staff['instance'])) {

				$usersInstance;
				if ($usersInstance['is_portal'] === true) {

					$token = $this->pgObjects->whereAll(
						'portal_access_token',
						array(
							'client' => 		$usersInstance['id'], 'user' => 		intval($staff['id']), 'is_active' => 	true
						)
					)[0];

					if (!empty($token)) {
						$this->pgObjects->setInstance($token['instance']);
						$hostInstance = $this->pgObjects->getAll('instances')[0];
						$emailFrom = $hostInstance['emailFrom'];
						$contactId = $token['contact'];
						$hostInstanceName = $hostInstance['systemName'];
						if ($hostInstance['instance'] === 'foundation_group') {
							$loginPageUrl = 'https://foundationgroup.bento.infinityhospitality.net';
						}
					}
				}
			}

			$this->pgObjects->setInstance($staff['instance']);
			$this->updateObject(array('id' => $staff['id'], 'password' => $pass['pwdHash']), 'users', false);
			$count++;
		}

		// Create the email record in the host instance and tag it with the contact associated with the user
		// resetting their password.
		if ($hostInstance) {
			$this->pgObjects->setInstance($hostInstance['instance']);
		} else {
			$contactId = null;
		}

		$mergevars = array(
			'TITLE' => 			'New Password',
			'SUBJECT' => 		'Here is the new password you requested.',
			'BODY' => 			'Here is the new password you requested: ' . $pass['pwd'] . '<br /><br />If you did not request your password to be reset, please contact your administrator. Login with your new password here: <a href="' . $loginPageUrl . '">' . $loginPageUrl . '</a>',
			'INSTANCE_NAME' => 	$hostInstanceName
		);

		$this->sendEmail($staffInfo[0]['email'], $emailFrom, 'New Password', $mergevars, 'Password Reset Email', false, $contactId);

		return $this->sendData(true, $json);
	}

	public function createFromTemplate($objectId = null, $childObjs = 0, $options = null, $json = 1)
	{

		if ($objectId == null) {

			$objectId = $_POST->objectId;
			$childObjs = $_POST->childObjs; // Not being used currently
			$options = $_POST->options;
		}

		if ($objectId == null) {

			echo ('Object id not found');
			return false;
		} else {

			$object = $this->pgObjects->getById('', intval($objectId), 1);

			if ($object == null) {

				echo ('Object is not found');
				return false;
			} else {

				$templateChild = $this->pgObjects->castFromTemplate($object, null, $options);

				return $this->sendData($templateChild, $json);
			}
		}
	}

	public function createNewPassword($json = 1, $passwordToUse = false)
	{

		$alphabet = "abcdefghijklmnopqrstuwxyzABCDEFGHIJKLMNOPQRSTUWXYZ0123456789";

		$pass = array(); //remember to declare $pass as an array

		$alphaLength = strlen($alphabet) - 1; //put the length -1 in cache

		for ($i = 0; $i < 8; $i++) {

			$n = rand(0, $alphaLength);

			$pass[] = $alphabet[$n];
		}

		if ($_POST->pwd) {
			$newPassword = $_POST->pwd;
		} elseif ($passwordToUse) {
			$newPassword = $passwordToUse;
		} else {
			$newPassword = implode($pass); //turn the array into a string
		}

		$salt = base64_encode(openssl_random_pseudo_bytes(PBKDF2_SALT_BYTE_SIZE));

		$newPasswordHash = PBKDF2_HASH_ALGORITHM . ":" . PBKDF2_ITERATIONS . ":" .  $salt . ":" .

			base64_encode($this->pbkdf2(

				PBKDF2_HASH_ALGORITHM,

				$newPassword,

				$salt,

				PBKDF2_ITERATIONS,

				PBKDF2_HASH_BYTE_SIZE,

				true

			));

		return $this->sendData(array('pwd' => $newPassword, 'pwdHash' => $newPasswordHash), $json);
	}

	public function createNewObject($objectType = null, $object = null, $json = 1, $getChildObjs = 0)
	{

		if ($objectType == null) {

			$objectType = $_POST->objectType;
			$object = $this->objToArr($_POST->objectData);
		}
		if (isset($_POST->childObjs)) {
			$getChildObjs = intval($_POST->childObjs);
		}

		if ($object['_useTemplate']) {

			$templateId = $object['_useTemplate'];
			unset($object['_useTemplate']);
			$created = $this->createFromTemplate(
				$templateId,
				$getChildObjs,
				$object,
				0
			);

			return $this->sendData(
				$created,
				$json
			);
		} elseif ($createdObj = $this->pgObjects->create($objectType, $object, 0, $getChildObjs)) {

			// if creating a new user, add to the user count
			if ($objectType === 'users') {

				$instance = $this->pgObjects->where('instances', array('instance' => $_REQUEST['pagodaAPIKey']))[0];

				$userCount = 0;
				if ($instance['qty_of_users']) {
					$userCount = $instance['qty_of_users'];
				}

				$userCount++;

				$this->pgObjects->update('instances', array(
					'id' => $instance['id'],
					'qty_of_users' => $userCount
				));
			}

			return $this->sendData($createdObj, $json);
		} else {

			return $this->sendData(
				[
					'msg' => 			[
						'messages' => ['The object could not be created.']
					], '_notify_usr' => 	true
				],
				$json
			);
		}
	}

	public function createObject($tableName = null, $json = 1)
	{

		if ($tableName == null and $_REQUEST['tableName']) {
			$tableName = $_REQUEST['tableName'];
		}

		if ($newObjectId = $this->obj->create($tableName)) {
			return $this->sendData($newObjectId, $json);
		}
	}

	public function createObjectType($json = 1)
	{

		$blueprint = $_POST->blueprint;
		$objectType = $_POST->objectInfo->objectType;
		$accessLevel = $_POST->objectInfo->accessLevel;

		if ($this->obj->createObjectType($objectType, $accessLevel, $blueprint)) {

			$blueprintArray = (array) $blueprint;

			foreach ($blueprintArray as $key => $field) {
				$blueprintArray[$key] = (array) $field;
				if ($field->type == 'select' or $field->type == 'multi-select') {
					$blueprintArray[$key]['options'] = (array) $field->options;
				}
			}

			if ($objId = $this->obj->create('object_blueprints')) {

				$this->obj->update('object_blueprints', $objId, 'object_type', $objectType);
				$this->obj->update('object_blueprints', $objId, 'access_level', $accessLevel);
				$this->obj->update('object_blueprints', $objId, 'blueprint', json_encode($blueprintArray));
			}

			return $this->sendData($this->obj->getByColumn('object_blueprints', $objId, 'id'), $json);
		};
	}

	public function createPDF($body = null, $header = null, $footer = null, $view = 0, $save = 0)
	{

		require_once 'html2pdf/html2pdf.class.php';

		$html2pdf = new HTML2PDF('P', 'A4', 'en', false, 'ISO-8859-15', array(5, 20, 5, 20));
		$html2pdf->setDefaultFont('helvetica');

		$newBody = $html2pdf->getHtmlFromPage($body);

		$contractHTML =

			'<page backtop="5mm" backbottom="5mm" backleft="5mm" backright="5mm">' .

			'<page_header>' . $header . '<p style="text-align:right;"><bookmark title="Contract" level="0" ></bookmark></p></page_header>' .

			$newBody .

			'<page_footer></page_footer>' .

			'</page>';

		$html2pdf->writeHTML($contractHTML);

		if ($view == 1) {

			$html2pdf->Output('contract.pdf');
		} else {

			if ($save != 0) {

				$random = rand(100000, 900000);

				$html2pdf->Output('../_files/_contracts/' . $random . '.pdf', 'F');

				return $random . '.pdf';
			} else {

				return $html2pdf->Output('', true);
			}
		}
	}

	public function createStripeConnectAccount($accountInfo = null, $json = 1)
	{

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		if ($accountInfo == null) {
			$accountInfo = $_POST->accountInfo;
		}

		$accountInfo = json_decode(json_encode($accountInfo), true);

		// Create the customer
		$newAccount = \Stripe\Account::create($accountInfo);

		return $this->sendData($newAccount, $json);
	}

	public function createStripeCustomer($token = null, $objectId = null, $objectType = null, $json = 1)
	{
		/*
error_reporting(E_ALL);
		ini_set('display_errors', '1');
*/
		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		if ($token == null) {
			$token = $_POST->token;
		}
		//var_dump($token);

		// if(is_object($token)){
		// 	echo true;
		// 	$token
		// }
		//
		//  die();
		// store an object id

		if ($objectId == null) {
			$objectId = $_POST->contact;
		}

		$instance = $this->pgObjects->where('instances', array('instance' => $_REQUEST['pagodaAPIKey']))[0];

		// check if an object type exists
		// if true, store in $objectType and check if it is 'instances'
		// if 'instances' get main contact on instance obj
		// if 'contacts' get contact obj

		if ($objectType == null && $_POST->objectType) {

			$objectType = $_POST->objectType;

			if ($objectType === 'instances') {

				$contactObj = $this->getInstanceMainContact($objectId);
			} else if ($objectType === 'contacts') {

				$contactObj = $this->getObjectById('contacts', $objectId, false);
			}
		} else {

			$contactObj = $this->getObjectById('contacts', $objectId, false);
		}

		//$stripe = new \Stripe\StripeClient($this->stripeSecretKey);

		$customer = \Stripe\Customer::create(
			[
				"name" => $contactObj['fname'] . ' ' . $contactObj['lname'], "metadata" => array(
					'pagoda_id' => $contactObj['id'], 'name' => $contactObj['fname'] . ' ' . $contactObj['lname']
				)
				// 				, "source" => $token->id
			],
			["stripe_account" => $instance['stripe_account_id']]
		);
		/*
var_dump($customer);
die();
*/
		$updContact = $this->pgObjects->update('contacts', array('id' => $contactObj['id'], 'stripe_id' => $customer['id']), 0);

		return $this->sendData($customer, $json);
	}

	public function deleteCookie($json = 1)
	{

		$this->cookies->destroyCookie($_COOKIE['uid'], $_COOKIE['type'], $_COOKIE['series']);

		return true;
	}

	public function deleteObject($jsonObject = null, $json = 1)
	{

		$objectId = 0;
		if (is_numeric($_POST->id)) {
			$objectId = intval($_POST->id);
		} elseif (is_object($_POST->id)) {
			$objectId = $this - objToArr($_POST->id);
		} elseif (is_array($_POST->id)) {
			$objectId = $_POST->id;
		}

		if (is_array($objectId)) {

			foreach ($objectId as $i => $objId) {

				if ($this->pgObjects->delete($_POST->type, $objId)) {

					// if creating a new user, add to the user count
					if ($_POST->type === 'users') {

						$instance = $this->pgObjects->where('instances', array('instance' => $_REQUEST['pagodaAPIKey']))[0];

						$userCount = 0;
						if ($instance['qty_of_users']) {
							$userCount = $instance['qty_of_users'];
						}

						$userCount = $userCount - 1;

						$this->pgObjects->update('instances', array(
							'id' => $instance['id'],
							'qty_of_users' => $userCount
						));
					}
				}
			}

			return true;
		} else {

			if ($this->pgObjects->delete($_POST->type, $_POST->id)) {

				// if creating a new user, add to the user count
				if ($_POST->type === 'users') {

					$instance = $this->pgObjects->where('instances', array('instance' => $_REQUEST['pagodaAPIKey']))[0];

					$userCount = 0;
					if ($instance['qty_of_users']) {
						$userCount = $instance['qty_of_users'];
					}

					$userCount = $userCount - 1;

					$this->pgObjects->update('instances', array(
						'id' => $instance['id'],
						'qty_of_users' => $userCount
					));
				}

				return true;
			}
		}
	}

	public function deleteStripeConnectAccountSource($accountId = null, $json = 1)
	{

		if ($accountId == null && $_POST->accountId) {
			$accountId = $_POST->accountId;
		} else {
			return false;
		}

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		$account = \Stripe\Account::retrieve($accountId);
		$response = $account->delete();

		return $this->sendData($response, $json);
	}

	public function deleteStripeConnectPaymentSource($sourceId = null, $accountId = null, $json = 1)
	{

		if ($sourceId == null && $_POST->sourceId) {
			$sourceId = $_POST->sourceId;
		} else {
			return false;
		}

		if ($accountId == null && $_POST->accountId) {
			$accountId = $_POST->accountId;
		} else {
			return false;
		}

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		$account = \Stripe\Account::retrieve($accountId);
		$account->external_accounts->retrieve($sourceId)->delete();

		return $this->sendData($account, $json);
	}

	public function deleteStripePaymentSource($token = null, $customer = null, $json = 1)
	{

		if ($token == null) {
			$token = $_POST->token;
		}

		if ($customer == null) {
			$customer = $_POST->customer;
		}

		$instance = $this->pgObjects->where('instances', array('instance' => $_REQUEST['instance']))[0];

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		$cu = \Stripe\Customer::retrieve(
			$customer->id,
			["stripe_account" => $instance['stripe_account_id']]
		);

		$done = $cu->sources->retrieve($token->id)->delete();

		return $this->sendData($done, $json);
	}

	public function generateInstanceFromSetupObject($setupObj, $json = 1)
	{

		if (!$setupObj) {
			return;
		}

		$instanceSetupId = $_POST->instanceSetupObj;
		$instanceName = $_POST->company;


		// create instance
		$instanceSetup = $this->pgObjects->getById('instance_setup', $instanceSetupId);

		$instanceSetup['instance'] = microtime();
		$instanceSetup['displayName'] = $instanceName;

		$newInstance = $this->pgObjects->createInstance('instances', $objArray);

		// create user

		// copy settings objects
		$this->copyInstanceSettingsObjects($instance['version'], $newInstance['instance'], 0);
	}

	public function generateRandomString($length = 10)
	{
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$charactersLength = strlen($characters);
		$randomString = '';
		for ($i = 0; $i < $length; $i++) {
			$randomString .= $characters[rand(0, $charactersLength - 1)];
		}
		return $randomString;
	}

	public function generateToken($staffId = null, $json = 1)
	{

		if ($staffId == null and $_POST->staffId) {
			$staffId = $_POST->staffId;
		}

		$cookie = $this->cookies->createCookie($staffId, 'users', $_POST->ip_address, $_POST->fingerprint, $_POST->platform, false);

		return $this->sendData($cookie, $json);
	}

	public function getStartupData($json = 1)
	{

		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		$data = array();

		$data['headquarters'] = $this->pgObjects->where('groups', array(
			'parent' => 0,
			'group_type' => 'Headquarters'
		));

		if (!empty($data['headquarters'][0])) {
			$data['headquarters'] = $data['headquarters'][0];
		}

		$data['user'] = $this->pgObjects->getById(
			'users',
			$_COOKIE['uid'],
			array(
				'profile_image' =>  true, 'email' =>        true, 'fname' =>        true, 'lname' =>        true
			)
		);

		if (!empty($data['user'])) {

			$data['user']['myStuff'] = $this->pgObjects->where('groups', array(
				'user' => $data['user']['id'],
				'group_type' => 'MyStuff'
			))[0];

			$data['companyLogo'] = $this->pgObjects->where(
				'company_logo',
				array(
					'is_primary' => 'yes'
				),
				'',
				[
					'company_logo' => true
				]
			)[0];

			$data['entityTypes'] = $this->pgObjects->getAll('entity_type');

			$data['portalTokens'] = $this->getActivePortals(false);
		}

		return $this->sendData($data, $json);
	}

	public function getActivePortals($json = 1)
	{

		// Get active tokens for the current user.
		$tokens = $this->pgObjects
			->whereAll(
				'portal_access_token',
				array(
					'client' => 		$this->appConfig['id'], 'user' => 		intval($_COOKIE['uid']), 'is_active' => 	true
				)
			);

		// 		error_reporting(E_ALL);
		// 		ini_set('display_errors', '1');

		// Merge in instance name for display.
		if (is_array($tokens)) {
			foreach ($tokens as $i => $token) {

				$tokens[$i]['systemName'] = $this->pgObjects->whereAll(
					'instances',
					[
						'instance' => $token['instance']
					]
				)[0]['systemName'];
			}
		}

		return $this->sendData(
			$tokens,
			$json
		);
	}

	public function getAll($tableName = null, $json = 1)
	{

		if ($tableName == null) {
			$tableName = $_POST->objectType;
		}

		$objs = $this->pgObjects->getAll($tableName);

		return $this->sendData($objs, $json);
	}

	public function getAllObjects($tableName = null, $getChildObjs = 0, $json = 1, $paged = null)
	{

		$archived = false;
		if ($_POST->archived) {
			$archived = $_POST->archived;
		}

		if ($tableName == null) {
			$tableName = $_POST->objectType;
		}
		if (isset($_POST->getChildObjs)) {

			if (is_numeric($_POST->getChildObjs)) {
				$getChildObjs = intval($_POST->getChildObjs);
			} elseif (is_object($_POST->getChildObjs)) {
				$getChildObjs = $this->objToArr($_POST->getChildObjs);
			}
		}
		if (isset($_POST->paged->paged)) {

			$count = true;
			$paged = true;
			$pageLength = $_POST->paged->pageLength;
			$sortCol = $_POST->paged->sortCol;
			$sortDir = $_POST->paged->sortDir;
			$offset = $_POST->paged->page;

			if (isset($_POST->paged->count)) {
				$count = $_POST->paged->count;
			}

			if ($_POST->paged->sortCast) {
				$sortCast = $_POST->paged->sortCast;
			} else {
				$sortCast = 'string';
			}

			if (count($_POST->paged->search) > 0) {

				$queryObj = array();

				foreach ($_POST->paged->search as $searchObj) {

					$queryObj[$searchObj->searchField] = array(
						'value' => $searchObj->searchTerm,
						'type' => 'contains'
					);
				}
			}

			if ($queryObj) {

				$objs = $this->pgObjects->where($tableName, $queryObj, '', $getChildObjs, true, $offset, $sortCol, $sortDir, $pageLength, null, array(), $sortCast);
			} else {

				$objs = $this->pgObjects->getAll($tableName, $getChildObjs, true, $offset, $sortCol, $sortDir, $pageLength, $sortCast, $count);
			}
		} else {

			$objs = $this->pgObjects->getAll(
				$tableName,
				$getChildObjs,
				false,
				0,
				'date_created',
				'asc',
				100,
				'string',
				true,
				$archived
			);
		}

		if ($tableName == 'notes') {
			foreach ($objs as $key => $obj) {
				$objs[$key]['about'] = $this->pgObjects->where($obj['type'], array('id' => $obj['type_id']), '', 0)[0];
			}
		}

		if ($paged) {

			$ret = array(
				"draw" => $_POST->queryObj->paged->paged,
				"recordsFiltered" => $objs[0]['full_count'],
				"recordsTotal" => $objs[0]['full_count'],
				"data" => $objs
			);

			return $this->sendData($ret, $json);
		} else {

			return $this->sendData($objs, $json);
		}
	}

	public function getApplicationBuild($json = 1)
	{

		$ret = array(
			"build" => APP_BUILD
		);

		return $this->sendData($ret, $json);
	}

	public function getCountWhere($objectType, $queryObj, $json = 1, $getChildObjs = 0)
	{

		$additionalClause = '';

		if (isset($_POST->objectType)) {
			$objectType = $_POST->objectType;
			$queryObj = $this->objToArr($_POST->queryObj);

			if ($queryObj['groupBy']) {
				$additionalClause = "group by object_data->>'" . $queryObj['groupBy'] . "'";
				unset($queryObj['groupBy']);
			}
		}

		if (isset($_POST->getChildObjs)) {
			if (is_numeric($_POST->getChildObjs)) {
				$getChildObjs = intval($_POST->getChildObjs);
			} elseif (is_object($_POST->getChildObjs)) {
				$getChildObjs = $this->objToArr($_POST->getChildObjs);
			}
		}

		foreach ($queryObj as $key => $val) {

			if (is_numeric($val)) {

				$queryObj[$key] = intval($val);
			}
		}

		if ($objectData = $this->pgObjects->getCountWhere($objectType, $queryObj, $additionalClause)) {

			if ($_POST->queryObj->paged) {

				return $this->sendData($ret, $json);
			} else {

				return $this->sendData($objectData, $json);
			}
		} else {

			return $this->sendData([], $json);
		}
	}

	public function getEventInvoice($invoiceId = null, $json = 1)
	{

		if ($invoiceId == null) {
			$invoiceId = $_POST->invoiceId;
		}

		$invoice = $this->pgObjects->getById('invoice', intval($invoiceId));

		$this->sendData($invoice, $json);
	}

	public function getEventInvoices($eventId = null, $json = 1)
	{

		if ($eventId == null) {
			$eventId = $_POST->eid;
		}

		$invoices = $this->pgObjects->where('invoice', array('eventId' => intval($eventId)));

		return $this->sendData($invoices, $json);
	}

    public function fgContactList($companyId = null, $json = 1){


        /*
            FOUNDATION GROUP UTIL FUNC

            @PARAM COMPANY ID
            @PARAM BOOL JSON FRM RESPONSE
            RETURNS - [CONTACTS]
            - GETS CONTACTS EMAIL CONTACT_INFO OBJ
            - GETS CONTACTS PORTAL ACCESS TOKEN
            - GETS CONTACTS ASSOCIATED USER OBJ IN PORTAL INSTANCE
        */

        if ( property_exists($_POST, 'organization') ) {
            $companyID = $_POST->organization;
        }

        $contactList = $this->getObjectsWhere('contacts', [ 'company'=> $companyID ], 0
        , [
            'contact_info' => array(
                'type' => true
                , 'object_id' => true
                , 'info' => true
            )
            , 'name' => true
        ]);

        // echo "3256 |contactList: " . pr_print($contactList) . PHP_EOL . "<br>";
        $updContactList = array();

        foreach ($contactList as $key => $value) {

            $value['email'] = __::find($value['contact_info'], function($infoType){
                return $infoType['type']['data_type'] == 'email';
            })['info'];

            unset($value['contact_info']);

            $contact = $value;

            $tokenList = $this->getObjectsWhere('portal_access_token', ['contact'=> $value['id']], 0
            , [
                'client' => true
                , 'company' => true
                , 'contact' => true
                , 'user' => true
            ]);

            foreach ($tokenList as $key => $value) {

                $portalInstanceUser = $this->pgObjects->whereAcrossInstances('users', ['id' => $value['user']]);

                $tokenList[$key]['user'] = $portalInstanceUser[0];
            }
            $contact['portal_access_tokens'] = $tokenList;
            array_push($updContactList, $contact);

        }

        $this->sendData($updContactList, 1);
    }

    public function fgRemoveToken($contactId = null, $json = 1){

        /*
            FOUNDATION GROUP UTIL FUNC

            @PARAM CONTACT ID
            @PARAM BOOL JSON FRM RESPONSE
            RETURNS - RESP OBJ LISTING ITEMS THAT HAVE BEEN ARCHIVED
            - GETS CONTACTS EMAIL CONTACT_INFO OBJ
            - GETS CONTACTS PORTAL ACCESS TOKEN
                - SWITCHES TO PORTAL INSTANCE
                    - ARCHIVES PORTAL USER LINKED TO FG CONTACT
                - SWITCHES BACK TO FG INSTANCE
                    - ARCHIVES PORTAL ACCESS TOKEN FOR CONTACT
                    - ARCHIVES CONTACT_INFO OBJS LINKED TO CONTACT
                    - ARCHIVES CONTACT
        */

        $response = new stdClass();

        if ( property_exists($_POST, 'contactId') ) {
            $contactId = $_POST->contactId;
        }

        $fg_contact = $this->pgObjects->where('contacts', array('id' => $contactId), '', [
            'contact_info' => true
            , 'name' => true
            , 'info' => true
        ])[0];
        $response->{'deleted_contact'} = $fg_contact;

        ///get portal access token
        $portal_access_token = $this->pgObjects->where('portal_access_token', ['contact'=> $contactId], '', [
            'client' => true
            , 'company' => true
            , 'contact' => true
            , 'user' => true
            , 'archive' => true
        ])[0];
        $response->{'deleted_token'} = $portal_access_token;

        ///(1) Clean up portal user
        if ( isset( $portal_access_token ) ) {

            $portalInstanceUser = $this->pgObjects->whereAcrossInstances('users', ['id' => $portal_access_token['user']])[0];
            $response->{'deleted_user'} = $portalInstanceUser;

            if ( isset( $portalInstanceUser ) ) {

                ///set the instance
                $this->pgObjects->setInstance($portalInstanceUser['instance']);

                ///delete user
                $deletedUser = $this->pgObjects->delete($portalInstanceUser['object_bp_type'], $portalInstanceUser['id']);

                ///reset instance back to vsoft
                $this->pgObjects->setInstance('foundation_group');

            }
            ///(2) Clean up Portal Token
            $this->pgObjects->delete('portal_access_token', $portal_access_token['id']);

        }
        ///(3) Clean up FG Contact
        if( isset($fg_contact) ){

            if ( !empty($fg_contact['contact_info']) ){
                ///(4) clean up contacts contact info objs
                foreach ($fg_contact['contact_info'] as $key => $val) {
                    $del_coninfo = $this->pgObjects->delete('contact_info', $val['id']);
                }
            }

            $this->pgObjects->delete('contacts', $fg_contact['id']);

        }

        $this->sendData($response, 1);

    }

//     public function fgShareWithContacts(){

//         /*
//         Reattach to correct Contact/User
//         [ ] Projects
//             [ ] Action Items
//         [ ] Get All Projects for Company
//         [ ] Get All Action Items
//         */

//         $contactID = $_POST->organization;
//         $contact = $this->getObjectsWhere('contacts', [ 'id'=> $contactID ], 0
//         , [
//             'contact_info' => array(
//                 'type' => true
//                 , 'object_id' => true
//             )
//             , 'name' => true
//         ]);
// // var_dump("3334 :: contact ", $contact ). PHP_EOL . "<br>";
// // die();

//         // foreach ($contactList as $key => $value) {

//         //     $contact = $value;

//         //     $tokenList = $this->getObjectsWhere('portal_access_token', ['contact'=> $value['id']], 0
//         //     , [
//         //         'client' => true
//         //         , 'company' => true
//         //         , 'contact' => true
//         //         , 'user' => true
//         //     ]);

//         //     foreach ($tokenList as $key => $value) {

//         //         $portalInstanceUser = $this->pgObjects->whereAcrossInstances('users', ['id' => $value['user']]);

//         //         $contact['portal_access_token'] = $value;
//         //         $contact['portal_user'] = $portalInstanceUser[0];

//         //     }

//         //     array_push($updContactList, $contact);

//         // }

//         // $this->sendData($updContactList, 1);
//     }

	public function getEventStaffingObject($eventId = null, $json = 1)
	{

		if (isset($_REQUEST['event-id'])) {
			$eventId = $_REQUEST['event-id'];
		}

		return $this->sendData($this->getObject("event_staffing", $eventId, 'event_id', 0)[0], $json);
	}

	public function getEventStaffStatus($retType = 'name', $staffId = null)
	{

		if (isset($_POST->selected)) {
			$selected = $_POST->selected;
		}

		if (isset($_POST->retType)) {
			$retType = $_POST->retType;
		}

		if ($staffId == 0 and isset($_POST->{'staff-id'})) {

			$staffId = $_POST->{'staff-id'};

			$staffInfo = $this->pgObjects->getById('staff', intval($staffId));
		}

		if ($retType == 'list') {

			$ret = array();

			foreach ($this->eventStaffTypes as $key => $value) {

				array_push($ret, [
					'id' => $key,
					'name' => $value
				]);
			}

			return $this->sendData($ret, 1);
		}

		if ($retType == 'name') {

			foreach ($this->eventStaffTypes as $statusValue => $statusName) {

				if ($statusValue == $selected) {
					return $statusName;
				}
			}
		} elseif ($retType == 'options') {

			foreach ($this->eventStaffTypes as $value => $name) {

				$checked = '';

				if (is_array($staffInfo['event_staff_type'])) {

					foreach ($staffInfo['event_staff_type'] as $departmentId) {

						if ($value == $departmentId) {

							$checked = 'checked';
						}
					}

					$i++;
				} else {

					$checked = '';
				}

				// check for selected values
				if ($itemInfo != null) {

					$selectedOptions = count($itemInfo['details'][$option['option_name']]);

					$i = 0;

					while ($i < $selectedOptions) {

						foreach ($itemInfo['details'][$option['option_name']] as $itemOptionValue) {

							if ($itemOptionValue == $name or $itemOptionValue == $value) {

								$checked = 'checked';
								$i = 100;
							}

							$i++;
						}
					}
				}

				if (1 == 1) {

					$options .= '<label><input type="checkbox" ' . $checked . ' name="type[]" value="' . $value . '"> ' . $name . '</label> ';
				} else {

					$options .= '<label><input type="checkbox" ' . $checked . ' name="type[]" value="' . $value . '"> ' . $name . '</label><br />';
				}
			}

			//$options = null;

			return $options;
		}
	}

	public function getEventTypes($retType = 'name', $selected = 0)
	{

		if (isset($_REQUEST['selected'])) {
			$selected = $_REQUEST['selected'];
		}

		if (isset($_REQUEST['retType'])) {
			$retType = $_REQUEST['retType'];
		}

		if ($retType == 'name') {

			foreach ($this->eventTypes as $statusValue => $statusName) {

				if ($statusValue == $selected) {
					return $statusName;
				}
			}
		} elseif ($retType == 'options') {

			$options = null;

			foreach ($this->eventTypes as $statusValue => $statusName) {

				if ($statusValue == $selected) {

					$options .= '<option value="' . $statusValue . '" selected>' . $statusName . '</option>';
				} else {

					$options .= '<option value="' . $statusValue . '">' . $statusName . '</option>';
				}
			}
		}

		return $options;
	}

	public function getFileByType($oid = null, $type = null, $fileType = null, $json = 1)
	{

		if ($oid == null) {
			$oid = $_REQUEST['oid'];
			$type = $_REQUEST['type'];
			$fileType = $_REQUEST['fileType'];
		}

		$fileInfo = $this->pgObjects->where('file_meta_data', array(
			'oid_type' => $type,
			'oid' => intval($oid),
			'file_type' => $fileType
		));

		return $this->sendData($fileInfo, $json);
	}

	public function updateSortAndState()
	{

		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		$data = $this->objToArr($_POST);

		if (!empty($data['objectId'])) {

			$obj = $this->pgObjects->getById('', array('id' => $data['objectId']), 1)[0];

			if (!empty($obj)) {

				if (!empty($data['sort']) && is_array($data['sort'])) {

					if (
						$data['sort']['before']
						&& $data['sort']['after']
					) {
						$instructions = array(
							'type' => 'between',
							'before' => $data['sort']['before'],
							'after' => $data['sort']['after']
						);
					} else if ($data['sort']['after']) {
						$instructions = array(
							'type' => 'after',
							'target' => $data['sort']['after']
						);
					} else if ($data['sort']['before']) {
						$instructions = array(
							'type' => 'before',
							'target' => $data['sort']['before']
						);
					}

					$this->pgObjects->runSteps(
						$data['objectId'],
						[
							'moveSortOrder' => $instructions
						],
						true
					)['updates'][0];
				}

				if (!empty($data['state']) && is_array($data['state'])) {

					if (!empty($data['state']['newState']) && !empty($data['state']['link'])) {

						if ($obj[$data['state']['stateProperty']] != $data['state']['newState']) {

							$this->updateState(
								$data['objectId'],
								$data['state']['stateProperty'],
								$data['state']['newState'],
								$data['state']['link'],
								'',
								false,
								0
							);
						}
					}
				}

				return $this->sendData($obj, 1);
			} else {

				return $this->sendData(false, 1);
			}
		} else {

			return $this->sendData(false, 1);
		}
	}

	public function getObjectsWhereGrouped($objectType = null, $queryObj = null, $json = 1, $getChildObjs = 0)
	{

		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		$groupedObjs = array(
			'data' => array()
		);

		if ($objectType == 'time_entries') {

			$pageLength = 100;
			$totalRecordsPaged = $pageLength;

			$queryObj['paged']['pageLength'] = $pageLength;

			function getTimeEntriesPaged($app, $groupedObjs, $totalRecordsPaged, $objectType, $queryObj, $json, $getChildObjs)
			{

				$objs = $app->getObjectsWhere($objectType, $queryObj, $json, $getChildObjs);

				if (!empty($objs)) {

					foreach ($objs['data'] as $obj) {

						$notFoundInArray = true;

						foreach ($groupedObjs['data'] as $key => $groupedObj) {

							if ($groupedObj['shift']['id'] == $obj['shift']['id']) {

								$notFoundInArray = false;

								if (array_key_exists('note', $getChildObjs)) {
									if (array_key_exists('note', $groupedObjs['data'][$key])) {
										if (array_key_exists('note', $obj)) {
											$groupedObjs['data'][$key]['note'] = !empty($obj['note']) ? $groupedObjs['data'][$key]['note'] . '</br>' .  $obj['note'] : $groupedObjs['data'][$key]['note'];
										}
									}
								}

								if (array_key_exists('duration', $getChildObjs)) {
									if (array_key_exists('duration', $groupedObjs['data'][$key])) {
										if (array_key_exists('duration', $obj)) {
											$groupedObjs['data'][$key]['duration'] = floatval($groupedObjs['data'][$key]['duration']) + floatval($obj['duration']);
										}
									}
								}

								if (array_key_exists('rate', $getChildObjs)) {
									if (array_key_exists('rate', $groupedObjs['data'][$key])) {
										if (array_key_exists('rate', $obj)) {
											$groupedObjs['data'][$key]['rate'] = floatval($obj['rate']);
										}
									}
								}

								if (array_key_exists('hourly_rate', $getChildObjs)) {
									if (array_key_exists('hourly_rate', $groupedObjs['data'][$key])) {
										if (array_key_exists('hourly_rate', $obj)) {
											$groupedObjs['data'][$key]['hourly_rate'] = floatval($groupedObjs['data'][$key]['hourly_rate']) + floatval($obj['hourly_rate']);
										}
									}
								}
							}
						}

						if (empty($groupedObjs['data']) || $notFoundInArray) {

							array_push($groupedObjs['data'], $obj);
						}
					}

					if (($objs['recordsTotal'] - $totalRecordsPaged) > 0) {

						$totalRecordsPaged = $totalRecordsPaged + $queryObj['paged']['pageLength'];

						$queryObj['paged']['page'] = $queryObj['paged']['page'] + $queryObj['paged']['pageLength'];

						return getTimeEntriesPaged($app, $groupedObjs, $totalRecordsPaged, $objectType, $queryObj, $json, $getChildObjs);
					} else {

						$groupedObjs['recordsTotal'] = !empty($objs['recordsTotal']) ? $objs['recordsTotal'] : 0;
						$groupedObjs['groupsTotal'] = count($groupedObjs['data']);

						return $groupedObjs;
					}
				} else {

					return array();
				}
			}

			$response = getTimeEntriesPaged($this, $groupedObjs, $totalRecordsPaged, $objectType, $queryObj, $json, $getChildObjs);

			return $this->sendData($response, $json);
		} else {

			return $this->sendData(array(), $json);
		}
	}

	public function getDataFilter()
	{

		$post = $this->objToArr($_POST);

		$queryObj = array();

		foreach ($post as $key => $value) {
			$queryObj[$key] = $value;
		}

		$dataFilter = $this->pgObjects->getByIdAcrossInstances('data_filter', $queryObj['id']);

		if (!empty($dataFilter) && ($dataFilter['instance'] == $this->appConfig['instance'] || in_array($dataFilter['shared_with_instances'], $this->appConfig['instance']))) {

			return $this->sendData($dataFilter, 1);
		} else {

			return $this->sendData(false, 1);
		}
	}

	public function getTickets()
	{

		$post = $this->objToArr($_POST);

		$queryObj = array();

		foreach ($post as $key => $value) {
			$queryObj[$key] = $value;
		}

		$queryObj['_30'] = $this->appConfig['instance'];

		$childObjs = $queryObj['childObjs'];
		unset($queryObj['childObjs']);

		$this->pgObjects->setInstance('voltzsoftware');

		$tickets = $this->getObjectsWhere('#Help_ticket', $queryObj, 0, $childObjs);
		// $tickets = $this->getObjectsWhere('#Test', $queryObj, 0, $childObjs);

		$this->pgObjects->setInstance($this->appConfig['instance']);

		return $this->sendData($tickets, 1);
	}

	public function getTicket()
	{

		$this->pgObjects->setInstance('voltzsoftware');

		$ticket = $this->pgObjects->getById('', array('id' => $_POST->id))[0];

		$this->pgObjects->setInstance($this->appConfig['instance']);

		return $this->sendData($ticket, 1);
	}

	public function getTicketBlueprint()
	{

		$this->pgObjects->setInstance('voltzsoftware');

		$ticketBlueprint = $this->pgObjects->getBlueprint('#Help_ticket');
		// $ticketBlueprint = $this->pgObjects->getBlueprint('#Test');

		$ticketBlueprint['_4']['workflow'] = $this->pgObjects->getById('', array('id' => $ticketBlueprint['_4']['workflow']))[0];

		$this->pgObjects->setInstance($this->appConfig['instance']);

		return $this->sendData($ticketBlueprint, 1);
	}

	public function updateTicketPriority()
	{

		$this->pgObjects->setInstance('voltzsoftware');

		$obj = $this->pgObjects->getById('', $_POST->objId);
		$steps = $this->objToArr($_POST->steps);

		$response = $this->pgObjects->rules->runSteps(
			$obj,
			$steps,
			$this->pgObjects,
			true
		);

		$ret = [
			'msg' => $response,
			'response' => $response['updated'],
			'_notify_usr' => true,
			'_completed' => $response['run']
		];

		$this->pgObjects->setInstance($this->appConfig['instance']);

		return $this->sendData($ret, 1);
	}

	public function getTicketComments()
	{

		$post = $this->objToArr($_POST);

		$queryObj = array();

		foreach ($post as $key => $value) {
			$queryObj[$key] = $value;
		}

		$queryObj['public'] = 1;

		$childObjs = $queryObj['childObjs'];
		unset($queryObj['childObjs']);

		$this->pgObjects->setInstance('voltzsoftware');

		$ticketComments = $this->getObjectsWhere('notes', $queryObj, 0, $childObjs);

		$this->pgObjects->setInstance($this->appConfig['instance']);

		if (!empty($ticketComments['data'])) {
			$ticketComments['data'] = $this->parseAuthors($ticketComments['data']);
		}

		return $this->sendData($ticketComments, 1);
	}

	public function getComments()
	{

		$post = $this->objToArr($_POST);
		$queryObj = array();

		foreach ($post as $key => $value) {
			$queryObj[$key] = $value;
		}

		$childObjs = $queryObj['childObjs'];
		unset($queryObj['childObjs']);

		$queryObj['_dont_force_portal_user_tag'] = true;

		$comments = $this->getObjectsWhere('notes', $queryObj, 0, $childObjs);

		if (!empty($comments['data'])) {
			$comments['data'] = $this->parseAuthors($comments['data']);
		}

		return $this->sendData($comments, 1);
	}

    public function getWhiteListComments()
	{

		$post = $this->objToArr($_POST);
		$queryObj = array();

		foreach ($post as $key => $value) {
			$queryObj[$key] = $value;
		}

		$childObjs = $queryObj['childObjs'];
		unset($queryObj['childObjs']);

		$queryObj['_dont_force_portal_user_tag'] = true;

        $queryObj['record_type'] = 'comment';

		$comments = $this->getObjectsWhere('notes', $queryObj, 0, $childObjs);

		if (!empty($comments['data'])) {


            $statusWhiteList = [
                'Intake', 'More Info Requested','WIP', 'Hold Until 990', 'Hold | State/Date of Inc.'
                , 'Hold | 1023 Application', 'Hold | IRS Determination Letter', 'Hold | 990'
                , 'Hold', 'Hold for Payment', 'Info Review', 'Information Review', 'Client Review'
                , 'Authorized', 'Final Review', 'Unassigned', 'Complete', 'Withdrawn',
            ];

            function findByUid($states, $searchUid)
            {

                foreach ($states as $st) {
                    if ($st['uid'] === $searchUid) {
                        return $st;
                    }
                }

                return null;
            }

			$comments['data'] = $this->parseAuthors($comments['data']);

            $filteredComments = array();

            foreach($comments['data'] as $i => &$comm){

                foreach($comm['tagged_with'] as $j => $tag){

                    $obj = $this->pgObjects->getById('', $tag,
                        [
                            'name'=> true
                            , 'object_bp_type'=> true
                            , 'group_type' => true
                            , 'state'=>true
                            , 'type'=> true
                        ]
                    , 2);

                    if ($obj['object_bp_type'] == 'companies') {
                        $foundCompany = $obj;
                    }
                    if ($obj['object_bp_type'] == 'groups' && $obj['group_type'] == 'Project') {
                        $foundProject = $obj;
                    }

                }

                unset($tag);

                $comm['related_company'] = $foundCompany;
                $comm['related_project'] = $foundProject;

                if ($comm['related_project']) {

                    $possibleStates =  $comm['related_project']['type']['states'];
                    $currentState =  $comm['related_project']['state'];
                    $foundState = findByUid($possibleStates, $currentState);

                    if ($foundState !== null) {

                        if ( in_array($foundState['name'], $statusWhiteList) ) {
                            array_push($filteredComments, $comm);
                        }

                    }
                }

            }


            unset($comm);
            $comments['recordsFiltered'] = count($filteredComments);
            $comments['data'] = $filteredComments;

		}

        return $this->sendData($comments, 1);
	}

	public function parseAuthors($objs = null, $email = false)
	{

		if (!empty($_POST->data)) {
			$objs = $this->objToArr($_POST)['data'];
			$authorIds = array();
			$i = 0;
			if (empty($objs[0])) {
				$tempObjs = $objs;
				$objs = array();
				array_push($objs, $tempObjs);
			}
			foreach ($objs as $obj) {
				if (is_array($obj['author'])) {
					$objs[$i]['author'] = $obj['author']['id'];
				}
				array_push($authorIds, $objs[$i]['author']);
				$i++;
			}
			$authorIds = __::uniq($authorIds);
		} else {
			$authorIds = __::uniq(__::pluck($objs, 'author'));
		}

		if (!empty($authorIds)) {

			$childObjs = array(
				'fname' => true,
				'lname' => true,
				'profile_image' => 'id'
			);

			if($email){
			    $childObjs["email"] = true;
            }

			$authors = $this->pgObjects->getByIdAcrossInstances('users', $authorIds, $childObjs);

			$profileImageIDs = array();
			$i = 0;
			foreach ($authors as $author) {
				$profileImageID = is_array($author['profile_image']) ? $author['profile_image']['id'] : $author['profile_image'];
				$authors[$i]['profile_image'] = $profileImageID;
				array_push($profileImageIDs, $profileImageID);
				$i++;
			}
			$profileImageIDs = __::uniq($profileImageIDs);
			$profileImages = $this->pgObjects->getByIdAcrossInstances('file_meta_data', $profileImageIDs);

			$i = 0;
			foreach ($objs as $obj) {
				foreach ($authors as $author) {
					if ($obj['author'] == $author['id']) {
						$objs[$i]['author'] = $author;
						foreach ($profileImages as $profileImage) {
							if ($author['profile_image'] == $profileImage['id']) {
								$objs[$i]['author']['profile_image'] = $profileImage;
							}
						}
					}
				}
				$i++;
			}
		}

		if (!empty($_POST->data)) {
			return $this->sendData($objs, 1);
		} else {
			return $objs;
		}
	}

	public function downloadFilesAsZip()
	{

		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		function getDocumentName($document, $hash = false)
		{

			// Set name
			$documentName = str_replace('/', '-', $document['name']);

			// Remove file extension
			if (substr($documentName, strrpos($documentName, '.') + 1) == substr($document['loc'], strrpos($document['loc'], '.') + 1)) {
				$documentName = substr($documentName, 0, strrpos($documentName, '.'));
			}

			// Hash the name
			if ($hash) {
				$documentName = $documentName . '-' . md5(rand());
			}

			// Add file extension
			$documentName =  $documentName . '.' . substr($document['loc'], strrpos($document['loc'], '.') + 1);

			// Return the name
			return $documentName;
		}

		function addFileToZip($app, $document, $zip, $path = '')
		{

			// Set document name
			$documentName = getDocumentName($document, false);

			// Check for duplicate
			if ($zip->locateName($path . $documentName) !== false) {
				$documentName = getDocumentName($document, true);
			}

			// Add file to folder
			$zip->addFromString($path . $documentName, file_get_contents($app->filesBucketUrl . '/' . $app->appConfig['instance'] . '/' . $document['loc']));

			return;
		}

		function recurrsionThroughFolders($app, $documents, $zip, $path = '')
		{

			foreach ($documents as $document) {

				if ($document['document_type'] == 'folder') {

					// Set name
					$folderName = str_replace('/', '-', $document['name']);

					// Check for duplicate
					if ($zip->locateName($path . $folderName . '/') !== false) {
						$folderName = $folderName . '-' . md5(rand());
					}

					// Set path
					$path = $path . $folderName . '/';

					$documents = $app->pgObjects->where('document', array('parent' => $document['id']), '', 1);

					if (!empty($documents)) {

						// Create a blank folder
						$zip->addEmptyDir($path);

						// Loop through folders and files
						recurrsionThroughFolders($app, $documents, $zip, $path);
					} else {

						// Create a blank folder
						$zip->addEmptyDir($path);
					}

					// Clear path
					$path = '';
				} else if ($document['document_type'] == 'upload') {

					// Add a file to folder
					if (!empty($document['loc'])) {
						addFileToZip($app, $document, $zip, $path);
					}
				}
			}

			return;
		}

		$ids = $_POST->ids;

		$childObjs = array(
			'document_type' => true,
			'loc' => true,
			'name' => true
		);

		$documents = $this->pgObjects->getById('documents', $ids, $childObjs);

		if (!empty($documents)) {

			// Set zip folder name
			$zipFolderName = $this->appConfig['instance'] . '-' . md5(rand()) . '.zip';

			// Create and open zip file
			$zip = new ZipArchive;
			$zip->open($zipFolderName, ZipArchive::CREATE);

			// Loop through all of the files and folders to create
			recurrsionThroughFolders($this, $documents, $zip);
			$userMsg = $zip->getStatusString();
			$zip->close();

			// !TODO: Put into spaces in temp-zip-downloads/
			$key = "HDDFHSE3NPPNGCNUEVOV";
			$secret = "+wACknEAuRZ0BfTsDj7MHIHF+3XySoaDy1Hl54eRdeg";
			$space_name = "pagoda";
			$region = "nyc3";
			$space = new SpacesConnect($key, $secret, $space_name, $region);

			//!TODO: enable an expiration date on the file upload into the spaces api
			$spacesResponse = $space->UploadFile(basename($zipFolderName), "public", "tempDownloads/" . $zipFolderName);

			// Push the URL back to the user
			$data = array(
				'url' => 	$spacesResponse['ObjectURL'], 'msg' => 	$userMsg
			);
			$zip->close();
			$zip = null;

			return $this->sendData($data, 1);
		} else {

			return $this->sendData(false, 1);
		}
	}

	public function getMergeTagData()
	{

		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		// Set variables
		$objId = $_POST->objId;
		$obj = $_POST->obj;

		if ($objId) {
			$obj = json_decode(json_encode($this->pgObjects->getById('', $objId, 1, 1)));
		}

		if (empty($obj)) {
			return;
		}

		$group = null;
		if ($obj->object_bp_type == 'groups') {
			$group = $obj->id;
		} else if (!empty($obj->related_object->id)) {
			$group = $obj->related_object->id;
		} else {
			$group = $obj->related_object;
		}

		// Initialize variables
		$data = array(
			'blueprints' => array(
				'contact' => array(),
				'company' => array(),
				'proposal' => array()
			)
		);
		$data['contact'] = array();
		$data['company'] = array();
		$data['company_logo'] = array();
		$data['invoices'] = array();
		$data['inventory_menu'] = array();
		$data['inventory_menu_pricing_breakdown'] = array();
		$data['invoice_system'] = array();
		$data['project'] = array();
		$data['schedule'] = array();

		$contactBlueprint = $this->pgObjects->getBlueprint('contacts');
		$companyBlueprint = $this->pgObjects->getBlueprint('company');
		$proposalBlueprint = $this->pgObjects->getBlueprint('groups');

		$data['blueprints'] = array(
			'contact' => $contactBlueprint,
			'company' => $companyBlueprint,
			'proposal' => $proposalBlueprint
		);

		$companyLogo = $this->pgObjects->where('company_logo', array('is_primary' => 'yes'), '', 1);
		$data['company_logo'] = $companyLogo;

		$invoiceSystem = $this->pgObjects->getAll('invoice_system', 1);
		$data['invoice_system'] = $invoiceSystem;
		$feesList = $this->pgObjects->getAll('invoice_fees', 1);
		$data['feesList'] = $feesList;

		if ($obj->merge_type == 'contact') {

			$contactID = !empty($obj->main_contact) ? $obj->main_contact . id : $obj->id;
			$contact = $this->pgObjects->getById('contacts', $contactID, 1);

			$companyID = !empty($contact['company']) ? $contact['company']['id'] : $contact['company']['related_object']['company'];
			$company = $this->pgObjects->getById('company', $companyID, 1);

			$data['contact'] = $contact;
			$data['company'] = $company;
		}

		if ($obj->contact) {

			$company = $this->pgObjects->getById('companies', $obj->contact->company->id, 1, 1);
			$data['company'] = $company;
		}

		if ($group) {

			$project = $this->pgObjects->getById('groups', $group, 2);

			if (!empty($project['proposal']['id'])) {

				$invoices = $this->pgObjects->where('invoices', array('related_object' => $project['proposal']['id']), '', 2);
				$schedule = $this->pgObjects->where('groups', array('group_type' => 'Schedule', 'parent' => $project['proposal']['id']), array('id' => true));

				$data['invoices'] = $invoices;
				$data['schedule'] = $schedule;
			}

			if (!empty($project['proposal']['menu'])) {

                $proposalID = $project['proposal']['menu'];

                if ( array_key_exists( 'id', $project['proposal']['menu']) ){
                    $proposalID = $project['proposal']['menu']['id'];
                }

				$inventory_menu = $this->pgObjects->getById('inventory_menu', $proposalID, 2, 1);
				$inventory_menu_pricing_breakdown = $this->pgObjects->where('inventory_menu_pricing_breakdown', array('menu' => $inventory_menu['id']));

				$data['inventory_menu'] = $inventory_menu;
				$data['inventory_menu_pricing_breakdown'] = $inventory_menu_pricing_breakdown;
			}

			if (!empty($project['main_contact'])) {

				$contact = $this->pgObjects->getById('contacts', $project['main_contact']['id'], 1);
			} else {

				$contact = array(
					'fname' => 'FIRST NAME',
					'lname' => 'LAST NAME',
					'name' => 'NAME',
					'company' => array()
				);
			}

            $data['contracts'] = array();

            $contracts = $this->pgObjects->where(
                'contracts'
                , array(
                    'related_object' => $project['id']
                    , 'status' => 'Signed'
                )
                , ''
                , 2
            );

			$data['contact'] = $contact;
			$data['project'] = $project;

            $data['contracts'] = $contracts;

		}

		return $this->sendData($data, 1);
	}

	public function generateBEOMergeTag()
	{

		// get a setup object
		$Setup = $_POST->setup;
		$Default_setup = [
			'border' 			=> true, 'sectionNames' 	=> true, 'internalNotes' 	=> true, 'choiceItems' 	=> true
		];
		$options = $_POST->options;

		// get menu object
		$menuId = $_POST->menuId;
		$menu = $this->pgObjects->getById('', $menuId, 1, 1);

		// Enhanced menu item processing with debugging
		$debugInfo = [];

		if (!empty($menu['sections'])) {
			$debugInfo['total_sections'] = count($menu['sections']);

			foreach( $menu['sections'] as $i => &$section){
				$debugInfo["section_$i"] = [
					'section_name' => $section['name'] ?? 'unnamed',
					'items_raw_count' => count($section['items'] ?? [])
				];

				if ( !empty($section['items']) ) {
					$debugInfo["section_$i"]['items_before'] = [];

					foreach( $section['items'] as $j => &$item ){
						$debugInfo["section_$i"]['items_before'][$j] = [
							'type' => gettype($item),
							'value' => is_numeric($item) ? "ID: $item" : 'object'
						];

						// NEW: Check if item is just an ID (integer)
						if (is_numeric($item)) {
							$debugInfo["section_$i"]["item_$j"] = "Fetching full object for ID: $item";

							// Fetch the full menu line item object
							$item = $this->pgObjects->getById('inventory_menu_line_item', $item, 1);

							if ($item && isset($item['item']['category'])) {
								$debugInfo["section_$i"]["item_$j"] .= " -> Category: " . $item['item']['category'];
							} else {
								$debugInfo["section_$i"]["item_$j"] .= " -> NO CATEGORY FOUND";
							}
						}
					}
				}
			}
		}

		// get menu sections
		$menuSections = $menu['sections'];

		// order menu sections
		$ordered_menuSections = __::sortBy($menuSections, function ($section) {

			return $section['sortId'];
		});

		function checkSetup($s, $defaultS)
		{

			foreach ($s as $optionName => $optionValue) {

				$defaultS[$optionName] = $optionValue;
			}

			return $defaultS;
		}

		$Default_setup = checkSetup($Setup, $Default_setup);

		$borderStyling = '';

		if ($Default_setup['border']) {
			$borderStyling = 'border: 1px solid lightgray; padding: 15px;';
		}

		$MergeTagHTML = '';

		$categories = array();

		if (
			!empty($options)
			&& is_array($options)
		) {

			foreach ($options as $cat) {

				array_push($categories, intval($cat));
			}
		}

		foreach ($ordered_menuSections as $section) {

			$itemsList = array();

			if (!empty($section['items'])) {

				foreach ($section['items'] as $i => $item) {

					$itemCategory = $item['item']['category'];

					if (
						empty($categories)
					) {

						array_push($itemsList, $item);
					} else {

						if (
							in_array($itemCategory, $categories)
						) {

							array_push($itemsList, $item);
						}
					}
				}
			}

			if (!empty($itemsList)) {

				$sectionItems 	= $section['items'];
				$internalNotes 	= $section['details'];

				$MergeTagHTML .= '<div style="' . $borderStyling . ' margin-bottom: 20px;">';

				// SECTION NAMES
				if ($Default_setup['sectionNames'] == true) {

					$sectionTime = '';

					if (!empty($section['from'])) {

						$sectionTimeFrom = new DateTime($section['from'], new DateTimeZone('UTC'));
						$sectionTimeTo = new DateTime($section['to'], new DateTimeZone('UTC'));

						$sectionTimeFrom->setTimeZone(new DateTimeZone($Setup->tz));
						$sectionTimeTo->setTimeZone(new DateTimeZone($Setup->tz));

						$sectionTime = ', ' . $sectionTimeFrom->format('m/d/Y g:i A') . ' to ' . $sectionTimeTo->format('m/d/Y g:i A');
					}

					$MergeTagHTML .= '<h3 style="font-weight:bold;">' . $section['name'] . $sectionTime . '</h3>';
				}

				// INTERNAL NOTES
				if ($Default_setup['internalNotes'] == true) {

					if (!empty($section['details'])) {

						$MergeTagHTML .= '<div style="margin-top: 5px; color: red;">' . $section['details'] . '</div>';
					}
				}

				$MergeTagHTML .= '<table class="medium-editor-table" style="width:100%; border-collapse: collapse; margin: 15px 0;">';

				$MergeTagHTML .= '<thead>';

				$MergeTagHTML .= '<tr style="text-align:left !important; font-size:12px;font-weight:bold; background-color: #F2F3F4; color:#B9BEC4;">';

				$MergeTagHTML .= '<th style="padding:5px; color:#B9BEC4; width: 50%; text-align:left !important;">Item</th>';

				if ($this->appConfig['instance'] != 'dreamcatering') {
					$MergeTagHTML .= '<th style="padding:5px; color:#B9BEC4; width: 40%; text-align:left !important;">Notes</th>';
				}

				$MergeTagHTML .= '<th style="padding:5px; color:#B9BEC4; width: 10%; text-align:left !important;">Qty</th>';

				$MergeTagHTML .= '</tr>';

				$MergeTagHTML .= '</thead>';

				$MergeTagHTML .= '<tbody>';

				foreach ($itemsList as $i => $item) {

					$rowBackgroundColor = '';
					$itemNameDisplay = $item['item']['name'];

					if ($i % 2 == 1) {

						$rowBackgroundColor = '#F2F3F4';
					}

					if (array_key_exists('beo_html', $item['item'])) {

						$itemNameDisplay = $item['item']['beo_html'];

						if ($this->appConfig['instance'] == 'dreamcatering') {

							if (
								$item['note']
							) {

								$itemNameDisplay .= '<div style="padding: 10px 10px 0 5px; color: red !important;">' . $item['note'] . '</div>';
							} else {

								$itemNameDisplay .= '<div style="padding: 10px 0px;"></div>';
							}
						}
					}

					$MergeTagHTML .= '<tr style="background-color: ' . $rowBackgroundColor . ';">';

					$MergeTagHTML .= '<td style="padding: 5px;">' . $itemNameDisplay . '</td>';

					if ($this->appConfig['instance'] != 'dreamcatering') {

						if (
							$item['note']
						) {

							$MergeTagHTML .= '<td style="padding: 10px 10px 0 5px; color: red; text-align:right;">' . $item['note'] . '</td>';
						} else {

							$MergeTagHTML .= '<td style="padding: 10px 0px;"></td>';
						}
					}

					$qtyDisplay = $item['absolute_qty'];

					if (
						$item['qty_type'] == 'per_guest'
						|| $item['qty_type'] == 'per_hour_per_guest'
					) {

						$qtyDisplay = $item['qty'];

						if ($menu['guest_count'] > 0) {
							$qtyDisplay = round($item['qty'] * $menu['guest_count']);
						}
					} else if ($item['qty_type'] == 'guest_count') {

						$qtyDisplay = $menu['guest_count'];
					} else if ($item['qty_type'] == 'per_hour') {

						$qtyDisplay = $item['qty'];
					}

					$MergeTagHTML .= '<td style="padding: 10px 0px; text-align: right; padding-right: 5px;">' . $qtyDisplay . '</td>';

					$MergeTagHTML .= '</tr>';
				}

				$MergeTagHTML .= '</tbody>';

				$MergeTagHTML .= '</table></div>';

				//$MergeTagHTML .= '<div style="text-align: center;"> ---- End of '. $section['name'] .' ---- </div>';

				//$MergeTagHTML .= '</div>';

			}
		}

		return $this->sendData($MergeTagHTML, 1);
	}

    public function generateDreamBEOMergeTag() {
		$Setup = $_POST->setup;
		$Default_setup = [
			'border' 			=> true
			, 'sectionNames' 	=> true
			, 'internalNotes' 	=> true
			, 'choiceItems' 	=> true
		];
		$options = $_POST->options;

		// get menu object
		$menuId = $_POST->menuId;
		$menu = $this->pgObjects->getById('', $menuId, 1, 1);

		// get menu sections
		$menuSections = $menu['sections'];

		// order menu sections
		$ordered_menuSections = __::sortBy($menuSections, function($section){

			return $section['sortId'];

		});
        ///test isolation
        // $ordered_menuSections = [$ordered_menuSections[1]];
		function checkSetup($s, $defaultS) {

			foreach($s as $optionName => $optionValue) {

				$defaultS[$optionName] = $optionValue;

			}

			return $defaultS;

		}

		$Default_setup = checkSetup($Setup, $Default_setup);

		$borderStyling = '';

		if ($Default_setup['border']) {
			$borderStyling = 'border: 1px solid lightgray; padding: 15px;';
		}

		$MergeTagHTML = '';

		$categories = array();

		if (
			!empty($options)
			&& is_array($options)
		) {

			foreach($options as $cat) {

				array_push($categories, intval($cat));

			}

		}

		foreach($ordered_menuSections as $section) {

			$itemsList = array();

			if (!empty($section['items'])) {

				foreach($section['items'] as $i => $item) {

                    $itemCategory = isset( $item['item']['category']['id'] ) ? $item['item']['category']['id'] : $item['item']['category'];

					if (
						empty($categories)
					) {

						array_push($itemsList, $item);

					} else {

						if (
							in_array($itemCategory, $categories)
						) {

							array_push($itemsList, $item);

						}

					}

				}

			}

			if (!empty($itemsList)) {

				$sectionItems 	= $section['items'];
				$internalNotes 	= $section['details'];

				$MergeTagHTML .= '<div style="'. $borderStyling .' margin-bottom: 20px;">';

				// SECTION NAMES
				if ($Default_setup['sectionNames'] == true) {

					$sectionTime = '';

					if (!empty($section['from'])) {

						$sectionTimeFrom = new DateTime($section['from'], new DateTimeZone('UTC'));
						$sectionTimeTo = new DateTime($section['to'], new DateTimeZone('UTC'));

						$sectionTimeFrom->setTimeZone(new DateTimeZone($Setup->tz));
						$sectionTimeTo->setTimeZone(new DateTimeZone($Setup->tz));

						$sectionTime = ', '. $sectionTimeFrom->format('m/d/Y g:i A') . ' to ' . $sectionTimeTo->format('m/d/Y g:i A');

					}

					$MergeTagHTML .= '<h3 style="font-weight:bold;">'. $section['name'] . $sectionTime .'</h3>';

				}

				// INTERNAL NOTES
				if ($Default_setup['internalNotes'] == true) {

					if (!empty($section['details'])) {

						$MergeTagHTML .= '<div style="margin-top: 5px; color: red;">' . $section['details'] . '</div>';

					}

				}

				$MergeTagHTML .= '<table class="medium-editor-table" style="width:100%; border-collapse: collapse; margin: 5px 0;">';

					$MergeTagHTML .= '<thead>';

						$MergeTagHTML .= '<tr style="text-align:left !important; font-size:12px;font-weight:bold; background-color: #F2F3F4; color:#B9BEC4;">';

							$MergeTagHTML .= '<th style="padding:5px; color:#B9BEC4; width: 70%; text-align:left !important;">Item</th>';

                            $MergeTagHTML .= '<th style="padding:5px; color:#B9BEC4; width: 20%; text-align:right !important;">Serving Style</th>';

							$MergeTagHTML .= '<th style="padding:5px; color:#B9BEC4; width: 10%; text-align:right !important;">Qty</th>';

						$MergeTagHTML .= '</tr>';

					$MergeTagHTML .= '</thead>';

					$MergeTagHTML .= '<tbody>';

                            /*
                                @TODO - DEVELOPER NOTES
                                $item => LINEITEM

                                    - has CHOICE SELECTIONS MADE -> $full_items MAP TO GET CHOICE DATA
                                        - name, desc, components (if any)
                                            - has Components
                                                - reference $full_items MAP
                                                    -name, desc, components (if any)
                                            - does not have Components
                                                - parse the description
                                                    - has | character
                                                    - does not have pipe character
                                    - DOES NOT HAVE CHOICE SELECTIONS (empty array )
                                        - parse the description
                                            - has | character
                                            - does not have pipe character

                                $fullItem => LINEITEM + Beo information

                                $full_items => LINEITEM + Map of component information
                            */

                        foreach($itemsList as $i => $item) {
                            // echo "4667 |item: " . pr_print($item) . PHP_EOL . "<br>";
                            $fullItem = $this->pgObjects->getById('', $item['id']
                                , [
                                    'beo_qty' => true
                                    , 'beo_servingstyle' => true
                                    , 'beo_note' => true
                                    , 'name' => true
                                    , 'beo_ingredients' => true
                                ], 2);

                            $full_items = $this->getFullRecipe($item['item']['id'], 0)['items'];

                            $rowBackgroundColor = '#ffffff';
                            $toplevel_itemNameDisplay = '<strong>' . $item['item']['name'] . '</strong>';
                            $toplevel_servingsStyleDisplay = isset( $item['beo_servingstyle'] ) ? strtoupper( $item['beo_servingstyle'] ) : '-';
                            $toplevel_quantityDisplay = isset( $item['beo_qty'] ) ? $item['beo_qty'] : '-';
                            $includeRowBorder = 'border-top:1px gray solid;';

                            $item_beoNote = '-';

                            $lineItemType = $item['type'];
                            if ($i % 2 == 1) {

                                $rowBackgroundColor = '#F2F3F4';

                            }

                            /*
                                (1) Line items either have choices on them or not. Need to display
                                    [LINE ITEM NAME][beo_servingstyle][beo_qty]
                                    [LINE ITEM DESCRIPTION                    ]
                                    [beo_note                                 ]
                                        [COMPONENT ITEM NAME][beo_servingstyle][beo_qty]
                                        [COMPONENT ITEM DESCRIPTION                    ]
                                        [beo_note                                      ]
                                            [IF COMPONENT HAS COMPONENTS - ITEM NAME][beo_servingstyle][beo_qty]
                                            [IF COMPONENT HAS COMPONENTS - ITEM DESCRIPTION                    ]
                                            [beo_note                                                          ]
                            */

                            if ( isset($item) && !empty($item) ) {

                                /// top level name, serving style, QTY
                                $MergeTagHTML .= '<tr style="background-color: '. $rowBackgroundColor  . ';'. '">';

                                    $MergeTagHTML .= '<td style="padding: 5px 0 0 5px;">'. $toplevel_itemNameDisplay .'</td>';

                                    $MergeTagHTML .= '<td style="padding: 5px 5px 0 0; color: black; text-align:right;">' . $toplevel_servingsStyleDisplay . '</td>';

                                    $MergeTagHTML .= '<td style="padding: 5px 5px 0 0; text-align: right;">' . $toplevel_quantityDisplay. '</td>';

                                $MergeTagHTML .= '</tr>';

                                if ( array_key_exists('beo_note', $fullItem) && !empty($fullItem['beo_note']) ) {

                                    $item_beoNote = $fullItem['beo_note'];

                                    $MergeTagHTML .= '<tr style="background-color: '. $rowBackgroundColor .';">';

                                        $MergeTagHTML .= '<td colspan="3" style="color: red; padding-left: 5px;">' . strip_tags($item_beoNote) . '</td>';

                                    $MergeTagHTML .= '</tr>';

                                }

                                if ( $lineItemType === 0 ){

                                    $choicesHaveBeenSelected = false;

                                    if ( isset( $item['choices'][0]['choice'] ) && !empty($item['choices'][0]['choice']) ) {
                                        $choicesHaveBeenSelected = true;
                                    }

                                    if ( $choicesHaveBeenSelected ){

                                        foreach($item['choices'] as $choiceItemMain) {

                                            foreach ($choiceItemMain['choice'] as $choiceItem) {

                                                if (isset($choiceItem) && !empty($choiceItem)) {


                                                    $choiceItemInfo = null;

                                                    foreach($full_items as $itemInfo){
                                                        if(($itemInfo['inventory_group'] == 0) && ($itemInfo['id'] == $choiceItem['item'])){
                                                            $choiceItemInfo =  $itemInfo['choices'];
                                                            break;
                                                        } else if(!empty($itemInfo['inventory_group'])){

                                                            $groupForSearch = $choiceItem['choice'][0];
                                                            $nameReference = $choiceItem['name'];

                                                            //found group
                                                            $idToSearchInventaryGroup = null;
                                                            foreach($itemInfo['inventory_group']['items'] as $idc => $choicesToSearch){
                                                                if(!empty($choicesToSearch['choices'])) {
                                                                    foreach ($choicesToSearch['choices'] as $choicesInside) {
                                                                        if (($groupForSearch == $choicesInside['id']) && ($choicesInside['inventory_group']['name'] == $nameReference)) {
                                                                            $idToSearchInventaryGroup = $idc;
                                                                            $choiceItemInfo = ($itemInfo['inventory_group']['items'][$idToSearchInventaryGroup])['choices'];
                                                                            break;
                                                                        }
                                                                    }
                                                                }
                                                            }

                                                        }
                                                    }

                                                    if (isset($choiceItemInfo) && !empty($choiceItemInfo)) {

                                                        foreach ($choiceItem['choice'] as $j => $selectedChoice) {

                                                            if (isset($selectedChoice) && !empty($selectedChoice)) {
                                                                // echo "4752 |selectedChoice: " . pr_print($selectedChoice) . PHP_EOL . "<br>";
                                                                $selectedChoiceInfo = __::find($choiceItemInfo, function ($compItem) use ($selectedChoice) {
                                                                    return $compItem['id'] == $selectedChoice;
                                                                });

                                                                // echo "4756 |selectedChoiceInfo: " . pr_print($selectedChoiceInfo) . PHP_EOL . "<br>";
                                                                $selectedChoiceName = $choiceItem['name'];
                                                                if (isset($selectedChoiceInfo['inventory_group'])) {
                                                                    $selectedChoiceName = $selectedChoiceInfo['inventory_group']['name'];
                                                                }

                                                                $selectedChoice_beo_servingstyle = '';
                                                                $selectedChoice_beo_qty = '';
                                                                $selectedChoice_beo_note = '';

                                                                if (array_key_exists('beo_ingredients', $fullItem)) {

                                                                    $selectedChoice_beoIngredients = __::find($fullItem['beo_ingredients'], function ($ingr) use ($selectedChoiceInfo) {
                                                                        if($ingr['name'] != null && $selectedChoiceName != null) {
                                                                            return str_contains($ingr['name'], $selectedChoiceName);
                                                                        }
                                                                        return false;
                                                                    });

																	if(!isset($selectedChoice_beoIngredients['beo_note'])){

																			$selectedChoice_beoIngredients = __::find($fullItem['beo_ingredients'], function ($ingr) use ($selectedChoiceInfo, $selectedChoiceName) {
																				if($ingr['name'] != null && $selectedChoiceName != null) {
																						$ingrName = explode(':', $ingr['name']);
																						return str_contains($ingrName[0], $selectedChoiceName);
																				}
																				return false;
																			});

																	}


                                                                    if (isset($selectedChoice_beoIngredients['beo_servingstyle'])) {
                                                                        $selectedChoice_beo_servingstyle = strtoupper($selectedChoice_beoIngredients['beo_servingstyle']);
                                                                    }
                                                                    if (isset($selectedChoice_beoIngredients['beo_qty'])) {
                                                                        $selectedChoice_beo_qty = $selectedChoice_beoIngredients['beo_qty'];
                                                                    }
                                                                    if (isset($selectedChoice_beoIngredients['beo_note'])) {
                                                                        $selectedChoice_beo_note = $selectedChoice_beoIngredients['beo_note'];
                                                                    }

                                                                }

                                                                $MergeTagHTML .= '<tr style="background-color: ' . $rowBackgroundColor . ';' . $includeRowBorder . '">';

                                                                $MergeTagHTML .= '<td>&nbsp;&nbsp; - ' . $selectedChoiceName . '</td>';

                                                                $MergeTagHTML .= '<td style="color: black; text-align:right;">' . $selectedChoice_beo_servingstyle . '</td>';

                                                                $MergeTagHTML .= '<td style="text-align: right;">' . $selectedChoice_beo_qty . '</td>';

                                                                $MergeTagHTML .= '</tr>';

                                                                if (!empty($selectedChoice_beo_note)) {

                                                                    $MergeTagHTML .= '<tr style="background-color: ' . $rowBackgroundColor . '">';

                                                                    $MergeTagHTML .= '<td colspan="3"  style="color: red;">&nbsp;&nbsp;&nbsp;' . strip_tags($selectedChoice_beo_note) . '</td>';

                                                                    $MergeTagHTML .= '</tr>';

                                                                }

                                                                ///if selected components has nested components
                                                                if (isset($selectedChoiceInfo['inventory_group']['items']) && !empty($selectedChoiceInfo['inventory_group']['items'])) {

                                                                    foreach ($selectedChoiceInfo['inventory_group']['items'] as $k => $selectedChoiceComponent) {
                                                                        // echo "4803 |scComponent: " . pr_print($selectedChoiceComponent) . PHP_EOL . "<br>";

                                                                        $selectedChoiceComponentName = '[selected choice component name]';

                                                                        if (isset($selectedChoiceComponent['inventory_group'])) {

                                                                            $selectedChoiceComponentName = $selectedChoiceComponent['inventory_group']['name'];
                                                                        }

                                                                        $MergeTagHTML .= '<tr style="background-color: ' . $rowBackgroundColor . ';">';

                                                                        $MergeTagHTML .= '<td style="padding-left:50px;"> -- <em>' . $selectedChoiceComponentName . '</em></td>';

                                                                        $MergeTagHTML .= '<td style="color: black; text-align:right;">' . $selectedChoice_beo_servingstyle . '</td>';

                                                                        $MergeTagHTML .= '<td style="text-align: right;">' . $selectedChoice_beo_qty . '</td>';

                                                                        $MergeTagHTML .= '</tr>';

                                                                        // $nestedChoiceInformation = __::find($scComponent['choices'], function ($chItem) use ($selectedChoice) {

                                                                        //     return $chItem['id'] == $selectedChoice;
                                                                        // });
                                                                        // $nestedChoiceItemName = $nestedChoiceInformation['inventory_group']['name'];
                                                                        // echo "4821 |||nestedChoiceInformation: " . pr_print($nestedChoiceInformation) . PHP_EOL . "<br>";
                                                                        // $componentOfNestedCompName =  $litem['inventory_group']['name'];

                                                                        // if ( isset( $nestedChoiceItemName ) && !empty($nestedChoiceItemName) ) {

                                                                        //     $MergeTagHTML .= '<tr style="background-color: '. $rowBackgroundColor .';">';

                                                                        //         $MergeTagHTML .= '<td colspan="3">&nbsp;&nbsp;&nbsp;&nbsp; -- '. $nestedChoiceItemName .'</td>';

                                                                        //     $MergeTagHTML .= '</tr>';

                                                                        // }

                                                                        // $nestedChoiceItem_beo_servingstyle = '-';
                                                                        // $nestedChoiceItem_beo_qty = '-';
                                                                        // $nestedChoiceItem_beo_note = '';

                                                                        // if ( array_key_exists('beo_ingredients', $fullItem) ) {

                                                                        //     $nestedChoiceItem_beoIngredients = __::find($fullItem['beo_ingredients'], function($ingr) use ( $nestedChoiceInformation ){

                                                                        //         return str_contains($ingr['name'], $nestedChoiceInformation['inventory_group']['name']);
                                                                        //     });

                                                                        //     if ( isset($nestedChoiceItem_beoIngredients['beo_servingstyle']) ) {
                                                                        //         $nestedChoiceItem_beo_servingstyle = strtoupper($nestedChoiceItem_beoIngredients['beo_servingstyle']);
                                                                        //     }
                                                                        //     if ( isset($nestedChoiceItem_beoIngredients['beo_qty']) ) {
                                                                        //         $nestedChoiceItem_beo_qty = $nestedChoiceItem_beo_qty['beo_qty'];
                                                                        //     }
                                                                        //     if ( isset($nestedChoiceItem_beoIngredients['beo_note']) ) {
                                                                        //         $nestedChoiceItem_beo_note = $nestedChoiceItem_beoIngredients['beo_note'];
                                                                        //     }

                                                                        //     $MergeTagHTML .= '<tr style="background-color: '. $rowBackgroundColor .';">';

                                                                        //         $MergeTagHTML .= '<td>&nbsp;&nbsp; - '. $choiceSelectionName .'</td>';

                                                                        //         $MergeTagHTML .= '<td style="color: black; text-align:right;">' . $choiceSelection_beo_servingstyle . '</td>';

                                                                        //         $MergeTagHTML .= '<td style="text-align: right;">' . $choiceSelection_beo_qty. '</td>';

                                                                        //     $MergeTagHTML .= '</tr>';

                                                                        // }


                                                                        // if ( isset($litem['inventory_group']['items']) && !empty($litem['inventory_group']['items']) ) {

                                                                        //     foreach( $litem['inventory_group']['items'] as $m => $mitem ){

                                                                        //         $componentOfNestedCompName =  $mitem['inventory_group']['name'];

                                                                        //         if ( isset($componentOfNestedCompName) && !empty($componentOfNestedCompName) ) {

                                                                        //             $MergeTagHTML .= '<tr style="background-color: '. $rowBackgroundColor .';">';

                                                                        //                 $MergeTagHTML .= '<td colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; --- '. $componentOfNestedCompName .'</td>';

                                                                        //             $MergeTagHTML .= '</tr>';
                                                                        //         }

                                                                        //         if ( isset($mitem['inventory_group']['items']) && !empty($mitem['inventory_group']['items']) ) {

                                                                        //             foreach( $mitem['inventory_group']['items'] as $n => $nitem ){

                                                                        //                 $componentOfNestedCompName =  $nitem['inventory_group']['name'];

                                                                        //                 if ( isset( $componentOfNestedCompName ) && !empty($componentOfNestedCompName) ) {

                                                                        //                     $MergeTagHTML .= '<tr style="background-color: '. $rowBackgroundColor .';">';

                                                                        //                         $MergeTagHTML .= '<td colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ---- '. $componentOfNestedCompName .'</td>';

                                                                        //                     $MergeTagHTML .= '</tr>';
                                                                        //                 }
                                                                        //             }
                                                                        //         }
                                                                        //     }
                                                                        // }

                                                                    }

                                                                }
                                                            }
                                                        }

                                                    }
                                                }

                                            }
                                        }
                                    } else {

                                        ///Line Item is an INDIVIDUAL COMPONENT, BUT HAS NESTED COMPONENT ITEMS
                                        if ( isset($item['item']['items']) && empty($fullItem['beo_ingredients']) ){

                                            foreach( $full_items as $j => $item ){

                                                $componentItem =$item['inventory_group'];

                                                $component_itemNameDisplay = $componentItem['name'];

                                                $MergeTagHTML .= '<tr>';

                                                    $MergeTagHTML .= '<td  colspan="3">&nbsp;&nbsp; - '. $component_itemNameDisplay .'</td>';

                                                $MergeTagHTML .= '</tr>';

                                                /// NESTED COMPONENT ITEM ALSO HAS COMPONENT ITEMS
                                                if ( !empty($componentItem['items']) ) {

                                                    foreach( $componentItem['items'] as $k => $it ){
                                                        // echo "4935 |it: " . pr_print($it) . PHP_EOL . "<br>";
                                                        $componentNestedName =  $it['name'];
                                                        // echo "4937 |componentNestedName: " . pr_print($componentNestedName) . PHP_EOL . "<br>";

                                                        if ( isset($it['inventory_group']) && !empty( $it['inventory_group'] ) ){
                                                            $componentNestedName =  $it['inventory_group']['name'];
                                                        }

                                                        $MergeTagHTML .= '<tr>';

                                                            $MergeTagHTML .= '<td style="padding-left:50px;" colspan="3"> -- <em>'. $componentNestedName .'</em></td>';

                                                        $MergeTagHTML .= '</tr>';

                                                        ///CHECK TO SEE IF COMPONENET ITEM OF THE ABOVE NESTED, HAS COMPONENTS
                                                        if ( count( $it['inventory_group']['items'] ) > 0 ) {

                                                            foreach( $it['inventory_group']['items'] as $l => $litem ){

                                                                $componentOfNestedCompName =  $litem['inventory_group']['name'];

                                                                $MergeTagHTML .= '<tr>';

                                                                    $MergeTagHTML .= '<td style="padding-left:75px;" colspan="3"> --- '. $componentOfNestedCompName .'</td>';

                                                                $MergeTagHTML .= '</tr>';

                                                            }

                                                        }

                                                    }
                                                }
                                            }

                                        }

                                    }

                                } else if ( $lineItemType === 'item' ){

                                    $description = strip_tags( $item['item']['description']);

                                    if ( strpos($item['item']['description'], '|') ) {

                                        $componentsFromDescription = explode('|', $description );

                                        foreach( $componentsFromDescription as $component ){

                                            $MergeTagHTML .= '<tr style="background-color: '. $rowBackgroundColor .';">';

                                                $MergeTagHTML .= '<td>&nbsp;&nbsp; - '. rtrim(ltrim($component)) .'</td>';

                                                $MergeTagHTML .= '<td></td>';

                                                $MergeTagHTML .= '<td></td>';

                                            $MergeTagHTML .= '</tr>';

                                        }

                                    }

                                }

                            }
                        }

                    $MergeTagHTML .= '</tbody>';

				$MergeTagHTML .= '</table></div>';

            }

		}

        return $this->sendData($MergeTagHTML, 1);

	}

    public function generateInfinityBEOMergeTag()
	{
        $debug = false;

		// get a setup object
		$Setup = $_POST->setup;
		$Default_setup = [
			'border' 			=> true, 'sectionNames' 	=> true, 'internalNotes' 	=> true, 'choiceItems' 	=> true
		];
		$options = $_POST->options;

		// get menu object
		$menuId = $_POST->menuId;
		$menu = $this->pgObjects->getById('', $menuId, 1, 1);

        // Enhanced menu item processing with debugging
        $debugInfo = [];

        if (!empty($menu['sections'])) {
            $debugInfo['total_sections'] = count($menu['sections']);

            foreach( $menu['sections'] as $i => &$section){
                $debugInfo["section_$i"] = [
                    'section_name' => $section['name'] ?? 'unnamed',
                    'items_raw_count' => count($section['items'] ?? [])
                ];

                if ( !empty($section['items']) ) {
                    $debugInfo["section_$i"]['items_before'] = [];

                    foreach( $section['items'] as $j => &$item ){
                        $debugInfo["section_$i"]['items_before'][$j] = [
                            'type' => gettype($item),
                            'value' => is_numeric($item) ? "ID: $item" : 'object'
                        ];

                        // NEW: Check if item is just an ID (integer)
                        if (is_numeric($item)) {
                            $debugInfo["section_$i"]["item_$j"] = "Fetching full object for ID: $item";

                            // Fetch the full menu line item object
                            $item = $this->pgObjects->getById('inventory_menu_line_item', $item, 1);

                            if ($item && isset($item['item']['category'])) {
                                $debugInfo["section_$i"]["item_$j"] .= " -> Category: " . $item['item']['category'];
                            } else {
                                $debugInfo["section_$i"]["item_$j"] .= " -> NO CATEGORY FOUND";
                            }
                        }

                        // EXISTING: Enhance the recipe if item object exists
                        if ( !empty($item['item'] ) ) {
                            $item['item'] = $this->get_full_recipe($item['item']);
                        }
                    }
                }
            }

        }

		// get menu sections
		$menuSections = $menu['sections'];

		// order menu sections
		$ordered_menuSections = __::sortBy($menuSections, function ($section) {

			return $section['sortId'];
		});

        ///test isolation
        // $ordered_menuSections = [$ordered_menuSections[2]];

		function checkSetup($s, $defaultS){

			foreach ($s as $optionName => $optionValue) {

				$defaultS[$optionName] = $optionValue;
			}

			return $defaultS;
		}

        function map_item_recipe_beo( &$MergeTagHTML, &$item, $selected, $beo, $level){

            $servingStyle = ( $level == 0 && isset( $beo['beo_servingstyle']) ) ? strtoupper( $beo['beo_servingstyle'] ) : '-';
            $quantity = ( $level == 0 && isset( $beo['beo_qty'] ) && $beo['beo_qty'] !== 0 ) ? $beo['beo_qty'] : '';
            $notes = ( $level == 0 && !empty($beo['beo_note']) ) ? $beo['beo_note'] : null;
            $ingredients = $beo['beo_ingredients'];
            $lineItemName = ( !empty($item['inventory_group'])) ? $item['inventory_group']['name'] : $item['name'];

            if ($debug) {

                echo " 5146 | selected « \n ";
                echo "<pre> " . var_dump( json_encode($selected , JSON_PRETTY_PRINT) ) . "</pre>";
                echo "<br>";
                var_dump( " 5146 :: lineItemName ", $lineItemName );
                echo "\n";

            }


            // $item['choice'] = array_filter($item['choice'], function($value) {
            //     return ($value !== null && $value !== false && $value !== '');
            // });

            // if ( isset($item['choice']) && empty($item['choice']) ) {
            //     // echo "has choice options available, but none selected";
            //     // echo "<br>";
            //     $lineItemName .= $lineItemName . ' <span class="font-size:10px;"><em> (No selections made)</em></span>';

            // }


        // echo " 5147 | notes :: ", var_dump($notes);
            ///setup BEO details from incoming $beo arg. top level will be in $beo itself, ing and comps will be in $beo['beo_ingredients'] array
            if (!empty($ingredients)) {

                $matchedIngredients = __::find($ingredients, function($ing) use ($lineItemName) {
                    if (str_contains($ing['name'], $lineItemName)) { return $ing; }
                });

                if ($matchedIngredients) {
                    $servingStyle = $matchedIngredients['beo_servingstyle'];
                    $quantity = $matchedIngredients['beo_qty'];
                    $notes = $matchedIngredients['beo_note'];
                }

            }

            ///@TODO-If an item has choice options and none have been selected, then concat to display name 'Needs choice selections'
            // $itemName = '—— › • ¬ ';
            switch (intval($level)) {
                case 0:
                    $formattedName = '<strong>'. $lineItemName . '</strong>';
                    $levelLPadOffset = 0;
                    $servingStyle = $servingStyle;
                    $quantity =  $quantity;
                    $borderTop = 'border-top:1px solid #dfdfdf;';
                    break;
                case 1:
                    $formattedName = '<span style="margin: 0 10px 0 0;"><strong> - </strong></span>' . $lineItemName;
                    $levelLPadOffset = ($level * 15);
                    $borderTop = 'border-top:1px solid #dfdfdf;';
                    break;
                case 2:
                    $formattedName = '<span style="margin: 0 10px 0 0; font-size: 10px;"> • </span>' . $lineItemName;
                    $levelLPadOffset = ($level * 20);
                    $borderTop = 'border-top:1px solid #dfdfdf;';
                    break;
                case 3:
                    $formattedName = '<span style="margin: 0 10px 0 0;"> › </span>' .  $lineItemName;
                    $levelLPadOffset = ($level * 25);
                    $borderTop = 'border-top:1px solid #dfdfdf;';
                    break;
                case 4:
                    $formattedName = '<span style="margin: 0 10px 0 0;"> - </span>' .  $lineItemName;
                    $levelLPadOffset = ($level * 30);
                    $borderTop = 'border-top:1px solid #dfdfdf;';
                    break;
                case 5:
                    $formattedName = '<span style="margin: 0 10px 0 0; font-size: 10px;"> • </span>' .  $lineItemName;
                    $levelLPadOffset = ($level * 35);
                    $borderTop = 'border-top:1px solid #dfdfdf;';
                    break;
                case 6:
                    $formattedName = '<span style="margin: 0 10px 0 0;"> › </span>' .  $lineItemName;
                    $levelLPadOffset = ($level * 40);
                    $borderTop = 'border-top:1px solid #dfdfdf;';
                    break;
            }

            /// top level name, serving style, QTY
            $MergeTagHTML .= '<tr>';

                $MergeTagHTML .= '<td style="padding: 3px 0 3px '. $levelLPadOffset .'px; '. $borderTop .'">'. $formattedName .'</td>';

                $MergeTagHTML .= '<td style="padding: 3px 0 3px 0; color: black; text-align:right; '. $borderTop .'">' . $servingStyle . '</td>';

                $MergeTagHTML .= '<td style="padding: 3px 0 3px 0; text-align: right; '. $borderTop .'">' . $quantity. '</td>';

            $MergeTagHTML .= '</tr>';

            $description = !empty( $item['description'])
                ? $item['description']
                : (!empty($item['inventory_group']['description']) ? $item['inventory_group']['description'] : null);

            if ( $description ) {

                $MergeTagHTML .= '<tr>';

                    $MergeTagHTML .= '<td colspan="3" style="padding: 0 0 0 '. ($levelLPadOffset + ($level * 10)) .'px; font-size: 12px;"><em>' . $description  . '</em></td>';

                $MergeTagHTML .= '</tr>';

            }

            if ( $notes ) {

                $MergeTagHTML .= '<tr>';

                    $MergeTagHTML .= '<td colspan="3" style="padding: 0 0 5px '. ($levelLPadOffset + ($level * 10)) .'px; color: red;">' . strip_tags( $notes ) . '</td>';

                $MergeTagHTML .= '</tr>';

            }

            if ($debug) {
                var_dump( " 5146 :: lineItemName ", $lineItemName );
                echo "\n";
                var_dump( " are there CHOICE selections ", !empty($selected) );
                echo "\n";
            }

            ///Any and all choice selections are stored as flat list on lineitem at ['choices']
            if ( !empty($selected) ) {


                if ($debug) {
                    var_dump( " 5146 :: lineItemName ", $lineItemName );
                    echo "\n";
                    var_dump( " does this item have billable item INFO ", !empty($item['items']) );
                    echo "\n";
                }

                if ( !empty($item['items']) ) {


                    // echo " 5257 | selected « \n ";
                    // echo "<pre> " . var_dump( json_encode($selected , JSON_PRETTY_PRINT) ) . "</pre>";
                    // echo "<br>";

                    // echo " 5146 | item « \n ";
                    // echo "<pre> " . var_dump( json_encode($item , JSON_PRETTY_PRINT) ) . "</pre>";
                    // echo "<br>";

                    foreach ($item['items'] as &$ingr) {

                        // echo " item items as ingr :: ", var_dump($ingr);

                        $selection = __::find($selected, function($sel) use ($ingr) {
                            return $sel['item'] == $ingr['id'];
                        });

                        if ($selection) {
                            if ($debug) {

                                echo " was there a selection match found? \n ";
                                echo "<pre> " . var_dump( json_encode((!!$selection) , JSON_PRETTY_PRINT) ) . "</pre>";
                                echo "<br>";
                                echo " 5283 | selection :: ", var_dump($selection);
                            }
                            map_item_recipe_beo($MergeTagHTML, $selection, null, $beo, $level + 1);
                        }


                        unset($ingr);

                    }

                }

            }

            if ($debug) {

                var_dump( " 5146 :: lineItemName ", $lineItemName );
                echo "does this item have CHOICE INFO « \n ";
                echo "<pre> " . var_dump( json_encode(isset($item['choice']) , JSON_PRETTY_PRINT) ) . "</pre>";
                echo "<br>";
            }

            if ( isset($item['choice']) ){

                ///@TODO bug in Invoice/Menu builder that is adding falsy vals for 'none' and no selections
                $item['choice'] = array_filter($item['choice'], function($value) {
                    return ($value !== null && $value !== false && $value !== '');
                });

                if ( !empty($item['choice']) ) {

                    foreach ( $item['choice'] as &$choice) {
                        if ($debug) {
                            echo " 5283 | choice :: ", var_dump($choice);
                        }
                        map_item_recipe_beo($MergeTagHTML, $choice, null, $beo, $level + 1);

                        unset($choice);

                    }
                }

            }

            unset($level);
            unset($item);
            unset($matchedIngredients);

            return $MergeTagHTML;

        }

        function gatherSelectedChoiceInfo($selection, $billable_info){

            foreach($selection as $index => &$itemSelected){

                $selectedBillable = __::find($billable_info, function($binfo) use ($itemSelected) {
                    return $binfo['id'] == $itemSelected['item'];
                });

                $itemSelected['choice'] = __::map($itemSelected['choice'], function($choiceSelected) use (&$itemSelected, $selectedBillable) {

                        $choice_billableInfo = __::find($selectedBillable['choices'], function($selbinfo) use ($choiceSelected) {
                            return $selbinfo['id'] == $choiceSelected;
                        });

                        return $choice_billableInfo;
                });

            }

            return $selection;
        }

		$Default_setup = checkSetup($Setup, $Default_setup);

		$borderStyling = '';
		$MergeTagHTML = '';
		$categories = array();

        $categories = $this->pgObjects->where(
            'beo_templates'
            , array(
                'name' => 'Infinity Menu BEO'
            )
            , ''
            , 0
        )[0]['categories'];

		if (
			!empty($options)
			&& is_array($options)
		) {

			foreach ($options as $cat) {

				array_push($categories, intval($cat));
			}
		}

		foreach ($ordered_menuSections as $i => &$section) {

			$itemsList = array();

			if (!empty($section['items'])) {

				foreach ($section['items'] as $index => &$item) {

                    $itemCategory = $item['item']['category'];

					if (
						empty($categories)
					) {

						array_push($itemsList, $item);
					} else {

						if (
							in_array($itemCategory, $categories)
						) {

							array_push($itemsList, $item);
						}
					}
                    unset($item);
				}


			}

			if (!empty($itemsList)) {

				$sectionItems 	= $section['items'];
				$internalNotes 	= $section['details'];

				$MergeTagHTML .= '<div style="' . $borderStyling . ' margin-bottom: 20px;">';

				// SECTION NAMES
				if ($Default_setup['sectionNames'] == true) {

					$sectionTime = '';

					if (!empty($section['from'])) {

						$sectionTimeFrom = new DateTime($section['from'], new DateTimeZone('UTC'));
						$sectionTimeTo = new DateTime($section['to'], new DateTimeZone('UTC'));

						$sectionTimeFrom->setTimeZone(new DateTimeZone($Setup->tz));
						$sectionTimeTo->setTimeZone(new DateTimeZone($Setup->tz));

						$sectionTime = ', ' . $sectionTimeFrom->format('m/d/Y g:i A') . ' to ' . $sectionTimeTo->format('m/d/Y g:i A');
					}

					$MergeTagHTML .= '<h3>' . $section['name'] . $sectionTime . '</h3>';
				}

				// INTERNAL NOTES
				if ($Default_setup['internalNotes'] == true) {

					if (!empty($section['details'])) {

						$MergeTagHTML .= '<div style="margin-top: 5px; color: red;">' . $section['details'] . '</div>';
					}
				}

				$MergeTagHTML .= '<table class="medium-editor-table" style="width:100%; border-collapse: collapse;">';

				$MergeTagHTML .= '<thead>';

                ///removed background-color: #F2F3F4;  for printer output
				$MergeTagHTML .= '<tr style="text-align:left !important; font-size:12px;font-weight:bold; color:#B9BEC4;">';
                    ///removed color:#B9BEC4 from headers for printer output;
                    $MergeTagHTML .= '<th style="color:#B9BEC4; width: 70%; text-align:left !important;">Item</th>';

                    $MergeTagHTML .= '<th style="color:#B9BEC4; width: 20%; text-align:right !important;">Serving Style</th>';

                    $MergeTagHTML .= '<th style="color:#B9BEC4; width: 10%; text-align:right !important;">Qty</th>';

				$MergeTagHTML .= '</tr>';

				$MergeTagHTML .= '</thead>';

				$MergeTagHTML .= '<tbody>';

                ///loop through each line item on the menu
				foreach ($itemsList as $i => &$lineItem) {

                    ///get BEO options for line item
                    $lineItem_beo = $this->pgObjects->getById('', $lineItem['id']
                    , [
                        'beo_qty' => true
                        , 'beo_servingstyle' => true
                        , 'beo_note' => true
                        , 'name' => true
                        , 'beo_ingredients' => true
                    ], 2);

                    $beo_details = array(
                        'beo_servingstyle' => $lineItem['beo_servingstyle']
                        , 'beo_quantity' => $lineItem['beo_qty']
                        , 'beo_note' => $lineItem['beo_note']
                        , 'beo_ingredients' => $lineItem['beo_ingredients']
                        , 'beo_qty' => $lineItem['beo_qty']
                    );

                    $lineItemInfo = array(
                        'id' => $lineItem['id']
                        , 'beo_servingstyle' => $lineItem['beo_servingstyle']
                        , 'beo_quantity' => $lineItem['beo_qty']
                        , 'beo_note' => $lineItem['beo_note']
                        , 'beo_ingredients' => $lineItem['beo_ingredients']
                        , 'itemInfo' => array(
                            'id' => $lineItem['item']['id']
                            , 'name' => $lineItem['item']['name']
                            , 'items' => count($lineItem['item']['items'])
                        )
                        , 'choices' => $lineItem['choices']
                    );

                    ///choice selections from Invoice/Menu Builder are saved as int vals
                    ///gatherSelectedChoiceInfo fn maps selected choice options to their billable_info_group data
                    if ( !empty($lineItem['choices'][0]['choice']) ){

                        $lineItem['choices'][0]['choice'] = gatherSelectedChoiceInfo($lineItem['choices'][0]['choice'], $lineItem['item']['items']);

                    }


                    if (!empty($lineItem['item'])) {

                        // echo " 5413 | selections « \n ";
                        // echo "<pre> " . var_dump( json_encode($lineItem['choices'][0]['choice'] , JSON_PRETTY_PRINT) ) . "</pre>";
                        // echo "<br>";
                        // continue;

                        map_item_recipe_beo( $MergeTagHTML, $lineItem['item'], $lineItem['choices'][0]['choice'], $beo_details, 0);

                    }

                    unset($lineItem);

				}

				$MergeTagHTML .= '</tbody>';

				$MergeTagHTML .= '</table></div>';

			}

            unset($section);

		}
        if ($debug) {
            die();
        }

        // Add debug info to browser console
        $MergeTagHTML .= '<script>';
        $MergeTagHTML .= 'console.log("=== BEO DEBUG START ===");';
        $MergeTagHTML .= 'console.log("BEO Menu ID:", ' . json_encode($menuId) . ');';
        $MergeTagHTML .= 'console.log("BEO Categories (template):", ' . json_encode($categories) . ');';
        $MergeTagHTML .= 'console.log("BEO Debug Info:", ' . json_encode($debugInfo) . ');';
        $MergeTagHTML .= 'console.log("BEO Final HTML Length:", ' . strlen($MergeTagHTML) . ');';
        $MergeTagHTML .= 'console.log("=== BEO DEBUG END ===");';
        $MergeTagHTML .= '</script>';

        return $this->sendData($MergeTagHTML, 1);
	}

	function generateChoiceItemsListHTML()
	{

		// Set variables

		$item = $_POST->item;

		$HTML = '';

		// Get objects
		$item = $this->pgObjects->getById('', $item->id, 1, 1);

		$ingredientNames = $item['item']['ingredient_names'];

		$HTML .= '<div class="text-muted">';

		$HTML .= '<ul style="padding-left: 20px !important;">';

		foreach ($ingredientNames as $i => $c) {

			$HTML .= '<li>' . $c . '</li>';
		}

		$HTML .= '</ul>';

		$HTML .= '</div>';

		return $this->sendData($HTML, 1);
	}

	function getMenuItemCategories()
	{

		$inventory_billable_categories = $this->pgObjects->getAll('inventory_billable_categories', array(
			'name' => true,
			'tax_rates' => true,
			'default_tax_rate' => true,
			'surcharges' => true
		));

		$inventory_billable_combination_categories = $this->pgObjects->getAll('inventory_billable_combination_categories', array(
			'name' => true,
			'tax_rates' => true,
			'default_tax_rate' => true,
			'surcharges' => true
		));

		$tax_rates = $this->pgObjects->getAll('tax_rates', 1);
		$surcharges = $this->pgObjects->getAll('surcharges', 1);

		$data = array(
			'inventory_billable_categories' => $inventory_billable_categories,
			'inventory_billable_combination_categories' => $inventory_billable_combination_categories,
			'tax_rates' => $tax_rates,
			'surcharges' => $surcharges
		);

		return $this->sendData($data, 1);
	}

	function getMenuLineItemPricing()
	{

		// Set variables
		$menuId = $_POST->menuId;
		$workOrderId = $_POST->workOrderId;

		// Get objects
		$workOrder = $this->pgObjects->getById('', $workOrderId, 1, 1);

		$discountsQuery = array(
			'menu' => $menuId
		);

		if (!empty($workOrder['main_object']['main_contact'])) {

			$discountsQuery['menu'] = array(
				'type' => 'or',
				'values' => [$menuId, $workOrder['main_object']['main_contact']]
			);
		}

		$discounts = $this->pgObjects->where('discounts', $discountsQuery);
		$tax_rates = $this->pgObjects->getAll('tax_rates', 1);

		$data = array(
			'discounts' => $discounts,
			'tax_rates' => $tax_rates
		);

		return $this->sendData($data, 1);
	}

	function getMenuBudgetTypesAndItemReservation()
	{

		// Set variables
		$objId = $_POST->objId;
		$menuId = $_POST->menuId;

		// Get objects
		$obj = $this->pgObjects->getById('', $objId, 1, 1);

		$menu_budget_types = $this->pgObjects->getAll('menu_budget_types');
		$item_reservation = $this->pgObjects->where('item_reservation', array('menu' => $menuId), 1);

		$discountsQuery['menu'] = $menuId;
		if ($obj['main_object'] && $obj['main_object']['main_contact']) {

			$discountsQuery['menu'] = array(
				'type' => 'or',
				'values' => [$menuId, $obj['main_object']['main_contact']]
			);
		}

		$discounts = $this->pgObjects->where('discounts', $discountsQuery);

		$data = array(
			'discounts' => $discounts,
			'item_reservation' => $item_reservation,
			'menu_budget_types' => $menu_budget_types
		);

		return $this->sendData($data, 1);
	}

	function createContact()
	{

		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		// Set variables
		$obj = $_POST->obj;
		$templateObj = $_POST->templateObj;
		$state = $_POST->state;
		$tagExistingProjects = $_POST->tagExistingProjects;

		// Initialize variables
		$company = array();
		$contactInfoObjs = array();

		if ($obj->basicData->company->value == 0) {

			$companyCreationObj = array(
				'name' => $obj->company,
				'tagged_with' => $templateObj->tagged_with,
				'manager' => $obj->basicData->companyManager->value,
			);

			// Create the company
			$company = $this->pgObjects->create('companies', $companyCreationObj);
		} else {

			if ($obj->basicData->company->value > 0) {

				$company['id'] = $obj->basicData->company->value;

				$newManager = false;
				$companyObj = $this->pgObjects->getById('companies', $obj->basicData->company->value, 1, 1);
				$manager = $companyObj['manager'];
				if ($manager != $obj->basicData->companyManager->value) {
					$newManager = true;
				}
				if ($newManager) {
					$companyObj['manager'] = $obj->basicData->companyManager->value;
					$contact = $this->pgObjects->update('companies', $companyObj);
				}
			}
		}

		// DO NOT REMOVE -- this is needed for a specific client requirement
		if ($this->appConfig['instance'] === 'foundation_group') {

			if (is_array($templateObj->tagged_with)) {
				array_push($templateObj->tagged_with, 1763496);
			} else {
				$templateObj->tagged_with = [1763496];
			}
		}

		$contactCreationObj = array(
			'fname' => $obj->basicData->fname->value,
			'lname' => $obj->basicData->lname->value,
			'manager' => $obj->manager->id,
			'sales_person' => $obj->salesperson->id,
			'type' => $obj->basicData->type->value,
			'company' => intval($company['id']),
			'tagged_with' => $templateObj->tagged_with
		);

		// Create the contact
		$contact = $this->pgObjects->create('contacts', $contactCreationObj);

		foreach ($obj->contactInfo->other as $contactInfoTypeID => $contactInfo) {

			if (!empty($contactInfoTypeID) && !empty($contactInfo->value)) {

				$contactInfoOtherObj = array(
					'info' => $contactInfo->value,
					'type' => $contactInfoTypeID,
					'object_id' => intval($contact['id']),
					'object_type' => 'contacts',
					'is_primary' => 'yes'
				);

				array_push($contactInfoObjs, $contactInfoOtherObj);
			}
		}

		if (!empty($obj->contactInfo->address->type)) {

			$contactInfoAddressObj = array(
				'info' => $obj->contactInfo->address->street->value,
				'street' => $obj->contactInfo->address->street->value,
				'city' => $obj->contactInfo->address->city->value,
				'state' => $obj->contactInfo->address->state->value,
				'zip' => $obj->contactInfo->address->zip->value,
				'country' => $obj->contactInfo->address->country->value,
				'type' => intval($obj->contactInfo->address->type),
				'object_id' => intval($contact['id']),
				'object_type' => 'contacts',
				'is_primary' => 'yes'
			);

			array_push($contactInfoObjs, $contactInfoAddressObj);
		}

		// Create contact info objects
		$contactInfoIds = array();
		foreach ($contactInfoObjs as $contactInfoObj) {
			$contactInfo = $this->pgObjects->create('contact_info', $contactInfoObj);
			array_push($contactInfoIds, $contactInfo['id']);
		}

		$contact['contact_info'] = $contactInfoIds;

		// Update contact with contact info objects
		$contact = $this->pgObjects->update('contacts', $contact);

		if ($state->onProject === true) {

			$groupUpdateObj = array(
				'id' => $state->pageObject->id,
				'main_contact' => $contact['id'],
				'main_client' => $company['id']
			);

			// Update project
			$project = $this->pgObjects->update('groups', $groupUpdateObj);
		}

		// Tag existing projects
		if ($tagExistingProjects) {

			$ids = array();

			// Get existing projects for this company
			$projects = $this->pgObjects->where('groups', array('group_type' => 'Project', 'parent' => intval($company['id'])));
			$projectIds = __::pluck($projects, 'id');

			array_push($ids, $projectIds);

			if ($this->appConfig['instance'] === 'rickyvoltz' || $this->appConfig['instance'] === 'foundation_group') {

				$childrenQuery['tagged_with'] = array(
					'type' => 'any',
					'values' => $projectIds
				);

				// Get existing children sets of project
				$children = $this->pgObjects->where('#.', $childrenQuery);
				$childrenIds = __::pluck($children, 'id');

				array_push($ids, $childrenIds);
			}

			foreach ($ids as $id) {

				$this->pgObjects->tagObject(
					$id,
					intval($contact['id']),
					'shared_with',
					true
				);
			}
		}

		return $this->sendData($contact, 1);
	}

	function getInvoiceList()
	{

		// Set variables
		$menuId = $_POST->menuId;

		$discounts = $this->pgObjects->where('discounts', array('menu' => $menuId));

		$search = array(
			'id' => array(
				'type' => 'or',
				'values' => array_keys($discounts)
			)
		);

		$inventory_billable_categories = $this->pgObjects->where('inventory_billable_categories', $search);
		$inventory_billable_combination_categories = $this->pgObjects->where('inventory_billable_combination_categories', $search);

		$categories = array_merge($inventory_billable_categories, $inventory_billable_combination_categories);

		$data = array(
			'discounts' => $discounts,
			'categories' => $categories
		);

		return $this->sendData($data, 1);
	}

	public function getAllInstances($json = 1)
	{

		$paged = true;
		$pageLength = $_POST->paged->pageLength;
		$sortCol = $_POST->paged->sortCol;
		$sortDir = $_POST->paged->sortDir;
		$offset = $_POST->paged->page;

		if (count($_POST->paged->search) > 0) {

			$queryObj = array();

			foreach ($_POST->paged->search as $searchObj) {

				$queryObj[$searchObj->searchField] = array(
					'value' => $searchObj->searchTerm,
					'type' => 'contains'
				);
			}
		}

		$objs = $this->pgObjects->where('instances', $queryObj, '', 2, true, $offset, $sortCol, $sortDir, $pageLength);

		$ret = array(
			"draw" => $_POST->queryObj->paged->paged,
			"recordsFiltered" => $objs[0]['full_count'],
			"recordsTotal" => $objs[0]['full_count'],
			"data" => $objs
		);

		return $this->sendData($ret, $json);
	}

	public function getInstancesBy($objectType = null, $queryObj = null, $json = 1, $getChildObjs = 0)
	{

		$additionalClause = '';

		if (isset($_POST->objectType)) {
			$objectType = $_POST->objectType;
			$queryObj = $this->objToArr($_POST->queryObj);

			if ($queryObj['groupBy']) {
				$additionalClause = "group by object_data->>'" . $queryObj['groupBy'] . "'";
				unset($queryObj['groupBy']);
			}
		}

		if (isset($_POST->getChildObjs)) {
			if (is_numeric($_POST->getChildObjs)) {
				$getChildObjs = intval($_POST->getChildObjs);
			} elseif (is_object($_POST->getChildObjs)) {
				$getChildObjs = $this->objToArr($_POST->getChildObjs);
			}
		}

		foreach ($queryObj as $key => $val) {

			if (is_numeric($val)) {

				$queryObj[$key] = intval($val);
			}
		}

		if ($queryObj['paged']) {

			$paged = true;
			$page = $queryObj['paged']['page'];
			$sortCol = $queryObj['paged']['sortCol'];
			$sortDir = $queryObj['paged']['sortDir'];
			$limit = $queryObj['paged']['pageLength'];
			$sum = $queryObj['paged']['sum'];
			unset($queryObj['paged']);
		}

		if ($objectData = $this->pgObjects->whereAcrossInstances($objectType, $queryObj, $additionalClause, $getChildObjs, $paged, $page, $sortCol, $sortDir, $limit, $sum)) {

			if ($_POST->queryObj->paged) {

				$ret = array(
					"draw" => $_POST->queryObj->paged->paged,
					"recordsFiltered" => $objectData[0]['full_count'],
					"recordsTotal" => $objectData[0]['full_count'],
					"data" => $objectData
				);

				return $this->sendData($ret, $json);
			} else {

				return $this->sendData($objectData, $json);
			}
		} else {

			return $this->sendData([], $json);
		}
	}

	public function getInstanceBlueprint($json = 1)
	{

		$ret = $this->pgObjects->getInstanceBlueprint('instances');

		return $this->sendData($ret, $json);
	}

	private function getInstanceMainContact($instanceId)
	{

		$instance = $this->pgObjects->where('instances', array('instance' => $_REQUEST['instance']))[0];

		$this->pgObjects->setInstance('voltzsoftware');

		$contactObj = $this->getObjectById('contacts', $instance['main_contact'], false);

		return $contactObj;
	}

	public function getIPAddress($json = 1)
	{

		if ($_SERVER['cf-connecting-ip']) {
			$connectingIP = $_SERVER['cf-connecting-ip'];
		} elseif ($_SERVER['CF-Connecting-IP']) {
			$connectingIP = $_SERVER['CF-Connecting-IP'];
		} elseif ($_SERVER['CF-CONNECTING-IP']) {
			$connectingIP = $_SERVER['CF-CONNECTING-IP'];
		} else {
			$connectingIP = $_SERVER['REMOTE_ADDR'];
		}

		return $this->sendData($connectingIP, $json);
	}

	public function getObject($tableName = null, $value = null, $column = 'id', $json = 1)
	{

		if ($tableName == null) {
			$tableName = $_POST->type;
		}

		if ($value == null) {
			$value = $_POST->value;
		}

		if ($_POST->column) {
			$column = $_POST->column;
		}

		if ($value == intval($value)) {
			$value = intval($value);
		}

		$object = $this->pgObjects->where($tableName, array($column => $value));

		return $this->sendData($object, $json);
	}

	public function getObjectById($tableName = null, $value = null, $json = 1)
	{

		if ($tableName == null) {
			$tableName = $_POST->type;
		}

		if ($value == null) {
			$value = $_POST->value;
		}

		if (isset($_POST->childObjs)) {
			$childObjs = intval($_POST->childObjs);
		}

		if (
			is_object($_POST->childObjs)
			and $_POST->childObjs->selectionObj == true
		) {
			$childObjs = $this->objToArr($_POST->childObjs);
			unset($childObjs['selectionObj']);
		}

		// Include archive option
		$includeArchive = false;
		if ($_POST->includeArchive) {
			$includeArchive = true;
		}

		$object = $this->pgObjects->getById($tableName, $value, $childObjs, $includeArchive);

		return $this->sendData($object, $json);
	}

	public function getObjectBlueprint($objectType = null, $accessLevel = 0, $json = 1)
	{

		if ($objectType == null) {
			$objectType = $_POST->objectType;
		}

		if (isset($_POST->accessLevel)) {
			$accessLevel = $_POST->accessLevel;
		}

		if (isset($_POST->getOptions)) {
			$getOptions = $_POST->getOptions;
		} else {
			$getOptions = false;
		}

		if (isset($_POST->getSetObj)) {
			$getSetObj = $_POST->getSetObj;
		} else {
			$getSetObj = false;
		}

		$blueprint = $this->pgObjects->getBlueprint($objectType, $getOptions, $getSetObj);

		return $this->sendData($blueprint, $json);
	}

	public function getObjectBlueprints($json = 1)
	{

		/*
		$blueprintsList = $this->obj->getJust('object_blueprints', '1 = 1', ['object_type', 'access_level']);
		$ret = [];

		if(is_array($blueprintsList)){
			foreach($blueprintsList as $blueprintItem){
				$ret[] = $this->obj->getBlueprint($blueprintItem['object_type'], false);
			}
		}
*/
		$ret = $this->pgObjects->getAllBlueprints('object_blueprints');

		return $this->sendData($ret, $json);
	}

	public function getObjectsWhereBy($objectType, $queryObj, $json = 1, $getChildObjs = 0)
	{

		$additionalClause = '';

		if (isset($_POST->objectType)) {
			$objectType = $_POST->objectType;
			$queryObj = $this->objToArr($_POST->queryObj);

			if ($queryObj['groupBy']) {
				$additionalClause = "group by object_data#>>'{" . $queryObj['groupBy'] . "}'";
				unset($queryObj['groupBy']);
			}
		}

		if (isset($_POST->getChildObjs)) {
			if (is_numeric($_POST->getChildObjs)) {
				$getChildObjs = intval($_POST->getChildObjs);
			} elseif (is_object($_POST->getChildObjs)) {
				$getChildObjs = $this->objToArr($_POST->getChildObjs);
			}
		}

		foreach ($queryObj as $key => $val) {

			if (is_numeric($val)) {

				$queryObj[$key] = intval($val);
			}
		}

		if ($queryObj['paged']) {

			$paged = true;
			$page = $queryObj['paged']['page'];
			$sortCol = $queryObj['paged']['sortCol'];
			$sortDir = $queryObj['paged']['sortDir'];
			$limit = $queryObj['paged']['pageLength'];
			$sum = $queryObj['paged']['sum'];
			unset($queryObj['paged']);
		}

		if ($objectData = $this->pgObjects->whereBy($objectType, $queryObj, $additionalClause, $getChildObjs, $paged, $page, $sortCol, $sortDir, $limit, $sum)) {

			if ($_POST->queryObj->paged) {

				$ret = array(
					"draw" => $_POST->queryObj->paged->paged,
					"recordsFiltered" => $objectData[0]['full_count'],
					"recordsTotal" => $objectData[0]['full_count'],
					"data" => $objectData
				);

				return $this->sendData($ret, $json);
			} else {

				return $this->sendData($objectData, $json);
			}
		} else {

			return $this->sendData([], $json);
		}
	}

	public function getObjectsWhere($objectType = null, $queryObj = null, $json = 1, $getChildObjs = 0)
	{

		$additionalClause = '';

		if (isset($_POST->objectType)) {
			$objectType = $_POST->objectType;
			$queryObj = $this->objToArr($_POST->queryObj);

			if ($queryObj['groupBy']) {
				$additionalClause = "group by object_data->>'" . $queryObj['groupBy'] . "'";
				unset($queryObj['groupBy']);
			}
		}

		if (isset($_POST->getChildObjs)) {
			if (is_numeric($_POST->getChildObjs)) {
				$getChildObjs = intval($_POST->getChildObjs);
			} elseif (is_object($_POST->getChildObjs)) {
				$getChildObjs = $this->objToArr($_POST->getChildObjs);
			}
		}

		if (isset($_POST->getJust)) {
			$getJust = $this->objToArr($_POST->getJust);
		} else {
			$getJust = array();
		}

		if (!empty($queryObj['paged'])) {

			$paged = true;
			$page = $queryObj['paged']['page'];
			$sortCol = $queryObj['paged']['sortCol'];
			$sortDir = $queryObj['paged']['sortDir'];
			$limit = $queryObj['paged']['pageLength'];
			$sum = $queryObj['paged']['sum'];
			unset($queryObj['paged']);

			if ($queryObj['paged']['sortCast']) {
				$sortCast = $queryObj['paged']['sortCast'];
			} else {
				$sortCast = 'string';
			}
		}

		// Check for searching by name of a linked obj
		if (!empty($queryObj['search'])) {

			if (is_array($queryObj['search']['fields'])) {

				$bp = $this->pgObjects->getBlueprint(
					$objectType,
					false
				);

				$searchByEdge = false;

				foreach ($queryObj['search']['fields'] as $i => $key) {

					if ($bp[$key]['type'] === 'objectId' || $bp[$key]['type'] === 'objectIds') {

						$field = ['name'];

						if ($bp[$key]['objectType'] == 'contact_info') {

							$field = ['info'];
						}

						$searchByEdge = true;

						$childObjType = '';

						if ($bp[$key]['searchAny'] === true) {

							$childObjType = 'any';
						} else {

							$childObjType = $bp[$key]['objectType'];
						}

						$relatedObjs = $this->pgObjects->where(
							$childObjType,
							[
								'search' => [
									'fields' => $field, 'type' => 'or', 'value' => $queryObj['search']['value']
								]
							],
							'',
							[
								'name' => true
							]
						);

						if (is_array($relatedObjs)) {

							$relatedObjs = __::pluck(
								$relatedObjs,
								'id'
							);
						} else {

							$relatedObjs = [];
						}

						$queryType = 'or';

						if ($bp[$key]['type'] === 'objectIds') {
							$multi = true;
						} else {
							$multi = false;
						}

						$queryObj['search']['fields'][$i] = [
							'value' => $relatedObjs,
							'key' => $key,
							'multi' => $multi
						];
					}
				}
			}
		} else {
			unset($queryObj['search']);
		}

		// Check for child obj sum calls to merge in
		$agg = false;
		if ($getChildObjs['_agg']) {

			$agg = $getChildObjs['_agg'];
			$where = (object) [
				'groupOn' => $agg['groupOn']
			];
			if ($agg['owner']) {
				$where->owner = $agg['owner'];
			}
			if ($agg['owner']) {
				$where->owner = $agg['owner'];
			}
			if (is_array($agg['where'])) {
				foreach ($agg['where'] as $key => $val) {

					$where->$key = $val;
				}
			}
			$dateField = 'date_created';
			if ($agg['dateField']) {
				$dateField = $agg['dateField'];
			}
			$start = 0;
			$end = date('Y-m-d H:i:s');
			if ($agg['dateField']) {
				$dateField = $agg['dateField'];
			}
			if (
				$queryObj[$dateField]
				&& $queryObj[$dateField]['start']
			) {
				$start = date('Y-m-d H:i:s', $queryObj[$dateField]['start']);
				$end = date('Y-m-d H:i:s', $queryObj[$dateField]['end']);
				unset($queryObj[$dateField]);
			}

			$aggs = $this->pgObjects->groupSum(
				$agg['objectType'],
				$agg['field'],
				null,
				null,
				$start,
				$end,
				0,
				null,
				'',
				$where
				// 				, $agg['dateField']
				,
				$dateField
			);
			unset($getChildObjs['_agg']);
		}

		if ($objectData = $this->pgObjects->where($objectType, $queryObj, $additionalClause, $getChildObjs, $paged, $page, $sortCol, $sortDir, $limit, $sum, $getJust, $sortCast)) {

			// Merge in child obj aggregate data
			if ($agg) {
				foreach ($objectData as $i => $obj) {

					$metric = __::find(
						$aggs,
						function ($agg) use ($obj) {

							return intval($agg['grouped']) === $obj['id'];
						}
					);

					$objectData[$i]['_agg'] = [
						'sum' => 		(float) $metric['sum'], 'avg' => 		(float) $metric['avg'], 'count' => 	(float) $metric['grouped_total']
					];
				}
			}

			// Return tags with their names
			if (is_array($getChildObjs['tagged_with'])) {

				$taggedWithIds = array();

				foreach ($objectData as $i => $obj) {

					foreach ($obj['tagged_with'] as $tagId) {

						array_push($taggedWithIds, $tagId);
					}
				}

				$taggedWithIds = array_unique($taggedWithIds);

				$tags = $this->pgObjects->getById('', $taggedWithIds, $getChildObjs['tagged_with']);

				foreach ($objectData as $i => $obj) {

					$taggedWith = array();

					foreach ($tags as $tag) {

						if (in_array($tag['id'], $obj['tagged_with'])) {

							array_push($taggedWith, $tag);
						}
					}

					$objectData[$i]['tagged_with'] = $taggedWith;
				}
			}

			if ($paged) {

				$ret = array(
					"draw" => $paged,
					"recordsFiltered" => $objectData[0]['full_count'],
					"recordsTotal" => $objectData[0]['full_count'],
					"data" => $objectData
				);

				return $this->sendData($ret, $json);
			} else {

				return $this->sendData($objectData, $json);
			}
		} else {

			return $this->sendData([], $json);
		}
	}

	public function getObjectsWhereWith($objectType = null, $queryObj = null, $childObj = null, $childObjKey = null, $json = 1)
	{

		if ($objectType == null) {

			$objectType = $_POST->objectType;
			$queryObj = $this->objToArr($_POST->queryObj);
			$childObj = $_POST->childObj;
			$childObjKey = $_POST->childObjKey;
		}

		if ($queryObj['paged']) {

			$paged = true;
			$page = $queryObj['paged']['page'];
			$sortCol = $queryObj['paged']['sortCol'];
			$sortDir = $queryObj['paged']['sortDir'];
			$limit = $queryObj['paged']['pageLength'];
			$sum = $queryObj['paged']['sum'];
			unset($queryObj['paged']);

			if ($queryObj['paged']['sortCast']) {
				$sortCast = $queryObj['paged']['sortCast'];
			} else {
				$sortCast = 'string';
			}
		}

		if ($objectData = $this->pgObjects->getWith($objectType, $queryObj, $childObj, $childObjKey, $paged, $page, $sortCol, $sortDir, $limit, $sum, $getJust, $sortCast)) {

			return $this->sendData($objectData, $json);
		} else {

			return $this->sendData(array(), $json);
		}
	}

	public function getObjNoteFeed($json = 1)
	{

		if (isset($_POST->queryObj)) {
			$queryObj = $this->objToArr($_POST->queryObj);
		} else {
			return $this->sendData(false, $json);
		}

		foreach ($queryObj as $key => $val) {

			if (is_numeric($val)) {

				$queryObj[$key] = intval($val);
			}
		}

		if ($notes = $this->pgObjects->where('notes', $queryObj, '', 1)) {

			foreach ($notes as $key => $note) {

				$notes[$key]['about'] = $this->pgObjects->where($note['type'], array('id' => intval($note['type_id'])), '', 0)[0];

				$notes[$key]['replies'] = $this->pgObjects->where('note_replies', array(
					'parent' => $note['id']
				), "ORDER BY date_created", 1);
			}

			return $this->sendData($notes, $json);
		} else {

			return $this->sendData(array(), $json);
		}
	}

	public function getOptions($json = 1)
	{

		if (isset($_POST->objectType)) {
			$objectType = $_POST->objectType;
			$field = $_POST->field;
		}

		$objectType = 'walkthroughs';
		$field = 'venue';

		$blueprint = $this->pgObjects->getBlueprint($objectType);

		return $this->sendData($blueprint[$field]['options'], $json);
	}

	public function getPayment($paymentId = null, $json = 1)
	{

		if ($paymentId == null) {
			$paymentId = $_POST->paymentId;
		}

		$payment = array($this->pgObjects->getById('payments', intval($paymentId)));

		return $this->sendData($payment, $json);
	}

	public function getPlaidBankAccount($publicToken = null, $accountId = null, $customer = null, $json = 1)
	{

		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		if ($publicToken == null) {
			$publicToken = $_POST->publicToken;
		}

		if ($accountId == null) {
			$accountId = $_POST->accountId;
		}

		if ($customer == null) {
			$customer = $_POST->customer;
		}

		$headers[] = 'Content-Type: application/json';
		$params = [
			'client_id' => '5e8cd8a2c2c21f0012583a13',
			'secret' => '4e5f258082c4b39124a602c8109ac1',
			'public_token' => $publicToken,
		];

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, "https://development.plaid.com/item/public_token/exchange");
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
		curl_setopt($ch, CURLOPT_TIMEOUT, 80);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

		if (!$result = curl_exec($ch)) {
			trigger_error(curl_error($ch));
		}
		curl_close($ch);

		$jsonParsed = json_decode($result);

		$btok_params = [
			'client_id' => '5e8cd8a2c2c21f0012583a13',
			'secret' => '4e5f258082c4b39124a602c8109ac1',
			'access_token' => $jsonParsed->access_token,
			'account_id' => $accountId
		];

		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, "https://development.plaid.com/processor/stripe/bank_account_token/create");
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($btok_params));
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
		curl_setopt($ch, CURLOPT_TIMEOUT, 80);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

		if (!$result = curl_exec($ch)) {
			trigger_error(curl_error($ch));
		}
		curl_close($ch);

		$btok_parsed = json_decode($result);

		$ret = array('id' => $btok_parsed->stripe_bank_account_token);

		$ret = (object) $ret;

		$sourceAdded = $this->addStripeSourceToCustomer($ret, $customer, false);

		return $this->sendData($sourceAdded, $json);
	}

	public function getStaffAccessLevels($json = 1)
	{

		return $this->sendData($this->staffTypes, $json);
	}

	public function getStaffDepartments($json = 1)
	{

		return $this->sendData($this->staffDepartments, $json);
	}

	public function getStripeConnectAccount($stripeId = null, $json = 1)
	{

		if ($stripId == null && $_POST->accountId) {
			$stripeId = $_POST->accountId;
		} else {
			return false;
		}

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		try {

			$customer = \Stripe\Account::retrieve($stripeId);
		} catch (Exception $e) {

			$customer = null;
		}

		return $this->sendData($customer, $json);
	}

	public function getStripeCustomer($stripeId = null, $instanceId = null, $json = 1)
	{

		if ($stripeId == null && $instanceId == null) {

			$stripeId = $_POST->stripeId;
			$instanceId = $_POST->instanceId;

			$instance = $this->pgObjects->where('instances', array('instance' => $_REQUEST['pagodaAPIKey']))[0];
			/*
var_dump(!$_POST->stripeId);
die();
*/
			if (!$_POST->stripeId) {
				/*
echo 'test';
die();
*/
				$contactId = $_POST->contactId;

				$customer = $this->createStripeCustomer($token, $contactId, 'contacts', 1);
				var_dump($customer);
				die();
				$contactObj['stripe_id'] = $customer->id;

				$stripeId = $contactObj['stripe_id'];

				$this->updateObject($contactObj, 'contacts', false, false);
			}

			if ($stripeId == null && $instanceId == null) {

				return false;
			} else {

				if ($instanceId != null) {

					$contactObj = $this->getInstanceMainContact($instanceId);

					$stripeId = $contactObj['stripe_id'];
				}
			}
		}

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		try {

			$customer = \Stripe\Customer::retrieve(
				$stripeId,
				[
					"stripe_account" => $instance['stripe_account_id'],
					'expand' => ['sources']
				]
			);

			/*
			$cards = \Stripe\Customer::allSources(
			  $stripeId,
			  ['object' => 'card', 'limit' => 10]
			);
*/

			//$customer->expand = ["sources.data"];

			return $this->sendData($customer, $json);
		} catch (Exception $e) {

			return $this->sendData(false, $json);
		}
	}

	public function getStripeCustomerTransactions($stripeId = null, $json = 1)
	{

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		if ($stripeId == null) {

			$stripeId = $_POST->stripeId;
		}

		if ($stripeId == null) {

			return false;
		}

		$transactions = \Stripe\Charge::all(["customer" => $stripeId]);

		return $this->sendData($transactions, $json);
	}

	public function getTaggedObjects($tagIds = null, $json = 1, $objectType = null)
	{

		if ($tagIds == null) {
			$tagIds = $_POST->tagIds;
		}
		if ($objectType == null and $_POST->objectType) {
			$objectType = $_POST->objectType;
		}

		foreach ($tagIds as $tagId) {

			$tagObj = $this->getObjectById('system_tags', $tagId, 0);

			$retList = [];

			foreach ($tagObj['objects'] as $objObj) {

				if ($objectType !== null) {

					if ($objObj['type'] === $objectType) {

						if ($ret = $this->pgObjects->where($objObj['type'], ['id' => intval($objObj['typeId'])], '', 1)[0]) {

							$ret['tag_obj_type'] = $objObj['type'];

							array_push($retList, $ret);
						}
					}
				} else {

					if ($ret = $this->pgObjects->where($objObj['type'], ['id' => intval($objObj['typeId'])], '', 1)[0]) {

						$ret['tag_obj_type'] = $objObj['type'];

						array_push($retList, $ret);
					}
				}
			}
		}

		return $this->sendData(array_map("unserialize", array_unique(array_map("serialize", $retList))), $json);
	}

	public function getContactInfo($contactId, $infoType, $onlyPrimary = true, $json = 1)
	{
		// **$infoType corresponds to the contact_info_types data_type property.**
		// Common data_types include 'phone', 'email', 'address', etc.

		$whereClause = [
			'object_id' => $contactId
		];
		if ($onlyPrimary) {
			$whereClause["is_primary"] = "yes";
		}

		$childObjs = [
			"id" => true, "is_primary" => true, "info" => true, "type" => true
		];

		// variables
		$contactInfoArray = $this->getObjectsWhere("contact_info", $whereClause, 0, $childObjs);
		$matchingIndexes = [];
		$returnArray = [];

		// get indexes of matching infoType
		foreach (array_column(array_column($contactInfoArray, 'type'), 'data_type') as $key => $value) {
			if ($value === $infoType) {
				array_push($matchingIndexes, $key);
			}
		}

		// build array to return
		foreach ($matchingIndexes as $i) {
			array_push($returnArray, $contactInfoArray[$i]["info"]);
		}

		return $this->sendData($returnArray, $json);
	}

	public function getAccounts($email = null, $json = 1)
	{

		if ($email == null and $_POST->email) {
			$email = $_POST->email;
		}

		$ret = [];
		$users = $this->pgObjects->whereAll(
			'users',
			array('email' => $email),
			'',
			[
				'email' => true, 'instance' => true
			]
		);

		if (is_array($users)) {
			foreach ($users as $user) {

				array_push(
					$ret,
					[
						'id' => 			$user['id'], 'email' => 	$user['email'], 'instance' => 	$user['instance']
					]
				);
			}
		}

		return $this->sendData(
			$ret,
			$json
		);
	}

	public function getUserAccounts($email = null, $json = 1)
	{

		if ($email == null and $_POST->email) {
			$email = $_POST->email;
		}

		return $this->sendData($this->pgObjects->whereAll('staff', array('email' => $email)), $json);
	}

    public function getContactsWithoutCompany($actionItems = null, $json = 1)
    {

        $instanceName = "foundation_group";
        if($appConfig['instance'] == 'rickyvoltz'){
            $instanceName = "rickyvoltz";
        }

        // Get contacts
        $contacts = $this->pgObjects->whereAll(
            'contacts',
            array('instance' => $instanceName),
            "",
            array('name' => true, 'company' => true, 'state' => true)
        );

        // add projects without companies in array object
        $contactsWithoutCompanies = [];
        foreach($contacts as $i => $contact){
            if($contact['company'] === false || $contact['company'] === null){
                $contactsWithoutCompanies[] = $contact;
            }
        }

        return $this->sendData($contacts, $json);
    }


    public function getCompaniesWithoutParent($actionItems = null, $json = 1)
    {

		if ($actionItems == null && $_POST->actionItems) {
			$actionItems = $_POST->actionItems;
		}

		$instanceName = "foundation_group";
        if($appConfig['instance'] == 'rickyvoltz'){
            $instanceName = "rickyvoltz";
        }

        // Get projects
        $projects = $this->pgObjects->whereAll(
            'groups',
            array('group_type' => "Project",
                  'instance' => $instanceName),
            "",
            array('name' => true, 'group_type' => true, 'parent' => true, 'main_contact' => true)
        );

        // add projects without companies in array object
        $projectsWithoutCompany = [];
        foreach($projects as $i => $project){
            if(($project['parent'] === false || $project['parent'] === null) && $project['main_contact'] != null){
                $projectsWithoutCompany[] = $project;
            }
        }

		//with action items
		if($actionItems){
			$projectsWithoutCompany = [];

            foreach($projectsWithoutCompany as $i => $project){
                $projectsWithoutCompany[] = $this->pgObjects->getById('', intval($project['id']),
                    [
                        'name' => true,
                        'group_type' => true,
                        'parent' => true,
                        'main_contact' => true,
                        '##Action Items' => true
                    ]
                );

			}
		}

        return $this->sendData($projectsWithoutCompany, $json);
    }

    public function getCompaniesWithoutContactCompany($json = 1)
    {

        $instanceName = "foundation_group";
        if($appConfig['instance'] == 'rickyvoltz'){
            $instanceName = "rickyvoltz";
        }

        // Get projects
        $projects = $this->pgObjects->whereAll(
            'groups',
            array('group_type' => "Project",
                'instance' => $instanceName,
                'is_template' => 0
                ),
            ""
        );

        // add projects without companies in array object
        $projectsWithoutCompany = [];
        foreach($projects as $i => $project){
            $contactFull = $this->pgObjects->getById('contacts',$project['main_contact']);
            $contact = $this->pgObjects->getById('contacts',$project['main_contact'], array('name' => true, 'company' => true));

            if($contact == null || $contact['company'] == null){
                $contactsDuplicated = $this->pgObjects->whereAll('contacts', array("name" => $contactFull['name'], 'instance' => $instanceName) );
                $correctContact = $this->pgObjects->whereAll('contacts', array("name" => $contactFull['name'], 'instance' => $instanceName, 'company' => true) );

                if(count($contactsDuplicated) > 1) {
                    $project['contact_duplicated'] = $contactsDuplicated;
                    $projectsWithoutCompany[] = $project;

                    $contactActive = __::filter($project['contact_duplicated'], function($contact) {
                        $hasCompany = $this->pgObjects->getById('contacts',$contact['id'], array('name' => true, 'company' => true));
                        return (!$hasCompany && $contact['state'] == 1);
                    });


                    if($contactActive && $correctContact) {
                        $this->pgObjects->update('contacts', array(
                            'id' => $contactActive['id'],
                            'company' => $correctContact['company']['id']
                        ));
                    }



                }
            }

        }

        return $this->sendData($projectsWithoutCompany, $json);
    }

	public function getUserAccountsNew($email = null, $json = 1)
	{

		if ($email == null && $_POST->email) {
			$email = $_POST->email;
		}

		if ($email == null && $_POST->userId) {
			$user = $this->pgObjects->getByIdAcrossInstances('users', $_POST->userId);
			$email = $user['email'];
		}

		// Get accounts
		$accounts = $this->pgObjects->whereAllCaseInsensitive(
			'users',
			array('email' => $email, 'enabled' => 1)
		);

		// Get instance names
		$instances = $this->pgObjects->whereAll(
			'instances',
			[
				'instance' => [
					'type' => 'or', 'values' => 		__::pluck(
						$accounts,
						'instance'
					)
				]
			]
		);

		foreach ($accounts as $i => $account) {

			foreach ($instances as $instance) {

				if ($instance['instance'] === $account['instance']) {

					$accounts[$i]['instanceName'] = $instance['systemName'];
				}
			}
		}

		return $this->sendData($accounts, $json);
	}

	public function fgRemoveInstanceUser($id = null, $json = 1)
	{


		if ($id == null && $_POST->id) {
			$id = $_POST->id;
		}
        ///gets user object from id that gets passed
        $user = $this->pgObjects->getByIdAcrossInstances('users', $id, 0, true);

        ///sets instance to user objs instance
        $this->pgObjects->setInstance($user['instance']);

        if ( $user['enabled'] == 0 ) {

            ///if user has already been disabled, then restore the user object and update the 'enabled' property
            $isRestored = $this->pgObjects->restore('users', $id);

            $user['enabled'] = 1;
            $this->pgObjects->update($user['object_bp_type'], $user);
            $user = $this->pgObjects->getByIdAcrossInstances('users', $id, 0, true);

            $message = "User obj in " . $user['instance'] . " Instance has been restored and enabled.";

        } else {

            /// disable user and archive the user object
            $user['enabled'] = 0;

            $this->pgObjects->update($user['object_bp_type'], $user);
            $user = $this->pgObjects->getByIdAcrossInstances('users', $id, 0, true);

            $isDeleted = $this->pgObjects->delete('users', $id);

            $message = "User obj in " . $user['instance'] . " Instance has been archived and disabled.";

        }

        $resp = array();

        $resp['message'] = $message;
        $resp['enabled'] = $user['enabled'];
        $resp['user'] = $user;

		return $this->sendData($resp, $json);
	}

	public function getVenueOptions($retType = 'name', $selected = 0)
	{

		if (isset($_REQUEST['selected'])) {
			$selected = $_REQUEST['selected'];
		}

		if (isset($_REQUEST['retType'])) {
			$retType = $_REQUEST['retType'];
		}

		if ($retType == 'name') {

			foreach ($this->inventory->venue as $statusValue => $statusName) {

				if ($statusValue == $selected) {
					return $statusName;
				}
			}
		} elseif ($retType == 'options') {

			$options = null;

			foreach ($this->inventory->venue as $statusValue => $statusName) {

				if ($statusValue == $selected) {

					$options .= '<option value="' . $statusValue . '" selected>' . $statusName . '</option>';
				} else {

					$options .= '<option value="' . $statusValue . '">' . $statusName . '</option>';
				}
			}
		}

		return $options;
	}

	public function getVenueRoomOptions($retType = 'options', $selected = 0, $venueId = null, $json = 0)
	{

		if (isset($_REQUEST['selected'])) {
			$selected = $_REQUEST['selected'];
		}

		if (isset($_REQUEST['retType'])) {
			$retType = $_REQUEST['retType'];
		}

		if (isset($_REQUEST['venue-id'])) {
			$venueId = $_REQUEST['venue-id'];
		}

		if (!empty($_REQUEST['json'])) {
			$json = $_REQUEST['json'];
			$json = (int)$json;
		}

		switch ($venueId) {

			case null:
				return false;
				break;

			case 0:
				$venueRooms = 'hardingHouseRooms';
				break;

			case 1:
				$venueRooms = 'bridgeBuildingRooms';
				break;

			case 2:
				$venueRooms = 'bellTowerRooms';
				break;

			case 3:
				$venueRooms = 'twelfthAndPorterRooms';
				break;

			default:
				return false;
		}

		if ($retType == 'name') {

			foreach ($this->inventory->$venueRooms as $statusValue => $statusName) {

				if ($statusValue == $selected) {
					return $statusName;
				}
			}
		} elseif ($retType == 'options') {

			$options = null;

			foreach ($this->inventory->$venueRooms as $statusValue => $statusName) {

				if ($statusValue == $selected) {

					$options .= '<option value="' . $statusValue . '" selected>' . $statusName . '</option>';
				} else {

					$options .= '<option value="' . $statusValue . '">' . $statusName . '</option>';
				}
			}
		} elseif ($retType == 'array') {

			foreach ($this->inventory->$venueRooms as $key => $value) {
				$retArray[$key] = $value;
			}

			return $this->sendData($retArray, $json);
		}

		return $options;
	}

	public function groupSum($queryObj = null, $json = 1)
	{

		if ($queryObj == null) {
			$objectType = $_POST->objectType;
			$field = $_POST->field;
			$dateBy = $_POST->dateBy;
			$groupBy = $_POST->groupBy;
			$groupOn = $_POST->queryObj->groupOn;
			$limit = $_POST->limit;
			$start = $_POST->queryObj->dateRange->start;
			$end = $_POST->queryObj->dateRange->end;
			$search = $_POST->search;
			$coalesce = false;
			$queryObj = $this->objToArr($_POST->queryObj);

			if ($_POST->queryObj->coalesce) {
				$coalesce = $_POST->queryObj->coalesce;
				unset($queryObj->coalesce);
				unset($_POST->queryObj->coalesce);
			}

			if ($_POST->queryObj->groupBy) {
				$groupBy = $_POST->queryObj->groupBy;
				unset($_POST->queryObj->groupBy);
			}

			unset($_POST->queryObj->dateRange);

			$queryObj = $_POST->queryObj;

			if ($groupOn) {
				$queryObj->groupOn = $groupOn;
			}

			if ($_POST->queryObj->dateField) {
				$dateField = $_POST->queryObj->dateField;
				unset($queryObj->dateField);
				unset($_POST->queryObj->dateField);
			} else {
				$dateField = 'date_created';
			}
		}

		$ret = $this->pgObjects->groupSum(
			$objectType,
			$field,
			$dateBy,
			$groupBy,
			$start,
			$end,
			0,
			$limit,
			$search,
			$queryObj,
			$dateField,
			$coalesce
		);

		$this->sendData($ret, $json);
	}

	public function logGoSquaredTransaction($transactionID, $amount, $json = 1)
	{

		require_once APP_ROOT . '/vendor/gosquared/php-sdk/main.php';

		$opts = array(
			'site_token' => 'GSN-752757-B',
			'api_key' => '2QZ2TS8N853DO629'
		);
		$GS = new GoSquared($opts);

		$opts = array(
			// if you wish to explicitly set revenue and quantity totals
			// for this transaction, specify them here. They will be used
			// instead of the default totals calculated from the items.
			'revenue' => ($amount / 100),
			'quantity' => 1,
			'referrer' => $this->instance
		);

		$transaction = $GS->Transaction($transactionID, $opts);
		// $transaction->add_items(array(
		//   array(
		// 	'name' => 'Product 1',
		// 	'price' => 1,
		// 	'quantity' => 5
		//   )
		// ));

		$response = $transaction->track();

		// Associate transaction with a user

		$person = $GS->Person($this->instance . '-' + $transactionID);
		$transaction = $person->Transaction($transactionID);
		// $transaction->add_item(array(
		//   'name' => 'Product 1',
		//   'price' => 1,
		//   'quantity' => 5
		// ));

		$response = $transaction->track();

		return true;
	}

	public function notify($json = 1, $notification = null, $sendToCurrentUser = false)
	{

		$title = 	$_POST->title;
		$details = 	$_POST->details;
		$producer = $_POST->producer;
		$color = 	$_POST->color;
		$icon = 	$_POST->icon;
		$type =		$_POST->type;
		$link = $_POST->link;
		$addToNotifyList = null;
		$updatedNotifyList = [];
		$sendToCurrentUser = !empty($_POST->sendToCurrentUser) ? true : false;

		if ($notification != null) {

			$title = $notification['title'];
			$secondaryTitle = $notification['secondaryTitle'];
			$details = $notification['details'];
			$producer = $notification['producer'];
			$color = $notification['color'];
			$icon = $notification['icon'];
			$type = $notification['type'];
			$link = $notification['link'];
			$addToNotifyList = $notification['notify'];
		}

		if ($_POST->notify) {
			$addToNotifyList = $_POST->notify;
		}

		if (
			is_null($title)
			or is_null($details)
			or is_null($producer)
			or is_null($color)
			or is_null($icon)
			or is_null($type)
			or is_null($link)
		) {
			return $this->sendData(false, $json);
		}

		// get the object
		$producerObj = $this->pgObjects->getById('', intval($producer), ['notify' => true]);

		$addToNotifyList = array_unique($addToNotifyList);

		if (is_array($_POST->notifySet)) {

			$updNotifyList = $_POST->notifySet;
		} else {

			if ($addToNotifyList) {

				$mergeList = array_merge([], $addToNotifyList);
				$updatedNotifyList = array_unique($mergeList);
			}

			$currentUser = intval($_COOKIE['uid']);
			$updNotifyList = $updatedNotifyList;
		}

		$CurrentUid = intval($_COOKIE['uid']);
		$CurrentPuid = intval($_COOKIE['p_uid']);

		// Don't create notifications for current user
		if (!$sendToCurrentUser) {
			$updNotifyList = __::filter($updNotifyList, function ($toNotify) use ($CurrentUid, $CurrentPuid) {

				return ($CurrentUid !== $toNotify
					&& $CurrentPuid !== $toNotify
				);
			});
		}

		$notificationsToCreate = [];
		$notification = [];

		// for each user in notify list
		if (is_array($updNotifyList)) {

			foreach ($updNotifyList as $i => $userToNotify) {

				// create notification obj
				$notification = [
					'title' => $title,
					'details' => $details,
					'producer' => $producer,
					'color' => $color,
					'icon' => $icon,
					'type' => $type,
					'user' => $userToNotify,
					'link' => $link,
					'is_viewed' => 0
				];

				// push to $notificationsToCreate
				array_push($notificationsToCreate, $notification);
			}
		}

		// for each user in object's notify list (listening users)
		// (not already notified)
		$secondaryNotifications = __::filter($producerObj['notify'], function ($toNotify) use ($currentUser) {

			return $currentUser !== $toNotify;
		});

		if ($type === 'mention') {

			if (is_array($secondaryNotifications)) {

				foreach ($secondaryNotifications as $i => $userToNotify) {

					if (!in_array($userToNotify, $updNotifyList)) {

						// create notification obj
						$notification = [
							'title' => $secondaryTitle,
							'details' => $details,
							'producer' => $producer,
							'color' => $color,
							'icon' => 'comments',
							'type' => 'comment',
							'user' => $userToNotify,
							'link' => $link,
							'is_viewed' => 0
						];

						// push to $notificationsToCreate
						array_push($notificationsToCreate, $notification);
					}
				}
			}
		}

		// get users and email from notification list
		$userObjs = $this->pgObjects->getById(
			'users',
			$updNotifyList,
			1
		);

		$this->pgObjects->setInstance($userObjs[0]['instance']);

		// create notifications
		$notifications = $this->pgObjects->create('notification', $notificationsToCreate, true);

		$userObjs = __::filter($userObjs, function ($user) {

			return $user['canBeNotified'] === null || $user['canBeNotified'] === true;
		});

		// Check for contacts to email in client portal
		$contactsToNotify = __::filter($userObjs, function ($user) {

			return $user['object_bp_type'] === 'contacts';
		});

		if (!empty($contactsToNotify)) {

			$tokens = $this->pgObjects->where('portal_access_token', [
				'contact' => [
					'type' => 'or', 'values' => __::pluck($contactsToNotify, 'id')
				]
			]);

			$portalUsers = $this->pgObjects->whereAll('users', [
				'id' => [
					'type' => 'or', 'values' => __::pluck($tokens, 'user')
				]
			]);

			$userObjs = array_merge($portalUsers, $userObjs);
		}

		// Don't notify users who are not enabled
		$userObjs = __::filter($userObjs, function ($user) {

			if ($user['enabled'] == 1) {
				return $user;
			} else {
				return false;
			}
		});

		// Grabbing parent object to identify context of notification
		$contextObj = $this->pgObjects->getById('', intval($producer));
		$contextName = '';

		if (isset($contextObj['name'])) {

			$contextName = ' on <strong>' . $contextObj['name'] . '</strong>';
		} else if (isset($contextObj['title'])) {

			$contextName = ' on <strong>' . $contextObj['title'] . '</strong>';
		} else {

			$contextName = '';
		}

		// send emails
		$instanceName = $this->appConfig['instance'];
		if (!empty($this->appConfig['systemName'])) {
			$instanceName = $this->appConfig['systemName'];
		}
		$mergevars = [
			'TITLE' => $title,
			'BODY' => ' <table style="font-family: sans-serif !important;" align="" border="0" cellpadding="0" cellspacing="0" width="100%" style="border-collapse: collapse;">
			      <tr>
			           <td align="center" bgcolor="#5084CC" style="padding: 5px; color: #ffffff; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold; letter-spacing:-.5px;"></td>
			      </tr>
			      <tr>
			           <td style="padding:10px 10px;">
				           <h2>' . $instanceName . '</h2>
			           </td>
			      </tr>
			      <tr>
			          <td style="padding:10px 10px;">' . $title . '<br /><br />' . $contextName . '<br/><br />' . $details . '</td>
			     </tr>
			     <tr>
			         <td style="padding:10px 10px;">
			             <br />
			             <a href="' . $link . '" style="color: #5084cc; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold;">View in Bento</a>
			         </td>
			     </tr>
			 </table>
			 ',
			'TEXT_BODY' => '' . $this->appConfig['instance'] . '

			 ' . $title . $contextName . ' ' . $details . '

			 ' . $link . '',
			'BUTTON' => 'View in Bento',
			'BUTTON_LINK' => $this->getUrl() . '/app/' . $this->appConfig['instance'],
			'INSTANCE_NAME' => $instanceName

		];

		// For Foundation Group, only send these notifications for users within the system
		// (not to portal-instance users)
		if (
			$this->appConfig['instance'] === 'foundation_group'
			&& $notification['type'] !== 'clientResponse'
		) {

			$userObjs = array_filter(
				$userObjs,
				function ($user) {

					return $user['instance'] === 'foundation_group';
				}
			);
		}

		$this->comm->sendMandrillEmail(
			__::pluck($userObjs, 'email'),
			null,
			$title,
			$mergevars,
			$email_tags,
			$producer,
			0,
			0,
			false,
			null,
			$this->appConfig['instance'],
			0,
			$contextObj['tagged_with']
		);

		return $this->sendData(true, $json);
	}

	public function makePDF($html = null, $view = 0)
	{

		try {

			if ($html == null) {
				$html = $_POST['html_string'];
				$view = $_POST['view'];
				$string = $html;
			}

			$footer = true;
			$orientation = 'P';
			$name = 'document';

			if (array_key_exists('f', $_REQUEST) && $_REQUEST['f'] == '0') {
				$footer = false;
			}
			if (array_key_exists('o', $_REQUEST) && $_REQUEST['o'] == 'l') {
				$orientation = 'L';
			}
			if (!empty($_REQUEST['n'])) {
				$name = $_REQUEST['n'];
			}

			$outputType = !empty($view) ? $view : 'I';
			$outputName = ($outputType == 'F') ? __DIR__ . '/tmp-pdf-generation/' . $name . '.pdf' : $name . '.pdf';

			$defaultConfig = (new Mpdf\Config\ConfigVariables())->getDefaults();
			$fontDirs = $defaultConfig['fontDir'];

			$defaultFontConfig = (new Mpdf\Config\FontVariables())->getDefaults();
			$fontData = $defaultFontConfig['fontdata'];

			$mpdf = new \Mpdf\Mpdf([
				'tempDir' => __DIR__ . '/tmp-pdf-generation',
				'mode' => 'utf-8',
				'orientation' => $orientation,
				'margin_left' => 10,
				'margin_right' => 10,
				'margin_top' => 10,
				'margin_bottom' => 10,
				'margin_header' => 5,
				'margin_footer' => 5,
				'fontDir' => __DIR__ . '/fonts',
				'fontdata' => $fontData + [
					'lato' => [
						'R' => 'Lato-Bold.ttf',
						'B' => 'Lato-Black.ttf',
						'I' => 'Lato-BoldItalic.ttf',
						'BI' => 'Lato-BlackItalic.ttf'
					],
					'helvetica' => [
						'R' => 'Lato-Bold.ttf',
						'B' => 'Lato-Black.ttf',
						'I' => 'Lato-BoldItalic.ttf',
						'BI' => 'Lato-BlackItalic.ttf'
					],
					'arial' => [
						'R' => 'Lato-Bold.ttf',
						'B' => 'Lato-Black.ttf',
						'I' => 'Lato-BoldItalic.ttf',
						'BI' => 'Lato-BlackItalic.ttf'
					],
					'sansserif' => [
						'R' => 'Lato-Bold.ttf',
						'B' => 'Lato-Black.ttf',
						'I' => 'Lato-BoldItalic.ttf',
						'BI' => 'Lato-BlackItalic.ttf'
					]
				],
				'default_font' => 'lato',
				'default_font_size' => 9
			]);

			$mpdf->debug = true;

			$mpdf->SetDisplayMode('fullpage');

			if ($footer) {
				$mpdf->setFooter('Page {PAGENO} of {nbpg}');
			}

			$stylesheet = file_get_contents($_SERVER["DOCUMENT_ROOT"] . '/api/mpdf.css');

			$mpdf->WriteHTML($stylesheet, \Mpdf\HTMLParserMode::HEADER_CSS);
			$mpdf->WriteHTML($html, \Mpdf\HTMLParserMode::HTML_BODY);

			$mpdf->Output($outputName, $outputType);

			return $outputName;
		} catch (\Mpdf\MpdfException $e) {

			echo $e->getMessage();
		}
	}

	public function metrics($queryObj = null, $json = 1)
	{

		if ($queryObj == null) {
			$calculation = $_POST->calculation;
			$objectType = $_POST->objectType;
			$field = $_POST->field;
			$dateBy = $_POST->dateBy;
			$groupBy = $_POST->groupBy;
			$limit = $_POST->limit;
			$start = $_POST->dateRange->start;
			$end = $_POST->dateRange->end;
			$search = $_POST->search;
		}

		/*
		$startTime = new DateTime($start);
		$endTime = new DateTime($end);
*/

		/*
		$startRange = $startTime->format('Y-m-d H:i:s');
		$endRange = $endTime->format('Y-m-d H:i:s');
*/

		if ($calculation == 'total') {
			$calcString = 'COUNT(id) as total';
		} else {
			$calcString = "" . $calculation . " (
							     CAST (
							          object_data->>'" . $field . "' AS INT
							     )
							)";
		}

		$sql = "select
				object_data->>'" . $groupBy . "' as $groupBy,
				" . $calcString . "
				FROM objects
				WHERE instance = '" . $this->appConfig['instance'] . "' AND object_type = '" . $objectType . "' and to_timestamp(object_data->>'date_created', 'YYYY-MM-DD HH24:MI:SS') BETWEEN to_timestamp('" . $start . "', 'YYYY-MM-DD HH24:MI:SS') AND '" . $end . "'
				GROUP BY object_data->>'" . $groupBy . "' offset 0 limit " . $limit . ";";
		//echo $sql;
		return $this->sendData($this->pgObjects->query($sql), $json);
	}

	public function pbkdf2($algorithm, $password, $salt, $count, $key_length, $raw_output = false)
	{

		$algorithm = strtolower($algorithm);

		if (!in_array($algorithm, hash_algos(), true))

			trigger_error('PBKDF2 ERROR: Invalid hash algorithm.', E_USER_ERROR);

		if ($count <= 0 || $key_length <= 0)

			trigger_error('PBKDF2 ERROR: Invalid parameters.', E_USER_ERROR);



		if (function_exists("hash_pbkdf2")) {

			// The output length is in NIBBLES (4-bits) if $raw_output is false!

			if (!$raw_output) {

				$key_length = $key_length * 2;
			}

			return hash_pbkdf2($algorithm, $password, $salt, $count, $key_length, $raw_output);
		}



		$hash_length = strlen(hash($algorithm, "", true));

		$block_count = ceil($key_length / $hash_length);



		$output = "";

		for ($i = 1; $i <= $block_count; $i++) {

			// $i encoded as 4 bytes, big endian.

			$last = $salt . pack("N", $i);

			// first iteration

			$last = $xorsum = hash_hmac($algorithm, $last, $password, true);

			// perform the other $count - 1 iterations

			for ($j = 1; $j < $count; $j++) {

				$xorsum ^= ($last = hash_hmac($algorithm, $last, $password, true));
			}

			$output .= $xorsum;
		}



		if ($raw_output)

			return substr($output, 0, $key_length);

		else

			return bin2hex(substr($output, 0, $key_length));
	}

    private function addTagIfNotExists($object, $tagToAdd, $fieldName){

        // $RcaPending = 8348218;
        $taggedWith = $object['tagged_with'];
        $hasTag = in_array($tagToAdd, $taggedWith);
        // $hasRCATag = in_array($RcaPending, $taggedWith);
        $currentTime = (new DateTime())->format('Y-m-d H:i:s');
        if(!$hasTag){
            $taggedWith[] = $tagToAdd;
            // if(!$hasRCATag){
            //     $taggedWith[] = $RcaPending;
            // }
            $object['tagged_with'] = $taggedWith;
            $object[$fieldName] = $currentTime;
            $this->pgObjects->update($object['object_bp_type'], $object);
        } else {
            $object[$fieldName] = $currentTime;
            $this->pgObjects->update($object['object_bp_type'], $object);
        }

    }

    public function addRCATagByType($_object = null, $tagType = ''){
	    //new comment
        $RCACompleted = 8348219;

        $tagByType = [
            'comments' => 9143452, //RCA - Comments
            'uploads' => 9143453 // RCA - Uploads
        ];

	    $objectParsed = null;
        if($_POST->objectData) {
            $objectParsed = $this->objToArr($_POST->objectData);
            if($objectParsed['_object']) {
                $_object = ['id' => $objectParsed['_object']];
                $tagType = $objectParsed['tagType'];
            }
        }

        $object = $this->pgObjects->getByIdAcrossInstances('', $_object['id']);

        $updated = $this->pgObjects->removeTagFromObject(
            $object['id']
            , $RCACompleted
            , 'tagged_with'
        );

        $object = $this->pgObjects->getByIdAcrossInstances('', $_object['id']);

        $fieldName = 'rca_last_updated_' . $tagType;
        if($tagType == 'comments' || $tagType == 'uploads') {
            $this->addTagIfNotExists($object, $tagByType[$tagType], $fieldName);
        }

        if($objectParsed){
            $childObjs = array('rca_last_updated_comments'=> true, 'rca_last_updated_uploads'=> true);
            $response = $this->pgObjects->getByIdAcrossInstances('', $_object['id'], $childObjs);
            return $this->sendData($response, 1);
        }
    }

	public function postComment($object = null, $getChildObjs = 0, $json = 1)
	{

		$feedCountOptions = [];
		$toNotify = [];
		if ($object == null) {

			$object = $this->objToArr($_POST->objectData);

			if (
				$object['notification']
				&& $object['notification']['notify']
				&& is_array($object['notification']['notify'])
			) {
				$toNotify = $object['notification']['notify'];
			}

			if (is_string($object['field'])) {
				$feedCountOptions['field'] = $object['field'];
			}
		}

		if (isset($_POST->getChildObjs)) {

			if (is_object($_POST->getChildObjs)) {

				$getChildObjs = $this->objToArr($_POST->getChildObjs);
			} else if (is_numeric($_POST->getChildObjs)) {

				$getChildObjs = intval($_POST->getChildObjs);
			}
		}

		if (isset($object['notification'])) {
			$objInfo = $this->pgObjects->getByIdAcrossInstances('', $object['notification']['producer'], 1);
		} elseif (isset($object['type_id'])) {
			$objInfo = $this->pgObjects->getByIdAcrossInstances('', $object['type_id'], 1);
		} else {
			$objInfo = $object['notification']['producer'];
		}

        // Switch instance
		$this->pgObjects->setInstance($objInfo['instance']);

        // Add tags to comment
		$object['tagged_with'] = $objInfo['tagged_with'];
		array_push(
			$object['tagged_with'],
			$objInfo['id']
		);

		// Additional tags (from user config)
		if (is_array($_POST->objectData->tagged_with)) {
			foreach ($_POST->objectData->tagged_with as $tag) {

				if (is_int($tag)) {

					array_push(
						$object['tagged_with'],
						$tag
					);
				}
			}
		}

		$object['tagged_with'] = array_unique($object['tagged_with']);

		// Remove users tags, unless its the author of the note or the user is tagged
		$tagObjs = $this->pgObjects->getById('', $object['tagged_with']);
		if (is_array($tagObjs)) {
			foreach ($tagObjs as $i => $tagObj) {

				// Remove if:
				// a user
				// that is not mentioned
				// and is not listening to the object
				if (
					$tagObj
					&& $tagObj['object_bp_type'] === 'users'
				) {
					$object['tagged_with'] = array_filter(
						$object['tagged_with'],
						function ($tag) use ($tagObj) {
							return $tag !== $tagObj['id'];
						}
					);
				}
			}
		}
		if (is_array($toNotify) && !empty($toNotify)) {
			$object['tagged_with'] = array_merge($object['tagged_with'], $toNotify);
		}
		if (is_array($objInfo['notify']) && !empty($objInfo['notify'])) {

			$object['tagged_with'] = array_merge($object['tagged_with'], $objInfo['notify']);
		}
		array_push($object['tagged_with'], intval($_COOKIE['uid']));

		$object['tagged_with'] = array_unique($object['tagged_with']);

		// Force public
		if ($objInfo['instance'] != $this->appConfig['instance']) {
			$object['public'] = 1;
		}

		$createdObj = $this->pgObjects->create('notes', $object, 0, $getChildObjs);

        $this->pgObjects->incrementCommentCount($objInfo['id'], $feedCountOptions);

		if ($createdObj) {

            if (!empty($object['notification']) && $object['notification'] !== false) {

				$author = $createdObj['author'];
				if (empty($author) || is_int($author)) {
					$author = $this->pgObjects->getByIdAcrossInstances('', $object['author']);
					$this->pgObjects->setInstance($createdObj['instance']);
				}

				$notification = [];
				$notification['title']    = $author['fname'] . ' ' . $author['lname'] . ' mentioned you.';
				$notification['secondaryTitle']    = $author['fname'] . ' ' . $author['lname'] . ' posted a comment.';
				$notification['details']  = $object['note'];
				$notification['color']    = 'purple';
				$notification['icon']     = 'at';
				$notification['type']     = 'mention';
				$notification['producer'] = $objInfo['id'];
				$notification['link']     = $object['notification']['link'];
				$notification['notify']   = $object['notification']['notify'];

				// Add main contact/company name to notification title the comment
				// is about a project.
				if (
					$objInfo['object_bp_type'] === 'groups'
					&& $objInfo['group_type'] === 'Project'
					&& $objInfo['main_contact'] > 0
				) {

					$contactInfo = $this->pgObjects->getById(
						'contacts',
						$objInfo['main_contact'],
						[
							'name' => true, 'fname' => true, 'lname' => true, 'company' => [
								'name' => true
							]
						]
					);

					$notification['title'] = $author['fname'] . ' ' . $author['lname'] . ' mentioned you in "' . $objInfo['name'] . '"';
					if ($contactInfo) {

						$notification['title'] .= ' for ' . $contactInfo['name'];

						if ($contactInfo['company']) {
							$notification['title'] .= ' at ' . $contactInfo['company']['name'];
						}
					}
					$notification['title'] .= '.';
				}

				$createdObj['notification'] = $this->notify(0, $notification);

                $notifyManagers = false;
                $userIdsToNotify = [];
                if (
                    $objInfo['object_bp_type'] === 'groups'
                    && $objInfo['group_type'] === 'Project'
                    && count($objInfo['managers']) != 0
                ) {
                    $notifyManagers = true;
                    if(isset($objInfo['managers'][0]['id'])){
                        $userIdsToNotify = __::pluck($objInfo['managers'], 'id');
                    } else {
                        $userIdsToNotify = $objInfo['managers'];
                    }
                } else if (
                    $objInfo['object_bp_type'] === '#Event_notes'
                    || $objInfo['object_bp_type'] === 'contracts'
                    || $objInfo['object_bp_type'] === '#zqnCcF'
                ) {
                    $notifyManagers = true;
                    $project = $this->pgObjects->getById('groups', $objInfo['parent'], 0);
                    if(isset($objInfo['managers'][0]['id'])){
                        $userIdsToNotify = __::pluck($objInfo['managers'], 'id');
                    } else {
                        $userIdsToNotify = $project['managers'];
                    }
                }

				if ($notifyManagers) {

					$this->pgObjects->setInstance($this->appConfig['instance']);
					$queriedUserIds = $this->pgObjects->where('users', ['id' => ['type' => 'or', 'values' => $userIdsToNotify]]);
					$this->pgObjects->setInstance($createdObj['instance']);

					$commentEmailTemplate = '<div>Hello {managerName}, </div><br> <div>You are being notified because a comment was posted on a project you manage <strong> {projectName} </strong> by <strong> {author} </strong> </div> <div style=\"margin: 10px 0;\"></div> <br><div style=\"margin: 10px 0;\"></div> <div>  {commentNote} </div><br> <div><a href="{link}" style="color: #5084cc; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold;">View in Bento</a></div>';

					$emailFrom = $this->appConfig['emailFrom'];
					$instance = $this->appConfig['instance'];
					$projectName = $objInfo['name'];
					$link = $notification['link'];

					foreach ($queriedUserIds as $manager) {
						$vars = array(
							'{managerName}' => $manager['name'],
							'{projectName}' => $projectName,
							'{author}' => $author['name'],
							'{commentNote}' => $object['note'],
							'{link}' => $link
						);


						$emailBody = strtr($commentEmailTemplate, $vars);
						$this->sendEmail(
							$manager['email'],
							$emailFrom,
							"A comment was posted on " . $projectName,
							['BODY' => $emailBody, 'INSTANCE_NAME' => $instance],
							"COMMENT_POSTED",
							false
						);
					}
				}

                $this->pgObjects->setInstance($objInfo['instance']);
				$entityToValidate = $this->pgObjects->getByIdAcrossInstances('', $objInfo["parent"]);

				$entityType = false;
				$validTypes = ['Client Info Request', 'Client Review'];
				$validEntityTypes = ['form', 'project', 'documentAttachment'];
                $fileName = "";

				if($object['relatedFormType']){
					$entityType = "documentAttachment";
                    if(array_key_exists('notification', $object)) {
                        $fileInfo = $this->pgObjects->getByIdAcrossInstances('', $object["notification"]['producer'], ["file_upload" => true]);
                        if($fileInfo && array_key_exists('file_upload', $fileInfo)) {
                            $fileName = $fileInfo['file_upload']['file_name'];
                        }
                    }

					$actionItemType = $this->pgObjects->getByIdAcrossInstances('', $object['relatedFormType']);
					if(in_array($actionItemType['name'], $validTypes)) {
						$actionItem = $this->pgObjects->getByIdAcrossInstances('', $objInfo["related_object"]);
						$projectId = $actionItem['parent'];
						$entityToValidate = $this->pgObjects->getByIdAcrossInstances('', $projectId);
					}
				}
				else if($entityToValidate && $entityToValidate["object_bp_type"] == "groups" && $entityToValidate["group_type"] ==  "Project"){
					$entityType = "form";
				} else {
                    $select = [
                        "name" => true,
                        "type" => true,
                        "tools" => true,
                        "managers" => true,
                        "parent" => true,
                        "object_bp_type" => true,
                        "group_type" => true
                    ];
					$entityToValidate = $this->pgObjects->getByIdAcrossInstances('', $objInfo["id"], $select);
					if($entityToValidate && $entityToValidate["object_bp_type"] == "groups" && $entityToValidate["group_type"] ==  "Project"){
						$entityType = "project";
					}
				}

				if(in_array($entityType, $validEntityTypes)) {
					if($entityType == "form" || $entityType == 'documentAttachment') {
						$select = [
							"##Action Items" => ["name" => true],
							"name" => true,
							"type" => true,
							"tools" => true,
							"managers" => true,
                            "parent" => true
						];

						$project = $this->pgObjects->getById(
							'groups',
							intval($entityToValidate['id']),
							$select
						);

						//is valid form, sent notification
						$actionItem = __::find($project["##Action Items"], function($obj) use ($objInfo){
							return  $objInfo["id"] == $obj["id"];
						});

						$actionItemType = $this->pgObjects->getBlueprint($actionItem['object_bp_type'], false, true, true);

					} else {
						$project = $entityToValidate;
					}

                    $taggedWith = [];

                    $taggedWith[] = $project['id'];
                    $taggedWith = array_merge($taggedWith, $project['tagged_with']);

                    $formationProjectTypes = [2238449, 2972438, 2972443, 2972535];
                    $teamId = 1652258; //Compliance Team Id

                    if(in_array($project['type']['id'], $formationProjectTypes)){
                        $teamId = 6077227; //Formation Team Id
                    }

                    $taggedWith[] = $teamId;

						//get role sheet
						$roleSheet = __::find($project['tools'], function ($obj) {
							$startWith = "Project Role Sheet";
							$len = strlen($startWith);
							return (substr($obj["display_name"], 0, $len) === $startWith);
						});

						$userNotificationId = [];
                        $metaRolesData = [];

						if ($roleSheet) {

							$bluePrint = $roleSheet["system_name"];
							$entity_type = $this->pgObjects->where('entity_type', array(
								'bp_name' => $bluePrint
							))[0];

							$childObjects = array(
								"type" => true,
								"date_created" => true,
								"object_uid" => true,
								"parent" => true,
								"name" => ["object_uid" => true],
								"status" => true,
								"created_by" => true,
								"is_template" => true,
								"time_logged" => true,
								"time_estimate" => true,
								"last_updated_by" => true,
								"date_created_done" => true
							);

                            $queryOptions = Array("tagged_with" => $project["id"]);
							$roles = array();

							foreach ($entity_type["blueprint"] as $key => $value) {

								if ($value["fieldType"] == "user" && strpos(strtolower($value["name"]), "specialist") !== false && $value['is_archived'] == false) {
									$roles[] = $key;
									$childObjects = array_merge([$key => ["fname" => true, "lname" => true, "profile_image" => true, "color" => true]], $childObjects);
								}
							}

							$bps = $this->pgObjects->where('#' . $bluePrint, $queryOptions, '', $childObjects);

							foreach ($roles as $role) {
								if ($bps[0][$role]["id"] > 0) {
									$userNotificationId[] = $bps[0][$role]["id"];
                                    $metaRolesData[$bps[0][$role]["id"]] = $bps[0][$role];
								}
							}

						} else {
							//receive the specialist manager
							if($project["managers"] && count($project["managers"]) > 0) {
								foreach($project["managers"] as $manager){
									$userNotificationId[] = $project["managers"][0]["id"];
								}
							}
						}

						$notifications = [];

						//only for test purporsed
						if(count($userNotificationId) <= 0){
                            $userTest = $this->pgObjects->getByIdAcrossInstances('users', 1674601);
                            $userNotificationId[] = $userTest["id"];
                            $metaRolesData[$userTest["id"]] = $userTest;
                        }

						if (count($userNotificationId) > 0) {
							foreach ($userNotificationId as $userId) {
								$producerType = ($entityType === 'form') ? $actionItemType["id"] : $project['type'];
								$producer = ($entityType === 'form') ? $objInfo["id"] : $project['id'];
                                $instanceName = "foundation_group";

                                if(array_key_exists($userId, $metaRolesData)){

                                    $labelColor = $metaRolesData[$userId]['color'];
                                    $labelName = $metaRolesData[$userId]['fname']. ' '.$metaRolesData[$userId]['lname'];
                                    $urlText = "bento.infinityhospitality.net/app/" . $instanceName . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $project['id'] . "-" . urlencode($project['name']);

                                    if($entityType === 'form'){
                                        $urlText = "bento.infinityhospitality.net/app/" . $instanceName . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $project['id'] . "-" . urlencode($project['name']). '&3=pt-entityList-Client Action items&4=e-' . $actionItem['id'] . "-" . urlencode($actionItem['name']);
                                        $notification['secondaryTitle'] = '<i class="icon comment"></i> '.$author['fname'] . ' ' . $author['lname'] .' posted a new comment on ' .$actionItem['name'];
                                        $notification['details'] = $notification['details'];
                                        $notification['link'] = $urlText;

                                    } else if($entityType === 'documentAttachment'){
                                        $notification['secondaryTitle'] = '<i class="icon comment"></i> '.$author['fname'] . ' ' . $author['lname'] .' uploaded a new file for ' .$actionItem['name'];
                                        $notification['details'] = $fileName;
                                        $notification['link'] = $urlText;
                                    } else {
                                        $notification['secondaryTitle'] = '<i class="icon comment"></i> '.$author['fname'] . ' ' . $author['lname'] .' posted a new comment on ' .$project['name'];
                                        $notification['details'] = $notification['details'];
                                        $notification['link'] = $urlText;
                                    }

                                    $notification['title'] = $project['parent']['name'] . ': <span class="ui mini '. $labelColor . ' label">'. $labelName . '</span>';
                                }

								$notifications[] = [
									'title' => $notification['title'],
                                    'secondaryTitle' => $notification['secondaryTitle'],
									'details' => $notification['details'],
									'producer' => $producer,
									'producer_type' => $producerType,
									'color' => "red",
									'icon' => "stream",
									'type' => "recent-client-activity",
									'user' => $userId,
									'link' => $notification['link'],
									'is_viewed' => 0,
                                    'tagged_with' => $taggedWith,
                                    'instance' => 'foundation_group'
								];

							}

							$notification = $this->pgObjects->create('notification', $notifications, true);

						}

				} else {
					//is not valid form or project
				}

				if($author && $author["instance"] == "foundation_group" && $object['public']){

                    $childObjs = [
                        "last_updated" => true,
                        "author" => true,
                        "public" => true
                    ];

                    $queryObj = [
                        "tagged_with" => [
                            "type" => "any",
                            "values" => [$objInfo["parent"]],
                            "or" =>[
                                "type_id" => $objInfo["parent"]
                            ]
                        ],
                        "is_template" => [
                            "type" => "not_equal",
                            "value" => 1
                        ],
                        "paged" => [
                            "count" => true,
                            "page" => 0,
                            "pageLength" => 50,
                            "paged" => true,
                            "sortCol" => "date_created",
                            "sortDir" => "desc",
                            "sortCast" => "date"
                        ]
                    ];

                    $queryObj['_dont_force_portal_user_tag'] = true;

                    $commentsResult = $this->getObjectsWhere('notes', $queryObj, 0, $childObjs);
                    $comments = [];

                    if (!empty($commentsResult['data'])) {
                        $comments = $this->parseAuthors($commentsResult['data'], true);
                    }

                    $usersToNotify = [];
                    $authors = [];
                    $instance = false;
                    if(count($comments) > 0) {
                        $usersToNotify = __::filter($comments, function($comment) {
                            if($comment['author'] && $comment['author']['instance'] != 'foundation_group'){
                                return true;
                            }
                            return false;
                        });
                        if(count($usersToNotify) > 0) {
                            $authors = __::uniq(__::pluck($usersToNotify, 'author'));
                            $instance = $authors[0]["instance"];
                            $usersToNotify = __::pluck($authors, 'id');
                        }
                    }


                    if(count($usersToNotify) > 0) {


                        foreach ($usersToNotify as $userId) {
                            $producerType = $project['type'];
                            $producer = ($entityType === 'form') ? $objInfo["id"] : $project['id'];

                            if(array_key_exists($userId, $metaRolesData)) {

                                $labelColor = $metaRolesData[$userId]['color'];
                                $labelName = $metaRolesData[$userId]['fname'] . ' ' . $metaRolesData[$userId]['lname'];


                                $relationName = $project['name'];
                                if($entityType === 'form'){
                                    $relationName = $actionItem['name']. ' / '.$project['name'];
                                }

                                $notification['title'] = '<span class="ui mini '. $labelColor . ' label">'. $labelName . '</span> : Foundation Group has replied to your comment';
                                $notification['secondaryTitle'] = '<i class="icon comment"></i> '.$labelColor .' has posted a response to ' . $relationName;


                                $notifications[] = [
                                    'title' => $notification['title'],
                                    'details' => $notification['details'],
                                    'producer' => $producer,
                                    'producer_type' => $producerType,
                                    'color' => "red",
                                    'icon' => "stream",
                                    'type' => "recent-client-activity",
                                    'user' => $userId,
                                    'link' => $notification['link'],
                                    'is_viewed' => 0,
                                    'tagged_with' => $taggedWith
                                ];

                            }

                        }

                        $this->pgObjects->setInstance($instance);
                        $this->pgObjects->create('notification', $notifications, true);

                        $this->pgObjects->setInstance($author["instance"]);

                        foreach($authors as $userInfo) {
                            // prepare email

                            $relationName = $project['name'];
                            $linkToComment = "bento.infinityhospitality.net/app/" . $objInfo['instance'] . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $project['id'] . "-" . urlencode($project['name']);
                            if($entityType === 'form'){
                                $relationName = $actionItem['name']. ' / '.$project['name'];
                                $linkToComment = "bento.infinityhospitality.net/app/" . $objInfo['instance'] . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $project['id'] . "-" . urlencode($project['name']) . "&3=pt-entityList-Client Action items&4=e-" . $actionItem['id'] . '-' . urlencode($actionItem['name']);
                            }

                            $mergeTags = [
                                "projectName" => $relationName,
                                "email" => $userInfo["email"],
                                "recipient" => $userInfo["name"],
                                "specialist" => $labelName
                            ];

                            $subjectString = "Foundation Group has replied to your portal comment";
                            $bodyString = "<p> <b>{{specialist}}</b>, has posted a response to <b>{{projectName}}</b>. <br/> ".$notification['details'] . ", please log in Bento for Response in <a href='".$linkToComment."'>" . $relationName . "</a>";
                            foreach ($mergeTags as $k => $v) {
                                //echo $k;
                                $subjectString = str_replace('{{' . $k . '}}', $v, $subjectString);
                                $bodyString = str_replace('{{' . $k . '}}', $v, $bodyString);
                            }

                            $mergevars = array(
                                'TITLE' => $subjectString,
                                'SUBJECT' => $subjectString,
                                'BODY' => $bodyString,
                                'INSTANCE_NAME' => $author["instance"]
                            );

                            $isComplianceTeam = in_array("1652258", $project['tagged_with']);
                            $isFormationTeam = in_array("6077227", $project['tagged_with']);


                            $projectMainContact = $this->pgObjects->getById('', $project['main_contact']);

                            $emailTags = [
                                intval($project['id']),
                                intval($projectMainContact['company']),
                                intval($project['main_contact']),
                                1636580 //foundationGroup HQ
                            ];



                            if($isComplianceTeam){
                                $emailTags[] = 1652258;
                            }
                            if($isFormationTeam){
                                $emailTags[] = 6077227;
                            }

                            $this->sendEmail(array($mergeTags["email"]), '<EMAIL>', $subjectString, $mergevars, $emailTags, false);
                        }
                    }

                }

            }

            /*
                FOUNDATION GROUP ONLY - Client Portal functionality
                FG needs to know when a comment is posted from a Client that is logged into a client portal
                Client can directly make comments on Action Items (Sets) and Projects (Groups)

                If a comment is made on either of those then need to tag with {id:8348218, name: 'RCA', color: 'red'}

                Foundation Group has a Team Tool and Dashboard AppViews configured to display
                    - All Action Items tagged with "RCA" using a Team Tool (Compliance Team & Formation Team)
                    - Dashboard AppViews grouped by Project Type

                For comments made by a client while logged in through their Client Portal, the following (3) action
                should occur:
                (1) Tag the context object with the RCA tag (id: 8348218)
                (2) Create an additional comment (object_bp_type is actually called 'notes' but referred to as comments)
                    stating a Recent Client Activity event has taken place with some additional context.
                    (i.e. "Client [First Name] [Last Name] has posted a comment in the portal" Here is what they said...)

                @TODO
                (3) Create a notification object notifying the user assigned to 'Specialist' Role on that related project
                [Project :hasMany: Action Items] Action Item parent property set to Project ID]
            */

            ///Exclude incoming postComments if they are Workflow State Changes - not necessary to add RCA tag
            if (($this->appConfig['instance'] == 'foundation_group' || $this->appConfig['instance'] == 'rickyvoltz') && $createdObj['log_type'] != 'state-change') {

                $currentInstance = $this->appConfig['instance'];

                if ( !empty( $object['author'] ) || is_int( $object['author'] )) {
                    $commentInstance = $this->pgObjects->getByIdAcrossInstances('', $object['author'])['instance'];
                }


                $project = $this->pgObjects->getById('', $objInfo['parent']['id']);
                $main_contact = $this->pgObjects->getById('', $project['main_contact'], ['contact_info' => true, 'company'=> true]);
                $organization = $main_contact['company'];

                $author = $this->pgObjects->getById('', $createdObj['author']);

                ///if different, then comment was made in portal, else comment was made foundation_group by a FG Teammember
                if ( $currentInstance != $commentInstance ) {

                    $this->pgObjects->setInstance($currentInstance);



                    ///if comment made on an action item,  else if comment made on project
                    if (  substr($objInfo['object_bp_type'], 0, 1) === '#') {


                        $this->addRCATagByType($objInfo, 'comments');

                        ///(2) comment/note
                        $link = 'https://foundationgroup.bento.infinityhospitality.net/app/foundation_group#hq&1=hqt-crmTool-Contacts&2=o-companies-' . $organization['id'] . '-'. rawurlencode($organization['name']) .'&3=o-project-'. $project['id'].'-'. rawurlencode($project['name']) .'&4=pt-entityList-Client%20Action%20items&5';
                        // $link = 'http://localhost:8080/app/foundation_group#hq&1=hqt-crmTool-Contacts&2=o-companies-' . $organization['id'] . '-'. rawurlencode($organization['name']) .'&3=o-project-'. $project['id'].'-'. rawurlencode($project['name']) .'&4=pt-entityList-Client%20Action%20items&5';

                        $originalAuthor = $this->pgObjects->getByIdAcrossInstances('', $object['author']);
                        $rcaNote = array(
                            'type_id' => $project['id']
                            , 'author' => $originalAuthor['id']
                            , 'log_type' => 'recent-client-action'
                            , 'notification' => array(
                                'producer' => $project['id']
                                , 'link' => $link
                            )
                            , 'record_type' => 'log'
                            , 'public' => 0
                            , 'edited' => 0
                            , 'note_type' => 'System Log'
                            , 'instance' => $createdObj['instance']
                            , 'icon' => array(
                                'icon' => 'tags', 'color' => 'red'
                            ), 'tagged_with' => [1652258, $project['id'], $main_contact['id'], $organization['id']]
                            , 'note' => ' <a href="' . $link . '" target="_blank"><i class="red tags icon"></i> &nbsp;' . $organization['name'] . ' new comment from ' . $originalAuthor['fname'] . ' '. $originalAuthor['lname'] . '</a></br>' . $createdObj['note']
                        );

                        $rcaNote = $this->pgObjects->create('notes', $rcaNote, 0, $getChildObjs);
                        $this->pgObjects->incrementCommentCount($project['id'], $feedCountOptions);


                        ///(3) Create notification to Role Assignment
                        // Pull the 'Specialist' on the project role sheet
                        $roleSheet = $this->pgObjects->where(
                            '#ReU6uX',
                            [
                                'parent' => $project['id']
                            ]
                        )[0];

                        $specialistKey;
                        $bp = $this->pgObjects->getBlueprint($roleSheet['object_bp_type']);

                        ///query blueprint to find the 'Specialist' Role assignment
                        foreach( $bp as $propertyKey => $property){
                            if ( $property['name'] == 'Specialist'){
                                $specialistKey = $propertyKey;
                            }
                        }

                        if (is_int($roleSheet[$specialistKey])) {
                            $userAssignedSpecialist = $roleSheet[$specialistKey];
                        }

                        $userAssignedSpecialist = $this->pgObjects->getById('', $userAssignedSpecialist);

                        // $notification = [];
                        // $notification['title']    = $author['fname'] . ' ' . $author['lname'] . ' commented on an action item.';
                        // $notification['secondaryTitle']    = $createdObj['note'];
                        // $notification['details']  = $createdObj['note'];
                        // $notification['color']    = 'red';
                        // $notification['icon']     = 'stream';
                        // $notification['type']     = 'recent-client-activity';
                        // $notification['producer'] = $objInfo['id'];
                        // $notification['link']     = $link;
                        // $notification['notify']   = $userAssignedSpecialist['id'];

                        // $notified = $this->notify(0, $notification);

                            // $rcaNoti = array();
                            // $rcaNoti = [
                            //     'title' => $author['fname'] . ' ' . $author['lname'] . ' posted a comment.',
                            //     'secondaryTitle' => $author['fname'] . ' ' . $author['lname'] . ' posted a comment.',
                            //     'details' => $objInfo['note'],
                            //     'producer' => $objInfo['id'],
                            //     'producer_type' => $producerType,
                            //     'color' => "red",
                            //     'icon' => "stream",
                            //     'type' => "recent-client-activity",
                            //     'link' => $link,
                            //     'is_viewed' => 0,
                            //     'tagged_with' => $updTaggedWith,
                            //     'instance' => 'foundation_group',
                            //     'notify' => $userAssignedSpecialist['id']
                            // ];

                        //     $rcan = $this->pgObjects->create('notification', $rcaNoti, true);
                        // var_dump("7471 :: rcaNoti ", $rcan ). PHP_EOL . "<br>";
                        // // var_dump("7454 :: notified ", $notified ). PHP_EOL . "<br>";
                        // die();

                    } else if ( $objInfo['object_bp_type'] === 'groups' && $objInfo['group_type'] === 'Project') {

                        $actions = $this->pgObjects->where(
                            'event_type',
                            [
                                'trigger' => 'clientCommentPosted', 'object' => $objInfo['type']
                            ]
                        );

                        $response = $this->pgObjects->runSteps(
                            $objInfo['id'],
                            $actions,
                            true
                        );

                        // if ($objInfo['object_bp_type'] == 'groups') {

                        //     $main_contact = $this->pgObjects->getById('', $objInfo['main_contact']);
                        //     $organization = $this->pgObjects->getById('', $main_contact['company']);
                        //     $link = 'https://bento.infinityhospitality.net/app/foundation_group#hq&1=o-project-' . $objInfo['id'] . '-' . rawurlencode($objInfo['name']);
                        //     // $link = 'http://localhost:8080/app/foundation_group#hq&1=o-project-'. $objInfo['id']. '-'. rawurlencode($objInfo['name']);

                        // }

                    } else if ( $objInfo['object_bp_type'] == 'document' ) {



                        $this->addRCATagByType($objInfo['related_object'], 'uploads');

                        ///get object #action item's parent PROJECT
                        $project = $this->pgObjects->getById(
                            'groups',
                            intval($objInfo['related_object']['parent']),
                            $select
                        );

                        $urlText = "bento.infinityhospitality.net/app/" . $instanceName . "#hq&1=hqt-projectTool-Projects&2=o-project-" . $project['id'] . "-" . urlencode($project['name']). '&3=pt-entityList-Client Action items&4=e-' . $objInfo['related_object']['id'] . "-" . urlencode($objInfo['related_object']['name']);
                        $notification['link'] = $urlText;

                        $notifications[] = [
                            'title' => '<i class="icon comment"></i> '.$author['fname'] . ' ' . $author['lname'] .' uploaded a new file for ' . $objInfo['related_object']['name'],
                            'details' => $notification['details'] . ' file has been uploaded.',
                            'producer' => $objInfo['related_object']['id'],
                            'producer_type' => $objInfo['related_object']['id'],
                            'color' => "red",
                            'icon' => "stream",
                            'type' => "recent-client-activity",
                            'user' => 1674601,
                            'link' => $notification['link'],
                            'is_viewed' => 0,
                            'tagged_with' => $project['taggedWith'],
                            'instance' => 'foundation_group'
                        ];

                        $notification = $this->pgObjects->create('notification', $notifications, true);
                    }

                } else {

                        /*
                            Comment made by Foundation Group user
                            If it's a public comment, then need to notify user
                                query for record
                                query for project
                                query for main_contact
                                get contacts email address
                                    - send email
                                search across instances to find user object in portal
                                    - send in app notification
                        */

                        if ( $createdObj['record_type'] !== 'log' && $createdObj['public'] === 1 ) {

                            //validate if add or remove tag for RCA
                            $RcaCompleted = 8348219;
                            $RcaPending = 8348218;
                            $RcaUploads = 9143453;
                            $RcaComments = 9143452;

                            if($author && ($author["instance"] == "foundation_group" || $author['instance'] == 'rickyvoltz') && $object['public']) {
                                //Its employee and has been tagged with RCA Tag before
                                $objectType = $this->pgObjects->getById('', $object['type_id']);
                                $hasRCATag = in_array($RcaPending, $objectType['tagged_with']);

                                if($hasRCATag){

                                    //remove RCA Tag
                                    $this->pgObjects->removeTagFromObject(
                                        $objInfo['id']
                                        , intval($RcaPending)
                                        , 'tagged_with'
                                    );

                                    //remove RCA Tag
                                    $this->pgObjects->removeTagFromObject(
                                        $objInfo['id']
                                        , intval($RcaUploads)
                                        , 'tagged_with'
                                    );

                                    //remove RCA Tag
                                    $this->pgObjects->removeTagFromObject(
                                        $objInfo['id']
                                        , intval($RcaComments)
                                        , 'tagged_with'
                                    );



                                }

                                //$this->addRCATagByType($objInfo, 'comments');

                                //Add RCA Completed
                                $this->pgObjects->tagObject(
                                    $objInfo['id']
                                    , intval($RcaCompleted)
                                    , 'tagged_with'
                                );

                            }

                            foreach ( $main_contact['contact_info'] as $info) {

                                if ($info['type'] == 1636586 and $info['is_primary'] == 'yes') {
                                    $main_contact_emailAddress = $info['info'];
                                }

                            }


                            /// SEND EMAIL RESPONSE TO CLIENT
                            if ( isset($main_contact_emailAddress) ){

                                $commentEmailTemplate = '<div>Hello {contact} </div><br><div>{author} from Foundation Group has responded to your comment</div><div style=\"margin: 10px 0;\"></div> <br><div style=\"margin: 10px 0;\"></div>' .
                                            '<div> {commentNote} </div><br><div><a href="https://bento.infinityhospitality.net" style="color: #5084cc; font-family:Helvetica, Arial, sans-serif; font-size:16px; font-weight:bold;">View in Bento</a></div>';

                                $emailFrom = $this->appConfig['emailFrom'];
                                $instance = $this->appConfig['instance'];
                                $projectName = $project['name'];

                                // $link = 'https://foundationgroup.bento.infinityhospitality.net/app/foundation_group#hq&1=hqt-crmTool-Contacts&2=o-companies-' . $organization['id'] . '-'. rawurlencode($organization['name']) .'&3=o-project-'. $project['id'].'-'. rawurlencode($project['name']) .'&4=pt-entityList-Client%20Action%20items&5';
                                // $link = 'http://localhost:8080/app/foundation_group#hq&1=hqt-crmTool-Contacts&2=o-companies-' . $organization['id'] . '-'. rawurlencode($organization['name']) .'&3=o-project-'. $project['id'].'-'. rawurlencode($project['name']) .'&4=pt-entityList-Client%20Action%20items&5';


                                $vars = array(
                                    // '{managerName}' => $manager['name'],
                                    '{projectName}' => $project['name'],
                                    '{contact}' => $main_contact['name'],
                                    '{author}' => $author['name'],
                                    '{commentNote}' => $createdObj['note'],
                                    '{link}' => $link
                                );

                                $emailBody = strtr($commentEmailTemplate, $vars);


                                $isComplianceTeam = in_array("1652258", $project['tagged_with']);
                                $isFormationTeam = in_array("6077227", $project['tagged_with']);


                                $projectMainContact = $this->pgObjects->getById('', $project['main_contact']);

                                $emailTags = [
                                    intval($project['id']),
                                    intval($projectMainContact['company']),
                                    intval($project['main_contact']),
                                    1636580 //foundationGroup HQ
                                ];



                                if($isComplianceTeam){
                                    $emailTags[] = 1652258;
                                }
                                if($isFormationTeam){
                                    $emailTags[] = 6077227;
                                }


                                $this->sendEmail(
                                    $main_contact_emailAddress,
                                    $emailFrom,
                                    "Response from Foundation Group" . $projectName,
                                    ['BODY' => $emailBody, 'INSTANCE_NAME' => $instance],
                                    $emailTags,
                                    false
                                );

                            }

                            // @TODO - SEND NOTIFICATION OBJ TO CLIENT
                            // $notification = [];
                            // $notification['title']    = $author['fname'] . ' ' . $author['lname'] . ' at Foundation Group responded to you.';
                            // $notification['secondaryTitle']    = $author['fname'] . ' ' . $author['lname'] . ' at Foundation Group responded to you.';
                            // $notification['details']  = $object['note'];
                            // $notification['color']    = 'purple';
                            // $notification['icon']     = 'at';
                            // $notification['type']     = 'clientResponse';
                            // $notification['producer'] = $objInfo['id'];
                            // $notification['link']     = $object['notification']['link'];
                            // $notification['notify']   = [$parent['main_contact']];

                            // $createdObj['notification'] = $this->notify(0, $notification);

                        }
                }

            } else {

                $isPortal = !in_array($this->appConfig['instance'], ['foundation_group', 'infinity','dreamscatering']);

                if ($isPortal && $createdObj['log_type'] != 'state-change') {
                    if (  substr($objInfo['object_bp_type'], 0, 1) === '#') {
                        $this->addRCATagByType($objInfo, 'comments');
                    }
                }
            }

        }

        // Switch instance back to default
		$this->pgObjects->setInstance($this->appConfig['instance']);

        return $this->sendData($createdObj, $json);
	}

	public function priceEvent($eventId = null, $json = 1)
	{

		if ($eventId == null and !empty($_POST->eventId)) {

			$eventId = $_POST->eventId;
		}

		$event = $this->pgObjects->getById('events', intval($eventId));

		if ($_COOKIE['uid'] == 1) {
			//var_dump($event);
			//die();
		}

		if ($event['event_type'] === 'Tasting') {

			$ret['total'] = 0;

			return $this->sendData($ret, $json);
		}

		$guestCount = (int)$event['guest_count'];

		if ($eventId > 0) {
			$timelineEvents = $this->pgObjects->where('timeline_events', ['eventId' => intval($eventId)]);
		}

		// get timeline events
		//$timelineEvents = $this->getTimelineEvents($eventId, 0);

		if ($_COOKIE['uid'] == 1) {
			//var_dump($timelineEvents);
			//echo $this->sendData($timelineEvents, 1);
			//echo 'have timeline events';
		}

		//ini_set('memory_limit', '-1');

		// loop over each timeline event
		if ($timelineEvents) {

			$totalCost = 0;

			foreach ($timelineEvents as $timelineEvent) {

				if ($timelineEvent['menu'] != null) {

					// loop over the menu
					// build menu for return
					// price menu options

					foreach ($timelineEvent['menu'] as $menuKey => $inventoryObject) {

						$inventoryItem = $inventoryObject['details'];

						if (!$inventoryObject['packageId']) {

							$packagePricing = $this->pricePackage($timelineEvent['menu'], $inventoryObject, $timelineEvent['id'], 0);
							//echo $packagePricing.'<br />';
							$inventoryItem['price'] += intval($packagePricing);

							if ($inventoryItem['units'] == 'custom' and $inventoryItem['type'] != 'bar') {

								/*
								if(!$_SESSION['uid']){
									echo $inventoryItem['name'].'<br />';
								}
*/

								$readablePrice = number_format(($inventoryItem['price'] / 100), 2);
								$itemQuantity = '';

								$inventoryItem['price'] += intval($packagePricing);

								$addToTotal = $inventoryItem['price'] * $inventoryItem['customQuantity'];
								$itemQuantity = '$' . $readablePrice . ' x ';
								$menuName = $inventoryItem['name'] . ' x' . $inventoryItem['customQuantity'];

								/*
								if(!$_SESSION['uid']){
									echo $inventoryItem['name'].' - '.$itemQuantity.'<br />';
								}
*/

								if ($inventoryItem['type'] == 'bar') {

									$itemQuantity = $itemQuantity . ' hours ';

									if (isset($inventoryItem['customGuestCount'])) {

										if ($inventoryItem['customGuestCountUnits'] == 'percentage') {

											$addToTotal = $inventoryItem['price'] * $inventoryItem['customQuantity'] * ($inventoryItem['customGuestCount'] / 100) * $guestCount;
											$itemQuantity = $itemQuantity . ' x ( ' . $inventoryItem['customGuestCount'] . '% of ' . $guestCount . ' guests )';
										} elseif ($inventoryItem['customGuestCountUnits'] == 'custom-quantity') {

											$addToTotal = $inventoryItem['price'] * $inventoryItem['customQuantity'] * $inventoryItem['customGuestCount'];
											$itemQuantity = $itemQuantity . ' x ' . $inventoryItem['customGuestCount'] . ' guests';
										}
									} else {

										$addToTotal = $inventoryItem['price'] * $inventoryItem['customQuantity'] * $guestCount;
										$itemQuantity = $itemQuantity . ' x ' . $guestCount . ' guests';
									}
								} else {

									if ($inventoryItem['customGuestCountUnits'] == 'percentage') {

										$addToTotal = $inventoryItem['price'] * ($inventoryItem['customGuestCount'] / 100) * $guestCount;
										$itemQuantity = $itemQuantity . ' ( ' . $inventoryItem['customGuestCount'] . '% of ' . $guestCount . ' guests )';
									} elseif ($inventoryItem['customGuestCountUnits'] == 'custom-quantity') {

										$addToTotal = $inventoryItem['price'] * $inventoryItem['customGuestCount'];
										$itemQuantity = $itemQuantity . ' ' . $inventoryItem['customGuestCount'];
									}
								}
							} else {

								$readablePrice = number_format(($inventoryItem['price'] / 100), 2);
								$itemQuantity = '';

								switch ($inventoryItem['type']) {

									case 'creditCardConvenienceFee':
									case 'Credit Card Convenience Fee':

										$addToTotal = $inventoryItem['price'];
										$itemQuantity = '$' . $readablePrice . ' x1';
										$menuName = $inventoryItem['name'] . ' x1';

										break;

									case 'bar':

										$barPrice = $inventoryItem['price'];

										if (count($inventoryItem['packageItems']) > 0) {

											foreach ($inventoryItem['packageItems'] as $packageItem) {

												$barPrice += $packageItem['details']['additionalPrice'];

												if (count($packageItem['details']['packageItems']) > 0) {

													foreach ($packageItem['details']['packageItems'] as $subPackageItem) {

														$barPrice += $subPackageItem['details']['additionalPrice'];
													}
												}
											}
										}

										if ($inventoryItem['hours'] > 0) {
											$hours = (float)$inventoryItem['hours'];
										} else {
											$hours = 1;
										}

										$readablePrice = number_format($barPrice / 100, 2);

										if ($inventoryItem['category'] == 'Packages' or $inventoryItem['category'] == 'Custom Selections') {

											if (isset($inventoryItem['customGuestCount'])) {

												if ($inventoryItem['customGuestCountUnits'] == 'percentage') {

													$addToTotal = (int)($barPrice * (float)$hours * ((int)$inventoryItem['customGuestCount'] / 100) * $guestCount);
													$itemQuantity = '$' . $readablePrice . ' x ' . $hours . ' hours x ( ' . $inventoryItem['customGuestCount'] . '% of ' . $guestCount . ' guests )';
												} elseif ($inventoryItem['customGuestCountUnits'] == 'custom-quantity') {

													$addToTotal = (int)($barPrice * (float)$hours * (int)$inventoryItem['customGuestCount']);
													$itemQuantity = '$' . $readablePrice . ' x ' . $hours . ' hours x ' . $inventoryItem['customGuestCount'] . ' guests';
												}
											} else {

												$addToTotal = (int)($barPrice * (float)$hours * $guestCount);

												$itemQuantity = '$' . $readablePrice . ' x ' . $hours . ' hours x ' . $guestCount . ' guests';
											}

											$menuName = $inventoryItem['name'] . ' x1';
										} elseif ($inventoryItem['category'] == 'A La Carte Items') {

											if (isset($inventoryItem['customGuestCount'])) {

												if ($inventoryItem['customGuestCountUnits'] == 'percentage') {

													$addToTotal = $barPrice * ((int)$inventoryItem['customGuestCount'] / 100) * $guestCount;
													$itemQuantity = '$' . $readablePrice . ' x ( ' . $inventoryItem['customGuestCount'] . '% of ' . $guestCount . ' guests )';
												} elseif ($inventoryItem['customGuestCountUnits'] == 'custom-quantity') {

													$addToTotal = $barPrice * $inventoryItem['customGuestCount'];
													$itemQuantity = '$' . $readablePrice . ' x ' . $inventoryItem['customGuestCount'];
												}
											} else {
												$addToTotal = $barPrice * $guestCount;
												$itemQuantity = '$' . $readablePrice . ' x ' . $guestCount;
											}

											$menuName = $inventoryItem['name'] . ' x1';
										} else {

											if (isset($inventoryItem['customGuestCount'])) {

												if ($inventoryItem['customGuestCountUnits'] == 'percentage') {

													$addToTotal = (int)($barPrice * (float)$hours * ((int)$inventoryItem['customGuestCount'] / 100) * $guestCount);
													$itemQuantity = '$' . $readablePrice . ' x ' . $hours . ' hours x ( ' . $inventoryItem['customGuestCount'] . '% of ' . $guestCount . ' guests )';
												} elseif ($inventoryItem['customGuestCountUnits'] == 'custom-quantity') {

													$addToTotal = (int)($barPrice * (float)$hours * (int)$inventoryItem['customGuestCount']);
													$itemQuantity = '$' . $readablePrice . ' x ' . $hours . ' hours x ' . $inventoryItem['customGuestCount'] . ' guests';
												}
											} else {
												$addToTotal = (int)((int)$barPrice * (float)$hours * (int)$guestCount);
												$itemQuantity = '$' . $readablePrice . ' x ' . $hours . ' hours x ' . $guestCount . ' guests';
											}

											$menuName = $inventoryItem['name'] . ' x1';
										}
										//echo $barPrice;
										$barPrice = 0;

										break;

									case 'decor':

										if ($timelineEvent['name'] == 'CATERING PACKALIST') {
										} else {

											if ($inventoryItem['units'] == 'Per Guest') {

												$addToTotal = $inventoryItem['price'] * $guestCount;
												$itemQuantity = '$' . $readablePrice . ' x ' . $guestCount . ' guests';
												$menuName = $inventoryItem['name'] . ' x' . $guestCount;
											} else {

												$addToTotal = $inventoryItem['price'];
												$itemQuantity = '$' . $readablePrice . ' x1';
												$menuName = $inventoryItem['name'] . ' x1';
											}
										}

										break;

										// food pricing
									case 'Food Estimate':
									case 'food':

										if (count($inventoryItem['packageItems']) > 0) {

											foreach ($inventoryItem['packageItems'] as $packageItem) {

												if ($packageItem['details']['additionalPrice'] > 0) {

													$inventoryItem['price'] += $packageItem['details']['additionalPrice'];
												}

												if (count($packageItem['details']['packageItems']) > 0) {

													foreach ($packageItem['details']['packageItems'] as $subpackageItem) {

														if ($subpackageItem['details']['additionalPrice'] > 0) {

															$inventoryItem['price'] += $subpackageItem['details']['additionalPrice'];
														}
													}
												}
											}
										}

										$readablePrice = number_format(($inventoryItem['price'] / 100), 2);

										switch ($inventoryItem['units']) {

											case 'Per Guest':
												//case 0:

												$addToTotal = $inventoryItem['price'] * $guestCount;
												$itemQuantity = '$' . $readablePrice . ' x ' . $guestCount . ' guests';
												$menuName = $inventoryItem['name'] . ' x' . $guestCount;

												break;

											case 'Per Event':
												//case 1:

												$addToTotal = $inventoryItem['price'];
												$itemQuantity = '$' . $readablePrice . ' x1';
												$menuName = $inventoryItem['name'] . ' x1';

												break;

											case 'Percent of Guest Count':
												//case 2:

												$addToTotal = ($guestCount * .65) * $inventoryItem['price'];
												$itemQuantity = '$' . $readablePrice . ' x ' . round($guestCount * .65) . ' guests';
												$menuName = $inventoryItem['name'] . ' x' . round($guestCount * .65);

												break;

											case 'Per Hour, Per Guest':
												//case 3:

												$startTime = new DateTime($inventoryItem['startTime']);
												$endTime = new DateTime($inventoryItem['endTime']);

												$diff = $endTime->diff($startTime);
												$hours = $diff->h;
												$hours += $diff->days * 24;

												$addToTotal = $inventoryItem['price'] * $hours * $guestCount;
												$itemQuantity = '$' . $readablePrice . ' x ' . $hours . ' hours x ' . $guestCount . ' guests';
												$menuName = $inventoryItem['name'] . ' x ' . $hours . ' hours x ' . $guestCount . ' guests';

												break;
										}

										//echo '|'.$inventoryItem['name'] .' - '. $inventoryItem['units'].' - '. $menuName .'|';

										//$foodTotal = $foodTotal + $addToTotal;

										break;
										// end food pricing

									case 'lighting':

										if (count($inventoryItem['packageItems']) > 0) {

											foreach ($inventoryItem['packageItems'] as $packageItem) {

												if ($packageItem['details']['additionalPrice'] > 0) {

													$inventoryItem['price'] += $packageItem['details']['additionalPrice'];
												}

												if (count($packageItem['details']['packageItems']) > 0) {

													foreach ($packageItem['details']['packageItems'] as $subpackageItem) {

														if ($subpackageItem['details']['additionalPrice'] > 0) {

															$inventoryItem['price'] += $subpackageItem['details']['additionalPrice'];
														}
													}
												}
											}
										}

										switch ($inventoryItem['lightingPackageType']) {

											case 'Basic Package':

												$addToTotal = $inventoryItem['price'];
												$itemQuantity = '$' . $readablePrice . ' x1';

												break;

											case 'Single Item':

												$addToTotal = $inventoryItem['price'];
												$itemQuantity = '$' . $readablePrice . ' x1';

												break;

											default:

												$addToTotal = $inventoryItem['price'];
												$itemQuantity = '$' . $readablePrice . ' x1';
										}

										break;

									case 'rental':

										if ($inventoryItem['rentalType'] == 'CSG') {
											$addToTotal = ($guestCount) * $inventoryItem['price'];
											$itemQuantity = '$' . $readablePrice . ' x ' . $guestCount . ' guests';
											$menuName = $inventoryItem['name'] . ' x' . $guestCount;
											$inventoryObject['details']['units'] = 'Per Guest';
										} else {
											$addToTotal = $inventoryItem['price'];
											$itemQuantity = '$' . $readablePrice . ' x1';
											$menuName = $inventoryItem['name'] . ' x1';
											//$inventoryObject['details']['units'] = '';
										}

										break;

									default:

										if (count($inventoryItem['packageItems']) > 0) {

											foreach ($inventoryItem['packageItems'] as $packageItem) {

												if ($packageItem['details']['additionalPrice'] > 0) {

													$inventoryItem['price'] += $packageItem['details']['additionalPrice'];
												}

												if (count($packageItem['details']['packageItems']) > 0) {

													foreach ($packageItem['details']['packageItems'] as $subpackageItem) {

														if ($subpackageItem['details']['additionalPrice'] > 0) {

															$inventoryItem['price'] += $subpackageItem['details']['additionalPrice'];
														}
													}
												}
											}
										}

										if ($inventoryItem['units'] == 'Per Guest') {

											$addToTotal = $inventoryItem['price'] * $guestCount;
											$itemQuantity = '$' . $readablePrice . ' x ' . $guestCount . ' guests';
											$menuName = $inventoryItem['name'] . ' x' . $guestCount;
										} else {

											$addToTotal = $inventoryItem['price'];
											$itemQuantity = '$' . $readablePrice . ' x1';
											$menuName = $inventoryItem['name'] . ' x1';
										}
								}
							}

							if ($inventoryItem['type'] == 'Food Estimate') {
								$inventoryItem['type'] = 'food';
							}

							// update the inventory item with the line item price
							$inventoryObject['details']['lineItemPrice'] = $addToTotal;
							$inventoryObject['details']['pricingFormula'] = $itemQuantity;
							$inventoryObject['details']['menuName'] = $menuName;

							$timelineEvent['menu'][$menuKey] = $inventoryObject;

							$categoryPrice[$inventoryItem['type']] += $addToTotal;
						}
					}

					$this->pgObjects->update('timeline_events', array(
						'id' => intval($timelineEvent['id']),
						'menu' => $timelineEvent['menu']
					));

					foreach ($categoryPrice as $category => $categoryValue) {

						$ret[$category] += $categoryValue;

						$totalCost += $categoryValue;
					}

					unset($categoryPrice);
				} else {

					$ret['total'] = 0;
				}
			}
		}

		if ($_COOKIE['uid'] == 1) {
			//var_dump($timelineEvents);
			//echo 'looped over timeline events';
		}

		// set venue fee
		//$ret['venueFee'] = 0;

		// decore budget
		$ret['decor_budget'] = $event['decor_budget'];

		// check for decor pricing
		if ($ret['decor'] == '') {

			$ret['decor'] = 0;
		}

		// check for discounts
		if ($event['discount_type'] > 0 and $event['discount_approval'] == 1) {

			// discount exists
			// check the discount type
			switch ($event['discount_type']) {

				case 1:

					// venue fee discount
					$ret['venueFee'] = $ret['venueFee'] - $event['discount_amount'];
					$ret['discount_amount'] = '-' . $event['discount_amount'];
					$ret['discount_type'] = 'venueFeeDiscount';

					break;

				case 2:

					// contract discount
					$ret['discount_amount'] = '-' . $event['discount_amount'];
					$ret['discount_type'] = 'contractDiscount';

					break;
			}
		}

		// check for receipts
		$receipts = $this->pgObjects->where('receipts', ['event_id' => intval($eventId)]);

		if (is_array($receipts) and count($receipts) > 0) {

			$ret['receipts'] = 0;

			foreach ($receipts as $receipt) {

				if ($receipt['approved'] == 1) {

					$ret['receipts'] = $ret['receipts'] + $receipt['amount'];
				}
			}
		}


		// check for coat check attendants ($75 each)
		$eventStaff = $this->pgObjects->where('event_staffing', ['event_id' => intval($eventId)])[0]['staff'];

		$ret['coat_check'] = 0;
		if ($eventStaff['coat_check']) {
			//$ret['coat_check'] = $eventStaff['coat_check'] * 10000;
		}

		// check labor pricing
		$eventStartDate = new DateTime($event['event_start_date']);
		$eventEndDate = new DateTime($event['event_end_date']);
		$modifiedEndDate = new DateTime($event['event_end_date']);
		$modifiedEndDate->add(new DateInterval('PT1H'));

		$diff = $eventStartDate->diff($eventEndDate);

		$eventHours = $diff->format('%h');

		$eventStaff = $this->getEventStaffingObject($event['id'], 0);

		foreach ($eventStaff['staff'] as $job => $slots) {

			if ($job != 'total') {

				$totalStaff += $slots;
			}
		}

		$nonEventStaffStaff = $totalStaff - $eventStaff['staff']['event_staff'] - $eventStaff['staff']['coat_check'];
		$eventStaffStaff = $eventStaff['staff']['event_staff'];
		$eventStaffCost = $eventStaffStaff * 15000;
		$coatCheckStaffCost = $eventStaff['staff']['coat_check'] * 10000;
		$nonEventStaffStaffCost = $nonEventStaffStaff * 20000 + $coatCheckStaffCost;

		$ret['labor'] += $eventStaffCost + $nonEventStaffStaffCost;
		$ret['labor_add'] = 0;
		$ret['labor_add_hours'] = 0;
		$ret['labor_add_staff'] = 0;

		if ($eventHours >= 8) {

			$totalExtraStaff = 0;

			// see if anyone is scheduled to work more than 8 hours
			foreach ($eventStaff['duties'] as $duty) {

				$dutyStartTime = new DateTime($duty['startTime']);

				$dutyDiff = $dutyStartTime->diff($modifiedEndDate);

				if ($dutyDiff->format('%h') > 8) {

					$totalExtraStaff = $totalExtraStaff + count($duty['staff']);
				}
			}

			if ($dutyDiff) {

				$extraHours = 8 - $dutyDiff->format('%h');
			} else {

				$extraHours = 8;
			}

			$extraStaffCharge = $extraHours * $totalExtraStaff * 2000;

			$ret['labor_add'] = $extraStaffCharge;
			$ret['labor_add_hours'] = $extraHours;
			$ret['labor_add_staff'] = $totalExtraStaff;
		}

		// tent pricing
		//$ret['tent'] += $ret['tent'];

		if ($event['tent_status']['rooftop']['heatcool'] == 'heat') {
			$ret['tent'] += 60000;
		}

		if ($event['tent_status']['rooftop']['heatcool'] == 'cool') {
			$ret['tent'] += 80000;
		}

		if ($event['tent_status']['mainPatio']['tent'] == 'yes') {
			$ret['tent'] += 150000;
		}

		foreach ($ret as $category => $price) {
			//echo $category.' - '.$price.'<br />';
			switch ($category) {

				case 'discount_type':
				case 'discount_amount':
				case 'labor_add_hours':
				case 'labor_add_staff':
				case 'venueFee':
				case 'event_planning_package':
				case 'creditCardConvenienceFee':
				case 'Credit Card Convenience Fee':
				case 'CREDITCARDFEE':
				case 'creditCardFee':
				case 'decor':
					break;

				default:

					$ret['subtotal'] += $price;
			}
		}

		$ret['vendors'] = 0;

		$eventStartDate = new DateTime($event['event_start_date']);
		$dateCheck = new DateTime('2016-10-25');

		// check for vendors
		if ($event['vendors']) {

			foreach ($event['vendors'] as $vendor) {

				if ($vendor['approved'] == 1) {

					$eventVendor = $this->pgObjects->getById('vendors', intval($vendor['id']));

					foreach ($vendor['items'] as $vendorItem) {

						if ($eventStartDate > $dateCheck) {

							$ret['vendors'] += ($vendorItem['price'] * ($eventVendor['upchargePercentage'] / 100)) + $vendorItem['price'];
						} else {

							$ret['vendors'] += $vendorItem['price'];
						}
					}
				}
			}
		}

		// add vendor tax if event is after September 30, 2016
		$eventDate = new DateTime($event['event_start_date']);
		$oldest = new DateTime('2016-09-31');



		$toOperationalCost = $ret['food'] + $ret['bar'] + $ret['Food'];
		$ret['operational_cost'] = $this->priceEventOperationCost($toOperationalCost);

		if ($event['discount_type'] == 1) {
		} else {
			$ret['subtotal'] = $ret['subtotal'] + $ret['discount_amount'];
		}

		// if event is non profit, don't apply tax
		$files = $this->getFileByType($event['client_id'], 'client', 'non-profit', 0);
		if (count($files) > 0) {
			$ret['venueFeeTax'] = 0;
			$ret['tax'] = 0;
		} else {
			$ret['tax'] = ($ret['subtotal'] - $ret['labor']) * .0925;

			if ($eventDate > $oldest) {
				$ret['tax'] += ($ret['vendors'] * .0925);
			}

			$ret['venueFeeTax'] = ($ret['venueFee'] * .0925);
		}

		$ret['subtotal'] += $ret['vendors'] + $ret['venueFee'] + $ret['event_planning_package'];

		$ret['tax'] += $ret['venueFeeTax'];

		$ret['total'] = $ret['tax'] + $ret['operational_cost'] + $ret['subtotal'] + $ret['Credit Card Convenience Fee'] + $ret['creditCardFee'];

		return $this->sendData($ret, $json);
	}

	private function priceEventOperationCost($basePriceInCents)
	{

		$operationalCost = .2;

		return ($basePriceInCents * $operationalCost);
	}

	public function pricePackage($menu, $itemObj, $timelineEventId, $total)
	{

		if ($itemObj['details']['packageLimit'] > 0) {

			foreach ($menu as $item) {

				if ($item['packageId'] == $itemObj['id']) {

					$total += intval($item['details']['additionalPrice']);

					if ($item['details']['packageLimit'] > 0) {

						$total = $this->pricePackage($menu, $item, $timelineEventId, $total);
					}
				}
			}
		}

		return $total;
	}

	public function smRoundRobin($json = 1, $staff = null)
	{
		//echo 'test';
		if ($staff == null) {
			$staff = $this->getAllObjects('users', 1, false);
			//$staff = $this->getObjectsWhere('users', array('enabled'=>1), 0, 0);
			//$staff = $this->pgObjects->where('users', array('enabled'=>1), '', 0, false);
		}
		//var_dump($staff);
		$totalPie = 0;
		$totalAssigned = 0;
		//var_dump($staff);
		foreach ($staff as $staffObj) {
			//var_dump($staffObj);
			//echo $staffObj['service'][0]['id'].'-';
			if ($staffObj['service'][0]['id'] == 1150952 and $staffObj['enabled'] == 1) {

				$totalPie += $staffObj['rate'];
				$totalAssigned += $staffObj['daily_requests'];

				$staffObj['request_percent'] = 0;

				$managers[] = $staffObj;
			} elseif ($staffObj['id'] == 920410) {

				$totalPie += $staffObj['rate'];
				$totalAssigned += $staffObj['daily_requests'];

				$staffObj['request_percent'] = 0;

				$managers[] = $staffObj;
			}
		}
		//var_dump($managers);
		foreach ($managers as $manager) {

			$manager['request_percent'] = ($manager['rate'] / $totalPie);
			$manager['assign_percent'] = ($manager['daily_requests'] / $totalAssigned);

			if ($manager['request_percent'] >= $manager['assign_percent']) {

				$ret[] = $manager;
			}
		}

		usort($ret, function ($a, $b) {
			return $a['assign_percent'] > $b['assign_percent'];
		});

		/*
		$user = $this->getObjectsWhere(
			'users',
			array("related_object"=>$ret[0]['id']),
			false,
			true
		);
*/

		return $this->sendData($ret[0], $json);
	}

	public function saveEventPayment($eventId = null)
	{

		if ($eventId == null) {
			$eventId = $_POST->eventId;
			$stringAmount = $_POST->amount;
			$paymentDate = $_POST->date;
			$paymentType = 'check';
			$notes = $_POST->notes;
			$name = $_POST->name;
		}

		$amount = intval(preg_replace('/[^0-9]+/', '', $stringAmount, 10));
		$ccFee = 0;

		if ($paymentType == 'creditcard') {

			//$ccFee = round($amount * .03);
			//$amount = $amount + $ccFee;

		}

		$total = $amount + $ccFee;

		$stringAmount = ($total / 100);

		$details['type'] = 'manual';
		$details['ssl_result_message'] = 'manual_payment';
		$details['txn_type'] = $paymentType;
		$details['ssl_amount'] = ($amount / 100);
		$details['amount'] = ($amount / 100);
		//$details['cc_fee'] = ($ccFee / 100);
		$details['total_charge'] = ($total / 100);
		$details['notes'] = $notes;
		$details['ssl_txn_time'] = $paymentDate;

		$payment = $this->pgObjects->create('payments', array(
			'event_id' => intval($eventId),
			'name' => $name,
			'amount' => intval($total),
			'transaction_details' => $details
		));

		return $payment['id'];
	}

	public function saveObjects($objects = null, $json = 1, $getChildObjs = 0)
	{

		if ($objects == null) {
			$objects = $this->objToArr($_POST->saveData);
		}
		if (isset($_POST->getChildObjs)) {
			if (is_numeric($_POST->getChildObjs)) {
				$getChildObjs = intval($_POST->getChildObjs);
			} elseif (is_object($_POST->getChildObjs)) {
				$getChildObjs = $this->objToArr($_POST->getChildObjs);
			}
		}

		$create_objs = array();
		$update_objs = array();

		if (is_array($objects)) {
			foreach ($objects as $i => $object) {

				if (isset($object['id'])) {

					array_push($update_objs, $object);
				} else {

					array_push($create_objs, $object);
				}
			}
		}

		// response to client
		$ret = array();

		if (is_array($create_objs)) {
			foreach ($create_objs as $i => $object) {

				array_push($ret, $this->pgObjects->create($object['object_bp_type'], $object, 0, $getChildObjs));
			}
		}

		if (is_array($update_objs)) {
			foreach ($update_objs as $i => $object) {

				array_push($ret, $this->pgObjects->update($object['object_bp_type'], $object, $getChildObjs));
			}
		}

		return $this->sendData($ret, $json);
	}

	public function schedulePayments($eventId = null, $json = 1, $totalOnly = 0, $newEventDate = null)
	{

		if ($eventId == null and !empty($_REQUEST['event-id'])) {
			$eventId = $_REQUEST['event-id'];
		}

		if ($totalOnly == 0 and !empty($_REQUEST['total-only'])) {
			$totalOnly = 1;
		}

		//$event = $this->getEventsBy($eventId, 'id', 0)[0];
		$event = $this->pgObjects->getById('events', $eventId);

		if (isset($_REQUEST['new-event-date'])) {
			$newEventDate = $_REQUEST['new-event-date'];
		}

		if ($newEventDate != null) {
			$event['event_start_date'] = $newEventDate;
		}

		$files = $this->getFileByType($event['client_id'], 'client', 'non-profit', 0);
		if (count($files) > 0) {
			$tax = 0;
		} else {
			$tax = .0925;
		}

		if ($event['booked_date'] == null) {
			$bookedDate = new DateTime();
			$today = new DateTime();
		} else {
			$bookedDate = new DateTime($event['booked_date']);
			$today = new DateTime($event['booked_date']);
		}

		$eventPrice = $this->priceEvent($eventId, 0);

		$sixtyDayBeforeEvent = new DateTime($event['event_start_date']);
		$thirtyDayBeforeEvent = new DateTime($event['event_start_date']);
		$fourteenDayBeforeEvent = new DateTime($event['event_start_date']);
		$sixtyDay = new DateTime($event['event_start_date']);
		$thirtyDay = new DateTime($event['event_start_date']);

		$sixtyDayBeforeEvent->modify("-60 day");
		$thirtyDayBeforeEvent->modify("-30 day");
		$fourteenDayBeforeEvent->modify("-10 day");
		$sixtyDay->modify("+60 day");
		$thirtyDay->modify("+30 day");

		/*
		if($eventPrice['eventPlanningPackage']){

			$eventPlanning = 1;

			$eventPlanningPrice = $eventPrice['eventPlanningPackage'];
			$eventPlanningTax = 0;
			$eventPlanningTotal = $eventPlanningPrice + $eventPlanningTax;

		}
*/

		if ($eventPrice['Credit Card Convenience Fee']) {

			$ccFee = 1;

			$ccFeeArray = array(
				'date' => $today->format('Y-m-d'),
				'amount' => $eventPrice['Credit Card Convenience Fee'],
				'tax' => 0,
				'type' => 'ConvenienceFee'
			);
		} else {

			$eventPrice['Credit Card Convenience Fee'] = 0;
		}

		if ($today > $sixtyDayBeforeEvent) {

			// event is too soon, use 60 day contract pricing schedule
			// 100% of venue fee + tax due right away
			// 100% of IEC is due after guest count

			$venueFee = $eventPrice['venueFee'] + ($eventPrice['venueFeeTax']);

			$contractFee = $eventPrice['total'] - $venueFee;

			if ($eventPlanning === 1) {

				$paymentSchedule = array(

					0 => array(
						'date' => $bookedDate->format('Y-m-d'),
						'amount' => $venueFee,
						'tax' => 0,
						'type' => 'venuefee'
					),

					1 => array(
						'date' => $today->modify('+1 day')->format('Y-m-d'),
						'amount' => $eventPlanningPrice,
						'tax' => 0,
						'type' => 'eventplanning'
					),

					2 => array(
						'date' => $fourteenDayBeforeEvent->format('Y-m-d'),
						'amount' => $contractFee - $eventPlanningTotal,
						'tax' => 0,
						'type' => 'contract'
					)

				);
			} else {

				/*
				if($_COOKIE['uid'] == 1){

					print_r($eventPrice);
					var_dump($venueFee);
				}
*/

				$paymentSchedule = array(

					0 => array(
						'date' => $bookedDate->format('Y-m-d'),
						'amount' => $venueFee,
						'tax' => 0,
						'type' => 'venuefee'
					),

					1 => array(
						'date' => $fourteenDayBeforeEvent->format('Y-m-d'),
						'amount' => $contractFee,
						'tax' => 0,
						'type' => 'contract'
					)

				);
			}
		} else {

			// event is in the future, use normal pricing schedule
			// 50% of venue fee + tax due today
			// 50% of venue fee + tax due 30 days later
			// 50% of estimated IEC is due 60 days out
			// remaining IEC is due 14 days out

			$venueFee = $eventPrice['venueFee'] + ($eventPrice['venueFeeTax']);
			$contractFee = ($eventPrice['total'] - $venueFee - $eventPrice['Credit Card Convenience Fee']);

			$firstVenueFee = round($venueFee / 2);
			$firstVenueFeeDue = $bookedDate->format('Y-m-d');
			$firstVenueFeeTax = 0;

			$secondVenueFee = $venueFee - $firstVenueFee;
			$secondVenueFeeDue = $bookedDate->modify("+30 day")->format('Y-m-d');
			$secondVenueFeeTax = 0;

			$firstContractFee = round(($contractFee / 2));
			$secondContractFee = round($contractFee - $firstContractFee, 0, PHP_ROUND_HALF_UP);

			if ($sixtyDayBeforeEvent < $today) {

				if ($eventPlanning === 1) {

					// the second venue payment is after the first contract payment
					// combine the venue payments
					$paymentSchedule = array(

						0 => array(
							'date' => $firstVenueFeeDue,
							'amount' => $firstVenueFee + $secondVenueFee,
							'tax' => $firstVenueFeeTax + $secondVenueFeeTax,
							'type' => 'venuefee'
						),

						1 => array(
							'date' => $today->modify('+1 day')->format('Y-m-d'),
							'amount' => $eventPlanningPrice,
							'tax' => 0,
							'type' => 'eventplanning'
						),

						2 => array(
							'date' => $sixtyDayBeforeEvent->format('Y-m-d'),
							'amount' => $firstContractFee - ($eventPlanningTotal / 2),
							'tax' => 0,
							'type' => 'contract'
						),

						3 => array(
							'date' => $fourteenDayBeforeEvent->format('Y-m-d'),
							'amount' => $secondContractFee - ($eventPlanningTotal / 2),
							'tax' => 0,
							'type' => 'contract'
						)

					);
				} else {

					// the second venue payment is after the first contract payment
					// combine the venue payments
					$paymentSchedule = array(

						0 => array(
							'date' => $firstVenueFeeDue,
							'amount' => $firstVenueFee + $secondVenueFee,
							'tax' => $firstVenueFeeTax + $secondVenueFeeTax,
							'type' => 'venuefee'
						),

						1 => array(
							'date' => $sixtyDayBeforeEvent->format('Y-m-d'),
							'amount' => $firstContractFee,
							'tax' => 0,
							'type' => 'contract'
						),

						2 => array(
							'date' => $fourteenDayBeforeEvent->format('Y-m-d'),
							'amount' => $secondContractFee,
							'tax' => 0,
							'type' => 'contract'
						)

					);
				}
			} else {

				if ($eventPlanning === 1) {

					$paymentSchedule = array(

						0 => array(
							'date' => $firstVenueFeeDue,
							'amount' => $firstVenueFee,
							'tax' => $firstVenueFeeTax,
							'type' => 'venuefee'
						),

						1 => array(
							'date' => $secondVenueFeeDue,
							'amount' => $secondVenueFee,
							'tax' => $secondVenueFeeTax,
							'type' => 'venuefee'
						),

						2 => array(
							'date' => $today->modify('+1 day')->format('Y-m-d'),
							'amount' => $eventPlanningPrice,
							'tax' => 0,
							'type' => 'eventplanning'
						),

						3 => array(
							'date' => $sixtyDayBeforeEvent->format('Y-m-d'),
							'amount' => $firstContractFee - ($eventPlanningTotal / 2),
							'tax' => 0,
							'type' => 'contract'
						),

						4 => array(
							'date' => $fourteenDayBeforeEvent->format('Y-m-d'),
							'amount' => $secondContractFee - ($eventPlanningTotal / 2),
							'tax' => 0,
							'type' => 'contract'
						)

					);
				} else {

					$paymentSchedule = array(

						0 => array(
							'date' => $firstVenueFeeDue,
							'amount' => $firstVenueFee,
							'tax' => $firstVenueFeeTax,
							'type' => 'venuefee'
						),

						1 => array(
							'date' => $secondVenueFeeDue,
							'amount' => $secondVenueFee,
							'tax' => $secondVenueFeeTax,
							'type' => 'venuefee'
						),

						2 => array(
							'date' => $sixtyDayBeforeEvent->format('Y-m-d'),
							'amount' => $firstContractFee,
							'tax' => 0,
							'type' => 'contract'
						),

						3 => array(
							'date' => $fourteenDayBeforeEvent->format('Y-m-d'),
							'amount' => $secondContractFee,
							'tax' => 0,
							'type' => 'contract'
						)

					);
				}
			}
		}

		//var_dump($paymentSchedule);

		// check if any vendors need to be paid
		if (is_array($event['vendors'])) {
			foreach ($event['vendors'] as $vendor) {

				if ($vendor['depositAmount'] > 0 and $vendor['depositDueDate']) {

					$vendorPaymentDueDate = new DateTime($vendor['depositDueDate']);

					$vendorPayments[$vendor['invoiceNumber']]['date'] = $vendorPaymentDueDate->format('Y-m-d');
					$vendorPayments[$vendor['invoiceNumber']]['amount'] = $vendor['depositAmount'];
					$vendorPayments[$vendor['invoiceNumber']]['tax'] = 0;
					$vendorPayments[$vendor['invoiceNumber']]['type'] = 'third_party_vendors';

					//array_push($paymentSchedule, $vendorPayment);

				}
			}

			foreach ($vendorPayments as $vendorPayment) {

				array_push($paymentSchedule, $vendorPayment);
			}
		}

		if ($ccFee == 1) {
			array_unshift($paymentSchedule, $ccFeeArray);
		}

		if (!function_exists('dateSort')) {

			function dateSort($a, $b)
			{

				$t1 = strtotime($a['date']);
				$t2 = strtotime($b['date']);
				return $t1 - $t2;
			}
		}
		usort($paymentSchedule, 'dateSort');

		// calculate total and check against current event price
		$totalFees = 0;
		$totalTax = 0;
		foreach ($paymentSchedule as $payment) {

			$totalFees = $totalFees + $payment['amount'];
			$totalTax = 0;
			$total = $totalFees + $totalTax;
		}

		$totalArray = array(

			'date' => $fourteenDayBeforeEvent->format('Y-m-d'),
			'amount' => $totalFees,
			'tax' => 0,
			'type' => 'total'

		);

		array_push($paymentSchedule, $totalArray);

		//var_dump($paymentSchedule);

		if ($totalOnly == 1) {

			return $this->sendData($totalArray, $json);
		} else {

			return $this->sendData($paymentSchedule, $json);
		}
	}

	public function refundStripePayment($transactionId = null, $json = 1)
	{

		/*
		error_reporting(E_ALL);
		ini_set('display_errors', '1');
*/

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		if ($transactionId == null) {
			$transactionId = $_POST->transactionId;
		} else {
			return false;
		}

		$re = \Stripe\Refund::create(array(
			"charge" => $transactionId
		));

		return $this->sendData($re, $json);
	}

	public function renameFile($fileMetaData = null)
	{

		if ($fileMetaData == null) {
			$fileMetaData = $_POST;
		}

		$renamed = $this->files->renameFile($fileMetaData, 0);

		return $this->sendData($renamed, 1);
	}

	public function removeTagFromObject($tagObj = null, $json = 1)
	{

		if ($tagObj == null) {
			$tagId = $_POST->tagId;
			$objectId = $_POST->typeId;
			$objectType = $_POST->type;
		}

		$tagType = 'tagged_with';
		if ($_POST->tagType === 'contacts') {
			$tagType = 'shared_with';
		}

		return $this->sendData(
			$this->pgObjects->removeTagFromObject($objectId, $tagId, $tagType),
			$json
		);
	}

	public function restoreObjs($jsonObject = null, $json = 1)
	{

		$objectId = 0;
		if (is_numeric($_POST->id)) {
			$objectId = intval($_POST->id);
		} elseif (is_object($_POST->id)) {
			$objectId = $this - objToArr($_POST->id);
		} elseif (is_array($_POST->id)) {
			$objectId = $_POST->id;
		}

		if (is_array($objectId)) {

			foreach ($objectId as $i => $objId) {

				$this->pgObjects->restore($_POST->type, $objId);
			}

			return true;
		} else {

			if ($this->pgObjects->restore($_POST->type, $_POST->id)) {

				return true;
			}
		}
	}

	public function runSteps($json = 1)
	{
		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');
		$objId = intval($_POST->objId);
		$steps = $this->objToArr($_POST->steps);

		$ret = [];
		if (empty($steps)) {

			$ret = false;
		} else {

			$response = $this->pgObjects->runSteps(
				$objId,
				$steps,
				true
			);

			$ret = [
				'msg' => 			$response, 'response' => 	$response['updated'], '_notify_usr' => 	true, '_completed' => 	$response['run']
			];
		}

		return $this->sendData($ret, $json);
	}

	public function saveNewInstanceObject($json = 1)
	{

		$objArray = json_decode(json_encode($_POST), true);
		//var_dump($objArray);
		$ret = $this->pgObjects->createInstance('instances', $objArray['instanceObj']);

		//$user = $this->getObjectById('users', $_COOKIE['uid'], false);

		$user =  $objArray['user'];

		unset($user['id']);

		$user['instance'] = $objArray['instanceObj']['instance'];
		$user['type'] = 'Admin';
		//var_dump($user);
		$this->pgObjects->setInstance($objArray['instanceObj']['instance']);

		$newUser = $this->pgObjects->create('users', $user, 0, 0);
		//var_dump($newUser);
		foreach ($user as $k => $v) {

			if ($k != 'id') {
				$newUser[$k] = $v;
			}
		}

		//$this->updateObject($newUser, 'users', false);

		// create files bucket
		//$this->files->createBucket('../../../../_production/pagoda/_files/_instances/testing'. $objArray['instance']);

		return $this->sendData($ret, $json);
	}

	private function getAllowedTeams($userId, $includeUser = false)
	{

		$allowedTeams = [];

		$groups = $this->pgObjects->where(
			'groups',
			array(
				'tagged_with' => [$userId]
			)
		);

		if (is_array($groups) && !empty($groups)) {
			foreach ($groups as $key => $val) {
				array_push($allowedTeams, $val['id']);
			}
		}

		if ($includeUser) {
			array_push($allowedTeams, $userId);
		}

		return $allowedTeams;
	}

	public function search($json = 1)
	{

		$permissionsCheck = false;

		if (is_null($queryObj)) {
			$queryObj = json_decode($_REQUEST['json'], true)['where'];
		}
		if (!is_array($queryObj)) {
			$queryObj = [];
		}

		if (isset($queryObj['permissions_check'])) {
			$permissionsCheck = true;
		}

		if ($permissionsCheck) {

			$allowed = $this->getAllowedTeams(intval($_COOKIE['uid']), true);

			$queryObj['tagged_with'] = array(
				'type' => 'any', 'values' => $allowed
			);

			unset($queryObj['permissions_check']);
		}

		$objectType = $_REQUEST['searchType'];
		$queryObj['name'] = [
			'type' => 'contains',
			'value' => $_REQUEST['value']
		];

		$data = [];

		$selectionObj = [
			'name' => true,
			'description' => true,
			'category' => [
				'name' => true
			],
			'group_type' => true,
			'inventory_categories' => [
				'name' => true
			],
			'uom' => true,
			'is_template' => true
		];

		$requestSelection = json_decode($_REQUEST['selection'], true);

		if (is_null($requestSelection)) {
			$requestSelection = json_decode($_REQUEST['json'], true)['selection'];
		}

		// !1552:  If flagged to also show cross-instance filters (and searching for entity_types)
		//  	   also pull in data_filter objects shared_with the current instance
		if (
			is_array($requestSelection)
			&& $objectType !== 'system_tags'
			&& $objectType !== 'Tag'
		) {

			$selectionObj = !empty($requestSelection['where']) ? $requestSelection : $selectionObj;

			if (is_array($selectionObj) && !empty($selectionObj['where'])) {
				foreach ($selectionObj['where'] as $key => $val) {
					$queryObj[$key] = $val;
				}
				unset($selectionObj['where']);
			}

			$limit = 50;
			if (
				array_key_exists('limit', $_REQUEST)
				&& intval($_REQUEST['limit']) > 0
			) {
				$limit = intval($_REQUEST['limit']);
			}

			if (
				array_key_exists('searchUids', $_REQUEST)
				&& intval($_REQUEST['searchUids']) > 0
			) {

				$selectionObj['object_uid'] = true;
				$queryObj['search'] = [
					'fields' => 		['name'], 'type' => 		'or', 'value' => 		$queryObj['name']['value'], 'fuzzySearch' => 	true
				];
				unset($queryObj['name']);
			}

			if ($objectType === 'ANY-SET') {
				$objectType = '#.';
			}

			$selectionObj['DISTANCE'] = true;

			$data = $this->pgObjects->where(
				$objectType,
				$queryObj,
				'',
				$selectionObj,
				true,
				0,
				'last_updated',
				'desc',
				$limit,
				null,
				array(),
				'string',
				true
			);

			if (
				array_key_exists('searchUids', $_REQUEST)
				&& intval($_REQUEST['searchUids']) > 0
			) {

				if (is_numeric($queryObj['search']['value'])) {

					$uidFilter = [
						'object_uid' => intval($queryObj['search']['value'])
					];
					if ($queryObj['tagged_with']) {
						$uidFilter['tagged_with'] = $queryObj['tagged_with'];
					}
					$uidMatch = $this->pgObjects->where(
						$objectType,
						$uidFilter,
						'',
						$selectionObj,
						true,
						0,
						'date_created',
						'asc',
						1,
						null,
						array(),
						'string',
						true
					);

					if (!empty($uidMatch) && !empty($uidMatch[0])) {
						array_unshift($data, $uidMatch[0]);
					}
				}
			}
		} else {

			switch ($objectType) {

				case 'Tag':
				case 'system_tags':

					// error_reporting(E_ALL);
					// ini_set('display_errors', '1');

					//!TODO: maintain a page count for full network request
					// get Team and Project groups

					$data = array();
					$value = $queryObj['name']['value'];

					if ($value[0] == ':') {

						if (strpos($value, ':companies') !== false || strpos($value, ':company') !== false) {

							if (strpos($value, ':companies') !== false) {
								$value = trim(str_replace(':companies', '', $value));
							} else if (strpos($value, ':company') !== false) {
								$value = trim(str_replace(':company', '', $value));
							}

							$data = array_merge($data, $this->pgObjects->where(
								'companies',
								[
									'name' => [
										'type' => 'contains',
										'value' => $value
									]
								],
								'',
								[
									'name' => true,
									'color' => true,
									'profile_image' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							));
						} else if (strpos($value, ':contact') !== false) {

							if (strpos($value, ':contacts') !== false) {
								$value = trim(str_replace(':contacts', '', $value));
							} else if (strpos($value, ':contact') !== false) {
								$value = trim(str_replace(':contact', '', $value));
							}

							$data = $this->pgObjects->where(
								'contacts',
								[
									'name' => [
										'type' => 'or',
										'fields' => ['fname', 'lname'],
										'value' => $value
									]
								],
								'',
								[
									'fname' => true,
									'lname' => true,
									'color' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							);
						} else if (strpos($value, ':project') !== false) {

							if (strpos($value, ':projects') !== false) {
								$value = trim(str_replace(':projects', '', $value));
							} else if (strpos($value, ':project') !== false) {
								$value = trim(str_replace(':project', '', $value));
							}

							$data = $this->pgObjects->where(
								'groups',
								[
									'name' => [
										'type' => 'contains',
										'value' => $value
									],
									'group_type' => 'Project'
								],
								'',
								[
									'name' => true,
									'color' => true,
									'description' => true,
									'group_type' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							);
						} else if (strpos($value, ':team') !== false) {

							if (strpos($value, ':teams') !== false) {
								$value = trim(str_replace(':teams', '', $value));
							} else if (strpos($value, ':team') !== false) {
								$value = trim(str_replace(':team', '', $value));
							}

							$data = $this->pgObjects->where(
								'groups',
								[
									'name' => [
										'type' => 'contains',
										'value' => $value
									],
									'group_type' => 'Team'
								],
								'',
								[
									'name' => true,
									'color' => true,
									'description' => true,
									'group_type' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							);
						} else if (strpos($value, ':user') !== false) {

							if (strpos($value, ':users') !== false) {
								$value = trim(str_replace(':users', '', $value));
							} else if (strpos($value, ':user') !== false) {
								$value = trim(str_replace(':user', '', $value));
							}

							$data = $this->pgObjects->where(
								'users',
								[
									'name' => [
										'type' => 'or',
										'fields' => ['fname', 'lname'],
										'value' => $value
									]
								],
								'',
								[
									'fname' => true,
									'lname' => true,
									'color' => true,
									'profile_image' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							);
						}

						return $this->sendData(['results' => $data], $json);
					} else if ($value[0] == '@') {

						if (strpos($value, '@') !== false) {
							$value = trim(str_replace('@', '', $value));
						} else if (strpos($value, '@') !== false) {
							$value = trim(str_replace('@', '', $value));
						}

						$data = $this->pgObjects->where(
							'users',
							[
								'name' => [
									'type' => 'or',
									'fields' => ['fname', 'lname'],
									'value' => $value
								]
							],
							'',
							[
								'fname' => true,
								'lname' => true,
								'color' => true,
								'profile_image' => true
							],
							false,
							0,
							'null',
							'asc',
							100,
							null,
							array(),
							'string',
							true
						);
					} else if ($value[0] == '#') {

						if (strpos($value, '#') !== false) {
							$value = trim(str_replace('#', '', $value));
						} else if (strpos($value, '#') !== false) {
							$value = trim(str_replace('#', '', $value));
						}

						$data = $this->pgObjects->where(
							'system_tags',
							[
								'tag' => [
									'type' => 'contains',
									'value' => $value
								]
							],
							'',
							[
								'tag' => true,
								'color' => true,
								'icon' => true
							],
							false,
							0,
							'null',
							'asc',
							100,
							null,
							array(),
							'string',
							true
						);
					} else {

						if (
							empty($queryObj['tagType'])
							|| $queryObj['tagType'] === 'team'
							|| $queryObj['tagType'] === 'project'
							|| $queryObj['tagType'] === 'job'
						) {

							$groupTypeFilter = [
								'type' => 'or', 'values' => [
									'Team', 'Project', 'JobType'
								]
							];

							if ($queryObj['tagType']) {

								if ($queryObj['tagType'] === 'team') {

									$groupTypeFilter = 'Team';
								} elseif ($queryObj['tagType'] === 'project') {

									$groupTypeFilter = 'Project';
								} elseif ($queryObj['tagType'] === 'job') {

									$groupTypeFilter = 'JobType';
								}
							}

							$data = $this->pgObjects->where(
								'groups',
								[
									'name' => [
										'type' => 'contains',
										'value' => $_REQUEST['value']
									],
									'group_type' => $groupTypeFilter
								],
								'',
								[
									'name' => true,
									'color' => true,
									'description' => true,
									'group_type' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							);
						}

						// if count not hit, get users, contacts and companies
						if (
							empty($queryObj['tagType'])
							|| $queryObj['tagType'] === 'person'
						) {

							$data = array_merge($data, $this->pgObjects->where(
								'users',
								[
									'name' => [
										'type' => 'or',
										'fields' => ['fname', 'lname'],
										'value' => $_REQUEST['value']
									]
								],
								'',
								[
									'fname' => true,
									'lname' => true,
									'color' => true,
									'profile_image' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							));

							// if count not hit, get users, contacts and companies
							$data = array_merge($data, $this->pgObjects->where(
								'contacts',
								[
									'name' => [
										'type' => 'or',
										'fields' => ['fname', 'lname'],
										'value' => $_REQUEST['value']
									]
								],
								'',
								[
									'fname' => true,
									'lname' => true,
									'color' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							));

							// if count not hit, get users, contacts and companies
							$data = array_merge($data, $this->pgObjects->where(
								'companies',
								[
									'name' => [
										'type' => 'contains',
										'value' => $_REQUEST['value']
									]
								],
								'',
								[
									'name' => true,
									'color' => true,
									'profile_image' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							));
						}

						if (
							empty($queryObj['tagType'])
							|| $queryObj['tagType'] === 'tag'
						) {

							// if count not hit, get system_tags
							$data = array_merge($data, $this->pgObjects->where(
								'system_tags',
								[
									'tag' => [
										'type' => 'contains',
										'value' => $_REQUEST['value']
									]
								],
								'',
								[
									'tag' => true,
									'color' => true,
									'icon' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							));
						}

						// Get space-like entities
						$spaceTypes = __::pluck(
							$this->pgObjects->where(
								'entity_type',
								[
									'is_space' => true
								],
								'',
								[
									'bp_name' => 	true, 'name' => 	true, 'icon' => 	true
								]
							),
							'bp_name'
						);

						if (!empty($spaceTypes)) {

							foreach ($spaceTypes as $i => $name) {
								$spaceTypes[$i] = '#' . $name;
							}

							$data = array_merge($data, $this->pgObjects->where(
								$spaceTypes,
								[
									'name' => [
										'type' => 'contains',
										'value' => $_REQUEST['value']
									]
								],
								'',
								[
									'name' => true
								],
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							));
						}
					}

					break;

				case 'users':

					if (strlen($_REQUEST['value']) < 3) {

						$data = [];
					} else {

						$options = [
							'fname' => true,
							'lname' => true,
							'color' => true,
							'profile_image' => true
						];

						if (
							empty($_REQUEST['value'])
							&& empty($queryObj['tagged_with'])
						) {

							$data = $this->pgObjects->getAll(
								'users',
								$options,
								false,
								0,
								'date_created',
								'asc',
								100
							);
						} else {

							$data = $this->pgObjects->where(
								'users',
								[
									'fname' => [
										'type' => 'or',
										'fields' => ['fname', 'lname'],
										'value' => $_REQUEST['value']
									]
									//, 'tagged_with' => $queryObj['tagged_with'] --> this line seems to be causing a search result of 'null'
								],
								'',
								$options,
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							);
						}
					}

					break;

				case 'contacts':

					if (strlen($_REQUEST['value']) < 3) {

						$data = [];
					} else {

						$options = [
							'fname' => true,
							'lname' => true,
							'company' => [
								'name' => true
							]
						];

						if (!$queryObj) {

							$queryObj = [
								'fname' => [
									'type' => 'or',
									'fields' => ['fname', 'lname'],
									'value' => $_REQUEST['value']
								]
								//, 'tagged_with' => $queryObj['tagged_with'] --> this line seems to be causing a search result of 'null'
							];
						}

						if (
							empty($_REQUEST['value'])
							&& empty($queryObj['tagged_with'])
							&& !$queryObj
						) {

							$data = $this->pgObjects->getAll(
								'contacts',
								$options,
								false,
								0,
								'date_created',
								'asc',
								100
							);
						} else {

							$data = $this->pgObjects->where(
								'contacts',
								$queryObj,
								'',
								$options,
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							);
						}
					}

					break;

				case 'companies':

					if (strlen($_REQUEST['value']) < 3) {

						$data = [];
					} else {

						$options = [
							'name' => true
						];

						if (!$queryObj) {

							$queryObj = [
								'name' => [
									'type' => 'contains',
									'value' => $_REQUEST['value']
								]
								//, 'tagged_with' => $queryObj['tagged_with'] --> this line seems to be causing a search result of 'null'
							];
						}

						if (
							empty($_REQUEST['value'])
							&& empty($queryObj['tagged_with'])
							&& !$queryObj
						) {

							$data = $this->pgObjects->getAll(
								'companies',
								$options,
								false,
								0,
								'date_created',
								'asc',
								100
							);
						} else {

							$data = $this->pgObjects->where(
								'companies',
								$queryObj,
								'',
								$options,
								false,
								0,
								'null',
								'asc',
								100,
								null,
								array(),
								'string',
								true
							);
						}
					}

					break;

				default;
					$data = $this->pgObjects->where(
						$objectType,
						$queryObj,
						'',
						[
							'name' => true,
							'description' => true,
							'category' => [
								'name' => true
							],
							'group_type' => true,
							'inventory_categories' => [
								'name' => true
							],
							'uom' => true
						],
						false,
						0,
						'null',
						'asc',
						100,
						null,
						array(),
						'string',
						true
					);
					break;
			}
		}

		return $this->sendData(['results' => $data], $json);
	}

	public function sendEmail($to = null, $from = null, $subject = null, $mergevars = null, $email_tags = null, $json = 1, $typeId = null)
	{

		// single email
		if ($to == null and $_POST->to) {

			$to = $_POST->to;
			$subject = $_POST->subject;
			$mergevars = $_POST->mergevars;
			$email_tags = $_POST->emailTags;
			$type = $_POST->type;
			$typeId = $_POST->typeId;
			$newThread = $_POST->newThread;
			$threadId = $_POST->threadId;
			$from = $_POST->from;
			$clicked = intval($_POST->clicked);

			// bulk send
		} else if ($_POST->emails) {
			$bulk = true;
			$emails = $_POST->emails;
		}

		if (!$bulk) {

			$taggedWithArr = array();

			if (empty($to)) {
				return false;
			}

			if (empty($from)) {
				$from = $this->fromEmailAddress;
			}

			if ($typeId) {

				// Needed to grab tags from related object
				$relatedObject = $this->pgObjects->getById(
					'',
					$typeId,
					[]
				);

				foreach ($relatedObject['tagged_with'] as $id) {

					array_push($taggedWithArr, intval($id));
				}

				array_push($taggedWithArr, intval($relatedObject['id']));
			}

			if ($email = $this->comm->sendMandrillEmail($to, $from, $subject, $mergevars, $email_tags, $typeId, 1, $type, $newThread, $threadId, $this->appConfig['instance'], $clicked, $taggedWithArr)) {

				return $this->sendData($email, $json);
			}
		} else {

			$ret = [];

			foreach ($emails as $key => $toSend) {

				$taggedWithArr = [];

				// Needed to grab tags from related object
				$relatedObject = $this->pgObjects->getById(
					'',
					$toSend->typeId,
					[]
				);

				foreach ($relatedObject['tagged_with'] as $id) {

					array_push($taggedWithArr, intval($id));
				}

				array_push($taggedWithArr, intval($relatedObject['id']));

				array_push($ret, $this->comm->sendMandrillEmail(
					$toSend->to,
					$toSend->from,
					$toSend->subject,
					$toSend->mergevars,
					$toSend->emailTags,
					$toSend->typeId,
					1,
					$toSend->type,
					$toSend->newThread,
					$toSend->threadId,
					$this->appConfig['instance'],
					$clicked,
					$taggedWithArr
				));
			}

			return $this->sendData($ret, $json);
		}

		return $this->sendData(false, $json);
	}

	public function sendLifeBookEmail($offset = 0, $loops = 0, $total = 0)
	{

		/*
		error_reporting(E_ALL);
		ini_set('display_errors', '1');
*/

		$pageLength = 50;
		$count = 0;
		$sendToList = [];

		$contacts = $this->pgObjects->getAll('contacts', 2, true, $offset, 'date_created', 'asc', $pageLength);

		if (count($contacts) > 0) {

			$loops++;

			foreach ($contacts as $c) {

				foreach ($c['contact_info'] as $info) {

					if ($info['type']['data_type'] == 'email' and $info['is_primary'] == 'yes') {

						$cancelled = $this->pgObjects->where('requests', array('contact' => $c['id'], 'status' => array('type' => 'or', 'values' => array('Cancelled', 'On Hold'))), '', 0, false);

						if (count($cancelled) == 0) {

							$total++;

							$unsubscribe = $this->getUrl() . '/api/unsubscribeEmail.php?email=' + $info['id'];

							$mergevars = array(
								'TITLE' => 'Here is the title',
								'SUBJECT' => 'This is the subject',
								'BODY' => 'Testing...',
								'INSTANCE_NAME' => $appConfig['systemName']
							);

							$this->sendEmail(array('<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'), $appConfig['emailFrom'], 'Testing', $mergevars, 'Internal Email', false);

							$sendToList[] = $info['info'];

							ob_flush();
							flush();
						}
					}
				}
			}

			echo 'Sending to ' . count($sendToList) . '<br />';
			echo 'Current Offset ' . count($offset) . '<br />';

			if ($loops > 0) {
				echo 'Complete! Sent ' . $total . ' emails.';
				die();
			}

			$this->sendLifeBookEmail(($offset + count($contacts)), $loops, $total);
		} else {

			echo 'Complete! Sent ' . $total . ' emails.';

			return;
		}
	}

	public function sendSMS($to = null, $message = null)
	{

		if ($to == null) {

			$to = $_POST->to;
			$message = $_POST->message;

			if ($_POST->from) {
				$from = $_POST->from;
			} else {
				$from = null;
			}
		}

		if (empty($to)) {
			return false;
		}

		if ($this->comm->sendSMS($to, $message, $from)) {
			return true;
		}
	}

	public function signContract($request = null, $json = 1)
	{

		$contract = false;
		$contextProject = false;
		$upd = [];
		$ret = [];

		// Pull in the request obj from $_POST, if it is not already set
		if ($request === null) {

			$request = $this->objToArr(
				$_POST
			);
		}

		$contract = $this->pgObjects->getById(
			'contracts',
			$request['contractId'],
			[
				'related_object' => [
					'type' => true, 'name' => true
				]
			]
		);

		// Grab the context project
		$contextProject = $contract['related_object'];

		// If a full request obj is not given, don't do anything and just
		// return false.
		if (
			empty($contract)
			|| empty($contextProject)
			|| empty($request['signer_ip'])
			|| empty($request['signer_name'])
			|| empty($request['signer_email'])
			|| empty($request['signed_on'])
		) {

			return $this->sendData(
				false,
				$json
			);
		}

		// Update the contract, recording the signer's IP address, email, name,
		// the current timestamp, and the html of the merged contract. Also,
		// set the status of the contract to 'Signed'.
		$updated = $this->pgObjects->update(
			'contracts',
			[
				'id' => 				$contract['id'], 'signer_ip' => 		$request['signer_ip'], 'signer_name' => 		$request['signer_name'], 'signer_email' => 	$request['signer_email']

				// !TODO: Move the timestamp generation over to
				// this func, instead of on the front-end.
				, 'signed_on' => 		$request['signed_on'], 'status' => 			'Signed', 'html_string' => 		$request['html_string']
			],
			1
		);
		$ret = $updated;

		// If this is the first contract signed in the context project, check
		// if the project_type is configured to trigger a state transition in
		// that case, and if so, transition the project.
		$signedContracts = $this->pgObjects->countObjs(
			'contracts',
			[
				'related_object' => $contextProject['id'], 'status' => 		'Signed'
			]
		)[0];

		// If this is the only signed contract, check the project type for a
		// user-configured state transition.
		//if ($signedContracts['count'] === 1) {
		if ($signedContracts['count'] == 1) {

			$projectType = $contextProject['type'];
			if (
				array_key_exists('onFirstSignature', $projectType)
				&& is_int($projectType['onFirstSignature'])
			) {
				$transitionResponse = $this->updateState(
					$contextProject['id'],
					null,
					$projectType['onFirstSignature'],
					$this->getUrl() . '/app/' . $contextProject['instance'] . '#mystuff&1=o-project-' . $contextProject['id'] . '-' . urlencode($contextProject['name']),
					'',
					false,
					0,
					function ($response) use ($ret) {

						return $ret;
					}
				);
			}
		}

		return $this->sendData($ret, $json);
	}

	public function simpleSum($objectType = null, $field = null, $where = null, $json = 1)
	{

		if (
			$objectType == null
			or $field == null
			or $where == null
		) {
			$objectType = 	$_POST->objectType;
			$field = 		$_POST->field;
			$where = 		$this->objToArr($_POST->where);
		}

		if (array_key_exists('search', $where)) {
			unset($where['search']);
		}

		$ret = $this->pgObjects->simpleSum($objectType, $field, $where);

		return $this->sendData(
			$ret,
			$json
		);
	}

	public function slow_equals($a, $b)
	{

		$diff = strlen($a) ^ strlen($b);

		for ($i = 0; $i < strlen($a) && $i < strlen($b); $i++) {

			$diff |= ord($a[$i]) ^ ord($b[$i]);
		}

		return $diff === 0;
	}

	public function sum($queryObj = null, $json = 1)
	{

		if ($queryObj == null) {
			$objectType = $_POST->objectType;
			$field = $_POST->field;
			$dateBy = $_POST->dateBy;
			$groupBy = $_POST->groupBy;
			$groupOn = $_POST->queryObj->groupOn;
			$limit = $_POST->limit;
			$start = $_POST->queryObj->dateRange->start;
			$end = $_POST->queryObj->dateRange->end;
			$search = $_POST->search;
			$queryObj = $this->objToArr($_POST->queryObj);

			if ($_POST->queryObj->groupBy) {
				$groupBy = $_POST->queryObj->groupBy;
				unset($_POST->queryObj->groupBy);
			}

			unset($_POST->queryObj->dateRange);

			$queryObj = $_POST->queryObj;

			if ($groupOn) {
				$queryObj->groupOn = $groupOn;
			}

			if ($_POST->queryObj->dateField) {
				$dateField = $_POST->queryObj->dateField;
				unset($queryObj->dateField);
				unset($_POST->queryObj->dateField);
			} else {
				$dateField = 'date_created';
			}
		}

		$queryObj = $this->objToArr($queryObj);

		$ret = $this->pgObjects->sum($objectType, $field, $dateBy, $groupBy, $start, $end, 0, $limit, $search, $queryObj, $dateField);

		$this->sendData($ret, $json);
	}

	public function subscribeEmailToMailchimp($listId = 1241981, $email = null, $mergeTags = null, $json = 1)
	{

		/*
		// Array with first/lastname (MailChimp merge tags)
		$mergeTags = [
	        'FNAME' =>  'Foo',
	        'LNAME' => 'Bar',
	    ];
*/

		if ($listId == null) {

			$listId = $_POST->listId;
			$email = $_POST->email;
			$mergeTags = $_POST->mergeTags;
		}

		$ret = $this->comm->subscribeMailchimpClient($listId, $email, $mergeTags);

		return $this->sendData($ret, $json);
	}

	public function triggerActions($request = null, $json = 1)
	{

		$ret = [];
		$triggerType = false;
		$contextObj = false;


		return $this->sendData(
			$ret,
			$json
		);
	}

	public function updateBlueprint($json = 1)
	{

		$object = $_POST->objectData;

		if ($updatedObj = $this->obj->update('blueprints', intval($object->id), 'blueprint', json_encode($object->blueprint))) {

			return $this->sendData($updatedObj, $json);
		}
	}

	public function updateInstanceObject($json = 1)
	{

		$ret = $this->pgObjects->updateInstance('instances', json_decode(json_encode($_POST), true));

		return $this->sendData($ret, $json);
	}

	public function updateInventoryItem($itemObj = null, $json = 1)
	{

		if ($itemObj == null) {

			$itemId = $_POST->id;
			$itemDetails = $_POST->details;
		}

		if ($this->pgObjects->update('inventory', array('id' => intval($itemId), 'details' => $itemDetails))) {
			// 		if($this->obj->update('inventory', $itemId, 'details', json_encode($itemDetails))){

			return true;
		}
	}

	public function updateInventoryMenu($updateData = null, $json = 1)
	{

		//!TODO: set 'scheduling_in_progress' or something like that to true in inventory settings obj
		//!TODO: check if 'scheduling_in_progress' before trying to do anything (timestamp of this as well, so a bug won't make these impossible to run forever)

		$settings = [
			$single_unit => [
				'id' => 0,
				'name' => 'single',
				'measurements' => [
					[
						'id' => 1,
						'name' => 'dozen',
						'divisor' => 1,
						'multiplier' => 12,
						'base_reference' => 0
					]
				]
			]
		];

		//!TODO: create delete function for stock items which will check reservations and try to reset them,
		// and if not, flag them on the menu to display to user next time it is loaded
		if ($updateData === null) {
			$updateData = $_POST;
		}

		$itemQty = intval($updateData->qty);
		$response = [];

		if ($updateData->menuEdited) {
			$updatedMenu = $this->objToArr($_POST->updatedMenu);
			$response['menuEdited'] = $this->pgObjects->update('inventory_menu', $updatedMenu, 0);
		}
		/*
echo $updateData->type.' TYPE <br />';
var_dump($updateData);
*/
		switch ($updateData->type) {

			case 'add-item':
				$response['add-item'] = $this->addItemToMenu($updateData, $settings);
				break;

			case 'add-section':
				$response['add-section'] = $this->addSectionToMenu($updateData, $settings);
				break;

			case 'edit-section':
				$response['edit-section'] = $this->editMenuSection($updateData, $settings);
				break;

			case 'adjust-qty':
				$response['adjust-qty'] = $this->adjustMenuItemQuantity($updateData, $settings);
				break;

			case 'remove-item':
				$response['remove-item'] = $this->removeItemFromMenu($updateData, $settings);
				break;

			case 'move-items':
				$response['move-items'] = $this->moveItemsBetweenSections($updateData, $settings);
				break;

			case 'remove-section':
				$response['remove-section'] = $this->removeSectionFromMenu($updateData, $settings);
				break;

			default:
				break;
		}

		//!TODO: set 'scheduling_in_progress' or something like that to false in inventory settings obj

		return $this->sendData($response, $json);
	}

	public function dumpValue($value = null)
	{

		var_dump($value);
	}

	public function updateState(
		$objectId = null,
		$stateProperty = 'state',
		$newState = null,
		$link = null,
		$transitionType = '',
		$triggeredByChild = false,
		$json = 1,
		$onStateChangeBeforeTriggeredActions = false
	) {
		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');
		$supressActions = false;

		// get the data out of $_POST
		if (
			$_POST
			&& $objectId === null
		) {

			$objectId = intval($_POST->objectId);
			$newState = intval($_POST->newState);
			$link = $_POST->link;

			// for recurring events
			$isRecurring = intval($_POST->isRecurring);
			$start = 0;
			$end = 0;

			if (!empty($_POST->between)) {
				$start = intval($_POST->between->start);
				$end = intval($_POST->between->end);
			}

			if ($_POST->supressActions === true) {
				$supressActions = true;
			}
		}
		$childObjs[$stateProperty] = true;

		if (!empty($_POST->stateProperty)) {
			$stateProperty = $_POST->stateProperty;
		}

		$isEntity = false;

		// check our inputs
		if (
			$objectId == null
			or ($newState === null
				&& empty($transitionType)
			)
			or $link == null
		) {
			return $this->sendData(false, $json);
		}

		$resp = [];
		$workflow = [];
		$obj = [];

		// get the object
		$childObjs = [
			// current state
			'state' => true,
			'parent' => true,
			'tagged_with' => true,

			// types
			'group_type' => true,
			'type' => [
				'states' => true
			],
			'project_type' => [
				'states' => true
      ],
      'date_booked' => true
		];

		if ($isRecurring) {

			$childObjs['name'] = true;
			$childObjs['is_recurring'] = true;
			$childObjs['schedule_options'] = true;
			$childObjs['group_type'] = true;
			$childObjs['managers'] = 'id';
			$childObjs['users'] = 'id';
			$childObjs['description'] = true;
			$childObjs['start_date'] = true;
			$childObjs['end_date'] = true;
			$childObjs['tagged_with'] = true;
		}

		$obj = $this->pgObjects->getById(
			'',
			$objectId,
			$childObjs
		);

		// check that the object is valid to update; if not return
		if (empty($obj)) {
			return $this->sendData(false, $json);
		}

		// get the workflow
		if (substr($obj['object_bp_type'], 0, 1) === '#') {

			$bp = $this->pgObjects->getBlueprint($obj['object_bp_type']);
			$workflowObj = $this->pgObjects->getById(
				'entity_workflow',
				intval($bp[$stateProperty]['workflow']),
				[
					'states' => true
				]
			);
			$workflow = $workflowObj['states'];
			$isEntity = true;
		} else if ($obj['type']) {

			$workflow = $obj['type']['states'];
			$workflowObj = $obj['type'];

			if ($obj['type']['object_bp_type'] === 'project_types') {

				$stateProperty = 'state';
			}
		} else if ($obj['project_type']) {

			$workflow = $obj['project_type']['states'];
			$workflowObj = $obj['project_type'];
		} else {
			return $this->sendData(false, $json);
		}

		// get new system readable status value
		$nextState = __::find($workflow, function ($state) use ($newState) {
			return intval($state['uid']) === intval($newState);
		});

		$currentState = __::find($workflow, function ($state) use ($obj, $stateProperty) {
			return intval($state['uid']) === intval($obj[$stateProperty]);
		});

		if (!$currentState) {

			$currentState =  __::find($workflow, function ($state) {
				return intval($state['isEntryPoint']) == true;
			});
		}

		// if finding next state through $transitionType
		if (!empty($transitionType)) {

			switch ($transitionType) {

				case 'next':
					$nextState = __::find($workflow, function ($state) use ($currentState) {
						return intval($state['uid']) === intval($currentState['next'][0]);
					});
					break;

				case 'previous':
					$nextState = __::find($workflow, function ($state) use ($currentState) {
						return intval($state['uid']) === intval($currentState['previous'][0]);
					});
					break;
			}

			$newState = $nextState['uid'];
		}

		// If project state transition triggered by a child task, check that this
		// state should be transitioned. If not, just return false.
		if (
			$triggeredByChild
			&& !$currentState['shouldTransitionOnTaskComplete']
		) {
			return false;
		}

		// If transitions are limited to next and previous values,
		// check that next state is allowed. If not, stop process
		// and return with a message to client.
		if (!$currentState['allowAllTransitions']) {

			if (
				!__::includ($currentState['previous'], strval($nextState['id']))
				&& !__::includ($currentState['next'], strval($nextState['id']))
			) {
				//!TODO: Fix issue here
				/*
				return $this->sendData(
					$response = [
						'msg' => 			[
							'messages' => ['Transition to this state from current state is not allowed.']
						]
						, 'response' => 	false
						, '_notify_usr' => 	true
					]
					, $json
				);
				*/
			}
		}


		if (empty($nextState)) {
			return $this->sendData(false, $json);
		}

		$newStatus = 'not_started';

		if (array_key_exists('type', $nextState) and is_string($nextState['type'])) {
			$newStatus = $nextState['type'];
		}

		// Check for conditions to transition to this state
		$conditions = $this->pgObjects->where(
			'condition',
			[
				'object' => 	$workflowObj['id'], 'state' => 	$newState, 'is_after_state_change' => [
					'type' => 		'not_equal', 'value' => 	true
				]
			],
			'',
			[
				'conditions' => 				true, 'next' => 					true, 'else_do' => 					true, 'is_after_state_change' => 	true, 'name' => 					true, 'object' => 					true, 'state' => 					true
			]
		);

		$checks = [];

		if (is_array($conditions)) {
			foreach ($conditions as $condition) {

				if (is_array($condition['conditions'])) {
					foreach ($condition['conditions'] as $c) {

						array_push($checks, $c);
					}
				}
			}
		}

		$condMsg = $this->pgObjects->runSteps(
			$obj['id'],
			[
				'_if' => $checks
			],
			true
		);

		// If was unable to run, stop and respond to client.
		if ($condMsg['run'] === false) {

			return $this->sendData(
				[
					'msg' => 			$condMsg, 'type' => 		'warning', 'response' => 	$obj, '_notify_usr' => 	true
				],
				$json
			);
		}

		$checks = null;

		// if group is a recurring group, get group for that time
		if ($isRecurring) {

			$followerToUpdate = $this->pgObjects->where(
				$obj['object_bp_type'],
				[
					'initial' => $obj['id'],
					'end_date' => [
						'type' => 'between',
						'start' => $start,
						'end' => $end
					]
				]
			)[0];

			// if group at that time does not exist, create it
			if (empty($followerToUpdate) or is_null($followerToUpdate)) {

				$toCreate = $obj;
				unset($toCreate['id']);
				unset($toCreate['cycle']);
				unset($toCreate['schedule_options']);
				unset($toCreate['repeat_forever']);
				unset($toCreate['repeat_end_date']);
				unset($toCreate['date_created']);
				unset($toCreate['full_count']);
				unset($toCreate['instance']);

				$toCreate['type'] = $obj['type']['id'];

				$toCreate['initial'] = $obj['id'];
				$toCreate['is_recurring'] = 0;
				$toCreate['state'] = $nextState['uid'];
				$toCreate['status'] = $newStatus;

				$startTime = new DateTime($obj['start_date']);
				$startTime->setDate(
					intval(date('Y', $start)),
					intval(date('m', $start)),
					intval(date('d', $start))
				);

				$endTime = new DateTime($obj['end_date']);
				$endTime->setDate(
					intval(date('Y', $start)),
					intval(date('m', $start)),
					intval(date('d', $start))
				);

				$toCreate['start_date'] = $startTime->format('Y-m-d H:i:s');
				$toCreate['end_date'] = $endTime->format('Y-m-d H:i:s');

				$resp = $this->pgObjects->create(
					$obj['object_bp_type'],
					$toCreate,
					0,
					1
				);
			}
		} else {

			// run the update, placing new object in $resp
			if ($isEntity) {

				$resp = $this->pgObjects->update(
					$obj['object_bp_type'],
					[
						'id' => 							$obj['id'], $stateProperty => 				$nextState['uid']
					],
					1
				);
			} else {

				$now = new DateTime('now', new DateTimezone('Africa/Abidjan'));

				if ($obj['object_bp_type']) {
					$stateProperty = 'state';
				}

				$updatedObject = [
					'id' => $obj['id'],
					$stateProperty => $nextState['uid'],
					'status' => $newStatus,
					'state_updated_on' => $now->format('Y-m-d H:i:s')
				];

				// Check if we need to set date_booked for specific instances and conditions
				$shouldSetDateBooked = false;
				if (
					$nextState['name'] === 'Booked' &&
					$obj['group_type'] === 'Project' &&
					$obj['object_bp_type'] === 'groups'
				) {
					if (
						($obj['instance'] === 'infinity' && $workflowObj['id'] == 1625566) ||
						($obj['instance'] === 'dreamcatering' && $workflowObj['id'] == 8248140) ||
						($obj['instance'] === 'rickyvoltz' && $workflowObj['id'] == 1625566)
					) {
						$shouldSetDateBooked = true;
					}
				}

				if ($shouldSetDateBooked) {

          $localTime = clone $now;
					$localTime->setTimezone(new DateTimezone('America/Chicago'));

          $projectTags = isset($obj['tagged_with']) && is_array($obj['tagged_with']) ? $obj['tagged_with'] : [];
						if (!in_array($obj['id'], $projectTags)) {
							$projectTags[] = $obj['id'];
						}

					// Only set date_booked if it's not already set (first time booking
					if (empty($obj['date_booked']) || $obj['date_booked'] === null) {

						$updatedObject['date_booked'] = $now->format('Y-m-d H:i:s');

						$this->postComment([
							'type_id' => $obj['id'],
							'note' => 'Event Booked on: ' . $localTime->format('m/d/Y g:i A'),
							'record_type' => 'log',
							'log_type' => 'booking',
							'tagged_with' => $projectTags,
							'icon' => [
								'icon' => 'calendar check',
								'color' => 'green'
							]
						], 0, 0);

					} else {

						$localTime = clone $now;
						$localTime->setTimezone(new DateTimezone('America/Chicago'));

						$originalBookingDate = new DateTime($obj['date_booked'], new DateTimezone('UTC'));
						$originalBookingDate->setTimezone(new DateTimezone('America/Chicago'));

						$this->postComment([
							'type_id' => $obj['id'],
							'note' => 'Event returned to Booked state on ' . $localTime->format('m/d/Y g:i A') .
									 ' (original booking date: ' . $originalBookingDate->format('m/d/Y g:i A') . ')',
							'record_type' => 'log',
							'log_type' => 'booking',
							'tagged_with' => $projectTags,
							'icon' => [
								'icon' => 'calendar check',
								'color' => 'blue'
							]
						], 0, 0);
					}

				}

				$resp = $this->pgObjects->update(
					$obj['object_bp_type'],
					$updatedObject,
					1
				);

				$now = null;
			}
		}

		$decisions = $this->pgObjects->where(
			'event_type',
			[
				'object' => $workflowObj['id'], 'state' => $nextState['uid'], 'requires_input' => true
			]
		);

		// Record prompts for manual events
		$notifications = [];
		if (!empty($decisions)) {
			foreach ($decisions as $i => $decision) {

				array_push(
					$notifications,
					$this->pgObjects->create(
						'notification',
						[
							'color' => 			'yellow', 'details' => 		$decision['description'], 'event_type' => 	$decision['id'], 'icon' => 		'tasks', 'is_decision' => 	true, 'is_viewed' => 	0, 'link' => 		'', 'object_uid' => 	$objectId, 'producer' => 	$objectId, 'title' =>  		$decision['name'], 'type' => 		'task', 'user' => 		$_COOKIE['uid']
						]
					)
				);

				$notifications[$i]['event_type'] = $decision;
			}
		}

		$updatedBy = $this->pgObjects->getByIdAcrossInstances(
			'users',
			intval($_COOKIE['uid']),
			[
				'fname' => true, 'lname' => true
			]
		);

		// Log state transition note/comment
		$this->postComment([
			'type_id' => $resp['id']
            , 'note' => 'Changed from <strong>' . $currentState['name'] . '</strong> to <strong>' . $nextState['name'] . '</strong> by ' . $updatedBy['fname'] . ' ' . $updatedBy['lname'] . '.' . $actionsHTML
            , 'record_type' => 'log'
            , 'log_type' => 'state-change'
            , 'icon' => [
				'icon' => 'share', 'color' => 'blue'
			]
		], 0, 0);


		if ($resp["group_type"] == "Project") {

			$queriedUserIds = [];

			if (is_int($resp["managers"][0])) {

				$userIdsToNotify = $resp['managers'];
				$queriedUserIds = $this->pgObjects->where('users', ['id' => ['type' => 'or', 'values' => $userIdsToNotify]]);
			} else {

				$queriedUserIds = $resp["managers"];
			}

			$this->pgObjects->setInstance($this->appConfig['instance']);

			$stateChangeEmailTemplate = '<div>Hello {managerName}, </div><br> <div>You are being notified because a project you manage - <strong> {projectName} </strong> - has been moved from <strong> {currentState} </strong> to <strong> {nextState} </strong> by <strong> {updater} </strong> </div> <div style=\"margin: 10px 0;\"></div></div>';
			$emailFrom = $this->appConfig['emailFrom'];
			$instance = $this->appConfig['instance'];
			$projectName = $resp["name"];

			foreach ($queriedUserIds as $manager) {
				$vars = array(
					'{managerName}' => $manager['name'],
					'{projectName}' => $projectName,
					'{currentState}' => $currentState['name'],
					'{nextState}' => $nextState['name'],
					'{updater}' => $updatedBy['fname'] . ' ' . $updatedBy['lname']
				);


				$emailBody = strtr($stateChangeEmailTemplate, $vars);

				//NotifyToRole - is working on foundation (so this will be disabled for foundation group)
				if ($this->appConfig['instance'] != 'foundation_group') {
					$this->sendEmail(
						$manager['email'],
						$emailFrom,
						"The state of " . $projectName . " has been updated",
						['BODY' => $emailBody, 'INSTANCE_NAME' => $instance],
						"COMMENT_POSTED",
						false
					);
				}
			}
		}

		$response = [
			'response' => 	$resp, 'msg' => ["notifications" => $notifications], '_notify_usr' => true
		];

		if (
			$onStateChangeBeforeTriggeredActions
			&& is_callable($onStateChangeBeforeTriggeredActions)
		) {
			$response = $onStateChangeBeforeTriggeredActions($response);
		}

		$this->respondAndContinue($response);

		$actions = false;
		if (!$supressActions) {

			$this->pgObjects->setInstance($workflowObj['instance']);
			$actions = $this->pgObjects->triggerStateActions(
				$objectId,
				$workflowObj,
				$newState,
				$currentState['uid'],
				$stateProperty
			);
		}

		if ($actions && $actions['updated'] && $actions['updated']['id'] === $resp['id']) {
			$resp = $actions['updated'];
		}

		if ($actions['notifications']) {
			$response['msg']['notifications'] = $actions['notifications'];
		}

		// Record a log.
		$actionsHTML = '';

		if ($actions['msg']['messages']) {
			$actionsHTML = '<ul><li>' . implode('</li><li>', $actions['msg']['messages']) . '</li></ul>';
		}
		if ($GLOBALS['speed-test'] === true) {

			echo 'BEFORE COMMENTS/NOTIFICATIONS::' . (microtime(true) - $GLOBALS['req-start-time']) . 's<br />';
		}

		// Create notifications
		$_POST->title = 	'Status update to ' . $resp['name'] . '.';
		$_POST->details = 	'Changed from <strong>' . $currentState['name'] . '</strong> to <strong>' . $nextState['name'] . '</strong> by ' . $updatedBy['fname'] . ' ' . $updatedBy['lname'] . '.';
		$_POST->producer = 	$resp['id'];
		$_POST->color = 	'blue';
		$_POST->icon = 		'arrows alternate horizontal';
		$_POST->type = 		'general';
		$_POST->link = 		$link;
		$_POST->notify = 	$resp['notify'];

		if (!empty($nextState['tags'])) {
			$_POST->notify = array_merge($_POST->notify, $nextState['tags']);
			$_POST->notify = array_unique($_POST->notify);
		}

		$this->notify(0);

		// Pass the new state back in the response
		$response['newState'] = $nextState;

		// Continue on processing...

		// If this is transition to a done state on a task, check if parent's
		// state (if it is a project or contact) should be updated, and update, if so.
		if (
			$newStatus === 'done'
			&& $obj['object_bp_type'] === 'groups'
			&& $obj['group_type'] === 'Task'
			&& !empty($obj['parent'])
			&& (
				($obj['parent']['object_bp_type'] === 'groups'
					&& $obj['parent']['group_type'] === 'Project'
				)
				|| 	$obj['parent']['object_bp_type'] === 'contacts'
			)
		) {

			// Get count of incomplete tasks in the project.
			$taskCount = $this->pgObjects->countObjs(
				'groups',
				[
					'parent' => 		$obj['parent']['id'], 'status' => 	[
						'type' => 		'not_equal', 'value' => 	'done'
					]
				],
				'group_type',
				''
			);

			if (
				empty($taskCount)
				|| $taskCount[0]['count'] === 0
			) {

				$parentUpdates = $this->updateState(
					$obj['parent']['id'],
					null,
					null,
					$link,
					'next',
					true,
					0
				);

				if ($parentUpdates) {

					// Updates for the front-end.
					if (!is_array($response['msg']['messages'])) {
						$response['msg']['messages'] = [];
					}

					if (!is_array($response['msg']['updates'])) {
						$response['msg']['updates'] = [];
					}

					switch ($obj['parent']['object_bp_type']) {

						case 'groups':
							array_push(
								$response['msg']['messages'],
								'Project moved to next state.'
							);
							break;

						case 'contacts':
							array_push(
								$response['msg']['messages'],
								'Contact moved to next state.'
							);
							break;
					}

					array_push(
						$response['msg']['updates'],
						[
							'id' => 			$parentUpdates['response']['id'], 'state' => 	$parentUpdates['response']['state']
						]
					);

					if (is_array($parentUpdates['msg'])) {

						if (is_array($parentUpdates['msg']['messages'])) {

							$response['msg']['messages'] = array_merge(
								$response['msg']['messages'],
								$parentUpdates['msg']['messages']
							);
						}

						if (is_array($parentUpdates['msg']['updates'])) {

							$response['msg']['updates'] = array_merge(
								$response['msg']['updates'],
								$parentUpdates['msg']['updates']
							);
						}
					}
				}
			}
		} elseif (
			$newStatus === 'done'
			&& $isEntity
		) {

			// Only look at entities whose type is flagged as task-like.
			$this->pgObjects->setInstance($obj['instance']);

			$parentProjs = $this->pgObjects->where(
				'groups',
				[
					'group_type' => 	'Project', 'id' => 			[
						'type' => 'or', 'values' => $obj['tagged_with']
					]
				],
				'',
				[
					'name' => 		true
				]
			);

			if (is_array($parentProjs)) {

				foreach ($parentProjs as $proj) {

					// Get total count across task-like types
					$count = $this->pgObjects->countObjs(
						'#Action Items',
						[
							'tagged_with' => $proj['id'], 'status' => 	[
								'type' => 		'not_equal', 'value' => 	'done'
							]
						],
						false,
						''
					);

					if ($count === 0) {

						$parentUpdates = $this->updateState(
							$proj['id'],
							null,
							null,
							$link,
							'next',
							true,
							0
						);

						if ($parentUpdates) {

							// Updates for the front-end.
							if (!is_array($response['msg']['messages'])) {
								$response['msg']['messages'] = [];
							}

							if (!is_array($response['msg']['updates'])) {
								$response['msg']['updates'] = [];
							}

							switch ($proj['object_bp_type']) {

								case 'groups':
									array_push(
										$response['msg']['messages'],
										'Project moved to next state.'
									);
									break;

								case 'contacts':
									array_push(
										$response['msg']['messages'],
										'Contact moved to next state.'
									);
									break;
							}

							array_push(
								$response['msg']['updates'],
								[
									'id' => 		$parentUpdates['response']['id'], 'state' => 	$parentUpdates['response']['state']
								]
							);

							if (is_array($parentUpdates['msg'])) {

								if (is_array($parentUpdates['msg']['messages'])) {

									$response['msg']['messages'] = array_merge(
										$response['msg']['messages'],
										$parentUpdates['msg']['messages']
									);
								}

								if (is_array($parentUpdates['msg']['updates'])) {

									$response['msg']['updates'] = array_merge(
										$response['msg']['updates'],
										$parentUpdates['msg']['updates']
									);
								}
							}
						}
					}
				}
			}
		}

		if (
			$this->pgObjects->rules->currentLayer > 0
			|| intval(ini_get('output_buffering')) === 0
		) {
			return $response;
		}

		return $this->sendData($response, $json);
	}

	public function updateStripeConnectAccount($accountObj = null, $json = 1)
	{

		/*
		error_reporting(E_ALL);
		ini_set('display_errors', '1');
*/

		if ($accountObj == null && $_POST->accountInfo) {
			$accountObj = $_POST->accountInfo;
		} else {
			return false;
		}

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		$account = \Stripe\Account::retrieve($accountObj->id);

		if ($accountObj->business_name) {

			$account->business_name = $accountObj->business_name;

			if ($accountObj->legal_entity->business_tax_id) {
				$account->legal_entity->business_tax_id = $accountObj->legal_entity->business_tax_id;
			}
		} else {

			$account->legal_entity->first_name = $accountObj->legal_entity->first_name;
			$account->legal_entity->last_name = $accountObj->legal_entity->last_name;
			$account->legal_entity->personal_id_number = $accountObj->legal_entity->personal_id_number;
		}

		$account->support_email = $accountObj->support_email;
		$account->support_phone = $accountObj->support_phone;

		$account->legal_entity->address->line1 = $accountObj->legal_entity->address->line1;
		$account->legal_entity->address->city = $accountObj->legal_entity->address->city;
		$account->legal_entity->address->state = $accountObj->legal_entity->address->state;
		$account->legal_entity->address->postal_code = $accountObj->legal_entity->address->postal_code;
		$account->legal_entity->address->country = $accountObj->legal_entity->address->country;
		$account->legal_entity->type = $accountObj->legal_entity->type;
		$account->statement_descriptor = $accountObj->statement_descriptor;

		$account->save();

		return $this->sendData($account, $json);
	}

	public function updateStripeConnectPayoutAccount($accountId = null, $payoutAccountId = null, $defaultStatus = true, $json = 1)
	{

		/*
		error_reporting(E_ALL);
		ini_set('display_errors', '1');
*/

		if ($payoutAccountId == null && $_POST->payoutAccountId) {
			$payoutAccountId = $_POST->payoutAccountId;
		} else {
			return false;
		}

		if ($accountId == null && $_POST->accountId) {
			$accountId = $_POST->accountId;
		} else {
			return false;
		}

		if ($defaultStatus == null && $_POST->defaultStatus) {
			$defaultStatus = $_POST->defaultStatus;
		}

		\Stripe\Stripe::setApiKey($this->stripeSecretKey);

		$account = \Stripe\Account::retrieve($accountId);
		$bank_account = $account->external_accounts->retrieve($payoutAccountId);
		$bank_account->default_for_currency = $defaultStatus;
		$bank_account->save();

		return $this->sendData($account, $json);
	}

	public function updateObject($objects = null, $objectType = null, $json = 1, $getChildObjs = 0)
	{

		if ($objects == null) {
			$object = $this->objToArr($_POST->objectData);
			$objectType = $_POST->objectType;
		} else {
			$object = $objects;
			$objectType = $objectType;
		}

		if (isset($_POST->getChildObjs)) {
			if (is_numeric($_POST->getChildObjs)) {
				$getChildObjs = intval($_POST->getChildObjs);
			} elseif (is_object($_POST->getChildObjs)) {
				$getChildObjs = $this->objToArr($_POST->getChildObjs);
			}
		}


		if ($updatedObj = $this->pgObjects->update($objectType, $object, $getChildObjs)) {

			return $this->sendData($updatedObj, $json);
		} else {

			return $this->sendData(false, $json);
		}
	}

	public function upsTrack($trackingNumber)
	{
		// TODO REPUBLIX
		$data = "<?xml version=\"1.0\"?>
		        <AccessRequest xml:lang='en-US'>
		                <AccessLicenseNumber>9D3E2CB01FE8E94C</AccessLicenseNumber>
		                <UserId><EMAIL></UserId>
		                <Password>432650Rv</Password>
		        </AccessRequest>
		        <?xml version=\"1.0\"?>
		        <TrackRequest>
		                <Request>
		                        <TransactionReference>
		                                <CustomerContext>
		                                        <InternalKey>blah</InternalKey>
		                                </CustomerContext>
		                                <XpciVersion>1.0</XpciVersion>
		                        </TransactionReference>
		                        <RequestAction>Track</RequestAction>
		                </Request>
		        <TrackingNumber>$trackingNumber</TrackingNumber>
		        </TrackRequest>";

		$ch = curl_init("https://www.ups.com/ups.app/xml/Track");

		curl_setopt($ch, CURLOPT_HEADER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_TIMEOUT, 60);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

		$result = curl_exec($ch);

		// echo '<!-- '. $result. ' -->';

		$data = strstr($result, '<?');

		$xml_parser = xml_parser_create();
		xml_parse_into_struct($xml_parser, $data, $vals, $index);
		xml_parser_free($xml_parser);

		$params = array();
		$level = array();

		foreach ($vals as $xml_elem) {

			if ($xml_elem['type'] == 'open') {

				if (array_key_exists('attributes', $xml_elem)) {

					list($level[$xml_elem['level']], $extra) = array_values($xml_elem['attributes']);
				} else {

					$level[$xml_elem['level']] = $xml_elem['tag'];
				}
			}

			if ($xml_elem['type'] == 'complete') {

				$start_level = 1;
				$php_stmt = '$params';

				while ($start_level < $xml_elem['level']) {

					$php_stmt .= '[$level[' . $start_level . ']]';

					$start_level++;
				}

				$php_stmt .= '[$xml_elem[\'tag\']] = $xml_elem[\'value\'];';

				eval($php_stmt);
			}
		}

		curl_close($ch);

		return $params;
	}

	public function unsubscribeEmailToMailchimp($listId = 1241981, $email = null, $json = 1)
	{

		if ($listId == null) {

			$listId = $_POST->listId;
			$email = $_POST->email;
		}

		$ret = $this->comm->unsubscribeMailchimpClient($listId, $email);

		return $this->sendData($ret, $json);
	}

	public function validate_password($password, $correct_hash)
	{

		$params = explode(":", $correct_hash);

		if (count($params) < HASH_SECTIONS)

			return false;

		$pbkdf2 = base64_decode($params[HASH_PBKDF2_INDEX]);

		return $this->slow_equals(

			$pbkdf2,

			$this->pbkdf2(

				$params[HASH_ALGORITHM_INDEX],

				$password,

				$params[HASH_SALT_INDEX],

				(int)$params[HASH_ITERATION_INDEX],

				strlen($pbkdf2),

				true

			)

		);
	}

	// HELPERS
	public function formatPhone($number)
	{
		return preg_replace('~.*(\d{3})[^\d]{0,7}(\d{3})[^\d]{0,7}(\d{4}).*~', '($1) $2-$3', $number) . "\n";
	}

	/*
public function updateObject($json = 1, $accessLevel = 0){

		$object = $_POST->objectData;
		$objectType = $_POST->objectType;
		$objectBlueprint = $this->obj->getBlueprint($objectType, $accessLevel);

		$objectData = array();
		foreach($objectBlueprint['blueprint'] as $propertyKey => $property){

			if(1 == 1){
			//if($property['immutable'] !== true){

				switch($property['type']){

					case 'string':
					$objectData[$propertyKey] = $object->$propertyKey;
					break;

					case 'int':
// 					if(is_int($object->$propertyKey)){
						//$object->$propertyKey = preg_replace("/[^0-9]/","",$object->$propertyKey);

						$insert = str_replace('$',"",$object->$propertyKey);

						$objectData[$propertyKey] = (float)$insert;
// 					}
					break;

					case 'select':
					$objectData[$propertyKey] = $object->$propertyKey;
					break;

					case 'multi-select':
					$objectData[$propertyKey] = $object->$propertyKey;
					break;

					default:
					break;
				}

			}

		}

		$this->obj->update($objectType, $object->id, 'object_data', json_encode($objectData));

		if($objectType == 'invoice'){
			$this->obj->update($objectType, $object->id, 'eventId', $object->eventId);
		}

		$updatedObject = $this->obj->getObject($objectType, $object->id);

		return $this->sendData($updatedObject, $json);

	}
*/

	// MAILCHIMP V3.0 METHODS
	public function addContactsToMailchimpList($listId = null, $json = 1)
	{

		if ($listId == null) {
			$listId = $_POST->listId;
		}

		// add list Id to all contacts in the instance


	}

	public function addMailchimpCustomer($storeId = null, $contactObj = null, $json = 1)
	{

		if ($storeId == null) {
			$storeId = $_POST->storeId;
			$contactObj = $_POST->contact;
		}

		$ret = $this->comm->createMailchimpCustomer($storeId, $contactObj);

		return $this->sendData($ret, $json);
	}

	public function addMailchimpCustomerOrder($storeId = null, $orderObj = null, $json = 1)
	{

		if ($storeId == null) {
			$storeId = $_POST->storeId;
			$orderObj = $_POST->order;
		}

		$ret = $this->comm->createMailchimpCustomerOrder($storeId, $orderObj);

		return $this->sendData($ret, $json);
	}

	public function addMailchimpListMember($listId = null, $memberObj = null, $json = 1)
	{

		if ($listId == null) {
			$listId = $_POST->listId;
			$memberObj = $_POST->member;
		}

		$ret = $this->comm->createMailchimpListMember($listId, $memberObj);

		return $this->sendData($ret, $json);
	}

	public function checkMailchimpAPIKeyValidity($key = null, $json = 1)
	{

		if ($key == null) {
			$key = $_POST->key;
		}

		$ret = $this->comm->checkMailchimpAPIKey($key);

		return $this->sendData($ret, $json);
	}

	public function deleteMailchimpCustomerOrder($storeId = null, $orderId = null)
	{

		if ($storeId == null) {
			$storeId = $_POST->storeId;
			$orderId = $_POST->orderId;
		}

		$ret = $this->comm->removeMailchimpCustomerOrder($storeId, $orderId);

		return $this->sendData($ret, $json);
	}

	public function pushContactsToMailChimp($json = 1)
	{
		/*
error_reporting(E_ALL);
ini_set('display_errors', '1');
*/
		function pushType($typeId, $map, $scope, $settings, $offset = 0)
		{

			$pageLength = 10;

			$queryObj = array(
				'type' => $typeId,
				'paged' => array(
					'page' => $offset,
					'sortCol' => 'date_created',
					'sortDir' => 'asc',
					'pageLength' => $pageLength,
					'sum' => false
				)
			);

			$contacts = $scope->getObjectsWhere('contacts', $queryObj, 0, 2);

			if (count($contacts) > 0) {

				$mcBatch = $scope->comm->mailchimp->new_batch();

				$members = [];

				foreach ($contacts as $contact) {

					unset($memberObj);

					$memberObj['status'] = 'subscribed';
					$memberObj['merge_fields']['FNAME'] = $contact['fname'];
					$memberObj['merge_fields']['LNAME'] = $contact['lname'];

					foreach ($contact['contact_info'] as $info) {

						if ($info['is_primary'] == 'yes') {

							$mergeMap = $settings[$contact['type']['id']]['map'];

							$mcFieldMatch = __::find($mergeMap, function ($mapFields) use ($info) {
								return $mapFields['pagodaContactInfoId'] == $info['type']['id'];
							});

							if ($mcFieldMatch) {

								if ($info['type']['data_type'] != 'email' and $mcFieldMatch['mcFieldId'] != 'EMAILADDRESS') {

									switch ($mcFieldMatch['data_type']) {

										case 'address':

											$memberObj['merge_fields'][$mcFieldMatch['mcFieldId']] = array(
												'addr1' => $info['street'],
												'addr2' => '',
												'city' => $info['city'],
												'state' => $info['state'],
												'zip' => $info['zip'],
												'country' => $info['country']
											);

											break;

										default:

											$memberObj['merge_fields'][$mcFieldMatch['mcFieldId']] = $info['info'];
									}
								}
							}

							switch ($info['type']['data_type']) {

								case 'email':

									$memberObj['email_address'] = $info['info'];

									break;
							}
						}
					}

					array_push($members, $memberObj);
				}

				$offset += $pageLength;

				$count = 0;

				foreach ($members as $member) {

					$mcBatch->post('op' . $count, "lists/" . $map['mailchimpListId'] . "/members", $member);

					$count++;
				}

				$push = $mcBatch->execute();

				pushType($typeId, $map, $scope, $settings, $offset);
			} else {

				return true;
			}
		}

		$instanceSettings = $this->getObjectsWhere('view_settings', array('user' => 1), false, 0);

		$settings = $instanceSettings[0]['settings']['integrations']['mailchimp']['pushToMaps'];

		foreach ($settings as $contactTypeId => $map) {

			$ret = pushType($contactTypeId, $map, $this, $settings);
		}

		return true;
	}

	public function getConnectedInstances($json = 1)
	{

		// Just hardcoding in the instance options for now
		$response = [];
		if ($this->appConfig['instance'] === 'voltzsoftware' || $this->appConfig['instance'] === 'rickyvoltz') {
			$response = [
				[
					'value' => 	'erationalmarketing'	// Value to store
					, 'name' => 	'Republix' 				// Display name
				],
				[
					'value' => 	'voltzsoftware'			// Value to store
					, 'name' => 	'Bento' 				// Display name
				]
			];
		} elseif ($this->appConfig['instance'] === 'erationalmarketing') {
			$response = [
				[
					'value' => 	'voltzsoftware'			// Value to store
					, 'name' => 	'Bento Systems' 		// Display name
				]
			];
		}

		return $this->sendData($response, $json);
	}

	public function getMailchimpList($listId = null, $json = 1)
	{

		if ($listId == null) {
			$listId = $_POST->listId;
		}

		$ret = $this->comm->mailchimp->get('lists/' . $listId);

		return $this->sendData($ret, $json);
	}

	public function getMailchimpLists($json = 1)
	{

		$ret = $this->comm->mailchimp->get('lists');

		return $this->sendData($ret, $json);
	}

	public function getMailchimpListMergeFields($listId = null, $json = 1)
	{

		if ($listId == null) {
			$listId = $_POST->listId;
		}

		$ret = $this->comm->mailchimp->get('lists/' . $listId . '/merge-fields');

		return $this->sendData($ret, $json);
	}

	public function mailchimpSendAPIEmail($email = null, $json = 1)
	{

		if ($email == null) {
			$email = $_POST->email;
			$workflowId = $_POST->workflowId;
			$emailId = $_POST->emailId;
		}

		$ret = $this->comm->mailchimp->post("/automations/$workflowId/emails/$emailId/queue", array('email_address' => $email));


		return $this->sendData($ret, $json);
	}

	public function startMailchimpWorkflow($emailAddress = null, $workflow = null, $emailId = null, $json = 1)
	{

		if ($emailAddress == null) {
			$emailAddress = $_REQUEST['emailAddress'];
			$workflow = $_REQUEST['workflow'];
			$emailId = $_REQUEST['emailId'];
		}

		$res = $this->comm->mailchimpAPI('post', 'automations/' . $workflow . '/emails/' . $emailId . '/queue/', [
			'email_address' => $emailAddress
		]);

		return $this->sendData($res, $json);
	}


	public function updateMailchimpCustomerOrder($storeId = null, $order = null, $json = 1)
	{

		if ($storeId == null) {
			$storeId = $_POST->storeId;
			$order = $_POST->orderObj;
			$orderId = $_POST->orderId;
		}

		$ret = $this->comm->patchMailchimpCustomerOrder($storeId, $orderId, $order);

		return $this->sendData($ret, $json);
	}

	public function updateMailchimpListMember($listId = null, $memberObj = null, $json = 1)
	{

		if ($listId == null) {
			$listId = $_POST->listId;
			$memberObj = $_POST->member;
		}

		$ret = $this->comm->patchMailchimpListMember($listId, $memberObj);

		return $this->sendData($ret, $json);
	}

	public function updateSingleContactWithMailchimp($contactId = null, $json = 1)
	{

		if ($contactId == null) {
			$contactId = $_POST->id;
		}

		$instanceSettings = $this->getObjectsWhere('view_settings', array('user' => 1), false, 0);

		$settings = $instanceSettings[0]['settings']['integrations']['mailchimp']['pushToMaps'];

		$map = $settings['map'];

		$contact = $this->getObjectsWhere('contacts', array('id' => $contactId), 0, 2)[0];

		$memberObj['merge_fields']['FNAME'] = $contact['fname'];
		$memberObj['merge_fields']['LNAME'] = $contact['lname'];

		foreach ($contact['contact_info'] as $info) {

			if ($info['is_primary'] == 'yes') {

				$mergeMap = $settings[$contact['type']['id']]['map'];

				$mcFieldMatch = __::find($mergeMap, function ($mapFields) use ($info) {
					return $mapFields['pagodaContactInfoId'] == $info['type']['id'];
				});

				if ($mcFieldMatch) {

					if ($info['type']['data_type'] != 'email' and $mcFieldMatch['mcFieldId'] != 'EMAILADDRESS') {

						switch ($mcFieldMatch['data_type']) {

							case 'address':

								$memberObj['merge_fields'][$mcFieldMatch['mcFieldId']] = array(
									'addr1' => $info['street'],
									'addr2' => $info[''],
									'city' => $info['city'],
									'state' => $info['state'],
									'zip' => $info['zip'],
									'country' => $info['country']
								);

								break;

							default:

								$memberObj['merge_fields'][$mcFieldMatch['mcFieldId']] = $info['info'];
						}
					}
				}

				switch ($info['type']['data_type']) {

					case 'email':

						$memberObj['email_address'] = $info['info'];

						break;
				}
			}
		}

		$contactHash = $this->comm->mailchimp->subscriberHash($memberObj['email_address']);

		$ret = $this->comm->mailchimp->patch("lists/" . $settings[$contact['type']['id']]['mailchimpListId'] . "/members/" . $contactHash, $memberObj);

		return $this->sendData($ret, $json);
	}

	// ADMIN METHODS
	public function copyInstanceSettingsObjects($fromInstance = null, $toInstance = null, $json = 1)
	{

		if ($fromInstance === null) {
			$fromInstance = $_POST->fromInstance;
			$toInstance = $_POST->toInstance;
		}

		if (!$fromInstance or !$toInstance) {
			return false;
		}

		return $this->sendData($this->pgObjects->copyInstanceSettingsObjects($fromInstance, $toInstance), $json);
	}

	// GIT COMMANDS
	public function pullRepo()
	{

		$path = "/var/www/vhosts/repos/$_POST[project]";
		$a = '';
		chdir($path);
		exec("git add .");
		exec("git commit -m'message'");
		echo "<h3 align = center> Succesfully commited all the files.</h3>";
	}

	// PRIVATE METHODS

	private function objToArr($object)
	{

		return json_decode(json_encode($object), true);
	}

	public function copyr($source, $dest)
	{

		// Check for symlinks
		if (is_link($source)) {
			return symlink(readlink($source), $dest);
		}

		// Simple copy for a file
		if (is_file($source)) {
			return copy($source, $dest);
		}

		// Make destination directory
		if (!is_dir($dest)) {
			mkdir($dest);
		}

		// Loop through the folder
		$dir = dir($source);
		while (false !== $entry = $dir->read()) {
			// Skip pointers
			if ($entry == '.' || $entry == '..') {
				continue;
			}

			// Deep copy directories
			$this->copyr("$source/$entry", "$dest/$entry");
		}

		// Clean up
		$dir->close();
		return true;
	}

	public function respondAndContinue($response)
	{

		// Send back response
		echo json_encode($response);
		// echo gzencode(json_encode($response));

		$size = ob_get_length();
		ob_start();

		$serverProtocole = filter_input(INPUT_SERVER, 'SERVER_PROTOCOL', FILTER_SANITIZE_STRING);

		header($serverProtocole . ' 200 OK');

		header("Content-Type: application/json");
		header('Content-Encoding: none');
		// header('Content-Encoding: gzip');

		header("Content-Length: {$size}");
		header("Connection: close");

		// Close connection
		ob_end_flush();
		ob_flush();
		flush();

		if (session_id()) {
			session_write_close();
		}
	}

	// INVENTORY METHODS

	// private

	private function can_nest_item($item = [], $nestItems = [], $messageTrace = [])
	{

		$ret = [
			'can_be_nested' => true,
			'message' => $messageTrace
		];

		// check if any nest item is the same as the proposed parent item
		if (in_array($item['id'], $nestItems)) {

			return [
				'can_be_nested' => false,
				'message' => $messageTrace
			];
		} elseif (count($nestItems) === 0) {

			return [
				'can_be_nested' => true,
				'message' => $messageTrace
			];
		}

		// get items w/possible descendent overlaps
		$nestItemObjects = $this->pgObjects->getById(null, $nestItems, [
			'id' => true,
			'items' => true,
			'name' => true
		]);

		if (is_array($nestItemObjects)) {

			foreach ($nestItemObjects as $nestItemObj) {

				if ($nestItemObj['object_bp_type'] === $item['object_bp_type']) {

					$nestedIds = [];

					foreach ($nestItemObj['items'] as $ingredient) {

						$ingredientIds = [];
						if (intval($ingredient['inventory_group']) > 0) {

							array_push($nestedIds, $ingredient['inventory_group']);
						} elseif (is_array($ingredient['choices'])) {

							foreach ($ingredient['choices'] as $choiceIngredient) {

								if (intval($choiceIngredient['inventory_group']) > 0) {

									array_push($nestedIds, intval($choiceIngredient['inventory_group']));
								}
							}
						}
					}

					if (count($nestedIds) > 0) {

						$newMsg = $ret['message'];
						array_push($newMsg, $nestItemObj['name']);
						$ret = $this->can_nest_item($item, $nestedIds, $newMsg);
						if (!$ret['can_be_nested']) {
							return $ret;
						}
					}
				}
			}
		} else {

			return [
				'can_be_nested' => false,
				'message' => $messageTrace
			];
		}

		return $ret;
	}

	private function map_item_recipe($item = [], $callback, $recurse = false, &$memo, $address = '0', $itemQty, $menu = [])
	{

		// For each component of the recipe..

		if ($memo['HTML'] == null) {
			$memo['HTML'] = '<b>' . $item['name'] . '</b>';
		} else {
			if (empty($item['description'])) {
				$memo['HTML'] .= '<li><b>' . $item['name'] . '</b></li>';
			} else {
				$memo['HTML'] .= '<li><b>' . $item['name'] . '</b><br/><em>' . $item['description'] . '</em></li>';
			}
		}

		if ($this->appConfig['instance'] == 'dreamcatering') {
			$memo['HTML'] .= '<ul style="margin-top: 10px;">';
		} else {
			$memo['HTML'] .= '<ul>';
		}

		if (is_array($item['items'])) {

			foreach ($item['items'] as $i => $ingr) {

				if (
					is_array($ingr['choices'])
					&& !empty($ingr['choices'])
				) {

					foreach ($ingr['choices'] as $i => $choiceItem) {

						$ingrQty = $this->to_base_units(
							'uscu',
							$choiceItem['qty']['unit_type'],
							$choiceItem['qty']['measurement'],
							$choiceItem['qty']['quantity']
						) * $itemQty;

						$recurse = $callback(
							$choiceItem,
							$memo,
							$address,
							$ingr,
							$ingrQty
						);

						if ($recurse) {
							$this->map_item_recipe($choiceItem['inventory_group'], $callback, $recurse, $memo, $address . '.' . $choiceItem['id'], $ingrQty, $menu);
						}
					}
				} elseif (is_array($ingr['inventory_group'])) {

					// For ingredients tied directly to guest counts
					if (
						array_key_exists('type', $ingr['qty'])
						and $ingr['qty']['type'] === 'per_guest'
					) {

						$ingrQty = $this->to_base_units(
							'uscu',
							$ingr['qty']['unit_type'],
							$ingr['qty']['measurement'],
							$ingr['qty']['quantity'] * $menu['guest_count']
						) * $itemQty;
					} else {

						$ingrQty = $this->to_base_units(
							'uscu',
							$ingr['qty']['unit_type'],
							$ingr['qty']['measurement'],
							$ingr['qty']['quantity']
						) * $itemQty;
					}

					$recurse = $callback(
						$ingr,
						$memo,
						$address,
						false,
						$ingrQty
					);

					if ($recurse) {
						$this->map_item_recipe($ingr['inventory_group'], $callback, $recurse, $memo, $address . '-' . $ingr['id'], $ingrQty, $menu);
					}
				}
			}
		}
		$memo['HTML'] .= '</ul>';
		return $memo;
	}

	public function assignMenuItem($menu, $section, $fullRecipe, $itemQty, $itemId, $choiceSelection, $qtyType, $skipReservations = false)
	{

		$adjustedQty = $itemQty;
		$basePrice = $fullRecipe['price'];
		switch ($qtyType) {

			case 'per_guest':
				$adjustedQty = $itemQty * $menu['guest_count'];
				$basePrice = $fullRecipe['price_per_person'];
				break;

			case 'guest_count':
				$adjustedQty = $menu['guest_count'];
				$basePrice = $fullRecipe['price_per_person'];
				break;

			case 'per_hour':
				$to_time = strtotime($section['to']);
				$from_time = strtotime($section['from']);
				$duration =  round(abs($to_time - $from_time) / 60, 2) / 60;
				$adjustedQty = $itemQty * $duration;
				$basePrice = $fullRecipe['price_per_hour'];
				break;

			case 'per_hour_per_guest':
				$to_time = strtotime($section['to']);
				$from_time = strtotime($section['from']);
				$duration =  round(abs($to_time - $from_time) / 60, 2) / 60;
				$adjustedQty = $itemQty * $duration;
				$adjustedQty = $adjustedQty * $menu['guest_count'];
				$basePrice = $fullRecipe['price_per_hour_per_person'];
				break;
		}

		$shouldReserve = false;
		if ($menu['active'] === 'Yes') {
			$shouldReserve = true;
		}

		//!TODO: add per person/per hour/per person per hour
		// 		 pricing to additional costs of item choices
		$memo = [
			'reservations' 			=> [],
			'item_price' 			=> $basePrice,
			'selections_strings'	=> [],
			'HTML' => null,
			'absolute_qty' 			=> $adjustedQty
		];

		$this->map_item_recipe(
			$fullRecipe,
			function (
				$ingr,
				&$memo,
				$address,
				$parentIngredient,
				$itemQty
			) use (
				$menu,
				$section,
				$fullRecipe,
				$itemId,
				$choiceSelection,
				$shouldReserve,
				$skipReservations
			) {

				// If item is a choice item
				if ($parentIngredient) {

					// Check if the current choice item is selected for this line item
					$selection = __::find($choiceSelection, function ($choice) use ($address) {
						return $address == $choice['ingr_key'];
					});
					$selection = __::find($selection['choice'], function ($choice) use ($parentIngredient) {
						return $choice['item'] == $parentIngredient['id'];
					});

					if (in_array($ingr['id'], $selection['choice'])) {

						// For custom choice items
						if (!$ingr['inventory_group']) {

							$ingr['inventory_group'] = [
								'name' => $ingr['name'],
								'description' => $ingr['description']
							];
						}

						if (!empty($ingr['inventory_group']['description'])) {
							array_push($memo['selections_strings'], $ingr['inventory_group']['name'] . ': ' . $ingr['inventory_group']['description']);
						} else {
							array_push($memo['selections_strings'], $ingr['inventory_group']['name']);
						}

						if (intval($ingr['additional_price'])) {

							$memo['item_price'] += intval($ingr['additional_price']);
						}

						// If the item is a stock item, make reservations and update the line item price tally
						if ($ingr['inventory_group']['object_bp_type'] == 'inventory_groups' && !$skipReservations) {

							$assignData = $this->assignStockItem(
								$menu,
								$section,
								$fullRecipe,
								$ingr,
								$itemId,
								$itemQty,
								0,
								null,
								null,
								$shouldReserve
							);

							return false;

							// If item is a recipe or product, check its child ingredients
						} else {

							return true;
						}
					}

					// If item is not a choice item (base item)

				} else {

					// if the item is a stock item, make reservations
					if ($ingr['inventory_group']['object_bp_type'] == 'inventory_groups' && !$skipReservations) {

						$assignData = $this->assignStockItem(
							$menu,
							$section,
							$fullRecipe,
							$ingr,
							$itemId,
							$itemQty,
							0,
							null,
							null,
							$shouldReserve
						);

						array_push($memo['reservations'], $assignData['reservation']);

						// if item is a recipe or product, check its child ingredients
					} else {

						return true;
					}
				}

				return;
			},
			true,
			$memo,
			'0',
			$adjustedQty,
			$menu
		);

		return $memo;
	}

	private function assignStockItem(
		$menu,
		$section,
		$billableGroup,
		$ingredient,
		$itemId,
		$itemQty,
		$choiceId = 0,
		$parentIngredient = null,
		$memo = null,
		$activeReservation = true,
		$removeForever = false
	) {

		$ingredientId = $ingredient['id'];
		if ($choiceId > 0) {
			$ingredientId = $parentIngredient['id'];
		}

		// parse input data
		$menuId = 0;
		$relatedId = 0;
		if (is_array($menu)) {
			$menuId = $menu['id'];
			$relatedId = $menu['related'];
		}

		// 		$base_mult = $this->toBaseUnits($ingredient, $ingredient['inventory_group']['base_unit']['measurements']);
		// 		$base_mult = floatval($this->to_base_units('uscu', $ingredient['qty']['unit_type'], $ingredient['qty']['measurement'], $ingredient['qty']['quantity']));
		// 	var_dump($itemQty, $ingredient['inventory_group']['name'], $base_mult);

		$resType = '';
		switch ($ingredient['inventory_group']['stock_type']) {

			case 'perishable':
				$resType = 'use';
				break;

			case 'not_perishable':
				$resType = 'rental';
				break;

			default:
				$resType = 'untracked';
				break;
		}
		if ($removeForever) {
			$resType = 'use';
		}

		if ($memo == null) {
			$memo = 'For ' . $billableGroup['name'] . ' in ' . $menu['name'];
		}

		$activeState = 1;
		if ($activeReservation === false) {
			$activeState = 0;
		}

		$reservation = array(

			'object_bp_type' => 'item_reservation',

			// related objs
			'menu' => 		$menuId,
			'related' => 	$relatedId,

			// item identity
			'menu_section' => 		$section['id'],
			'menu_item' => 			$itemId,
			'inventory_group' => 	$ingredient['inventory_group']['id'],
			'ingredient' => 			$ingredientId,
			'menu_item_choice' => 	intval($choiceId),

			// display info for user
			'details' => $memo,

			// packlist meta data
			'location' => 			$menu['venue'],
			'start_date' => 			$section['from'],
			'end_date' => 			$section['to'],
			'quantity' => 			$itemQty,
			'quantity_filled' => 	0,
			'unit_type' => 			$ingredient['qty']['unit_type'],
			'units' => 				$ingredient['qty']['measurement'],
			'is_active' => 			$activeState,
			'items' => 				[],
			'inventory_item_ids' => 	[],
			'filled' => 				0,
			'type' => 				$resType

		);

		// if reservation is set as active, reserve stock items
		// 		if($activeState === 1){

		$stockItems = $this->pgObjects->where('inventory_items', [
			'inventory_group' => intval($reservation['inventory_group']),
			'expiration_date' => [
				'type' => 'after',
				'date' => $reservation['end_date']
			],
			'quantity' => [
				'type' => 'greater_than',
				'value' => 0
			]
		]);

		// if 'rental' type, get any reservations that might affect these stock items and adjust their available quantities
		if ($resType === 'rental') {

			$stockItems = $this->prioritizeStockItems($stockItems, $reservation);
			$otherReservations = $this->pgObjects->where(
				'item_reservation',
				[
					'inventory_group' => intval($reservation['inventory_group']),
					'start_date' => [
						'type' => 'between',
						'start' => $reservation['start_date'],
						'end' => $reservation['end_date']
					],
					'filled' => 1
				]
			);
			$otherReservations2 = $this->pgObjects->where(
				'item_reservation',
				[
					'inventory_group' => intval($reservation['inventory_group']),
					'end_date' => [
						'type' => 'between',
						'start' => $reservation['start_date'],
						'end' => $reservation['end_date']
					],
					'filled' => 1
				]
			);

			if (is_array($otherReservations2) and count($otherReservations2)) {
				foreach ($otherReservations2 as $another) {
					array_push($otherReservations, $another);
				}
			}
			$otherReservations2 = null;
			if (is_array($otherReservations)) {
				foreach ($otherReservations as $otherRes) {

					if ($otherRes['quantity_filled'] > 0 and is_array($otherRes['items'])) {

						foreach ($otherRes['items'] as $itemUsed) {

							foreach ($stockItems as $i => $stockItem) {

								if ($itemUsed['inventory_item'] == $stockItem['id']) {

									$stockItems[$i]['quantity'] = floatval($stockItems[$i]['quantity']) - $itemUsed['qty'];
								}
							}
						}
					}
				}
			}
		}

		// inventory items that need to be updated
		$toUpdate = [];

		$i = 0;
		while ($reservation['filled'] === 0 && $i < count($stockItems)) {

			// if there is enough, use what is needed and stop
			if ((floatval($reservation['quantity']) - floatval($reservation['quantity_filled'])) <= floatval($stockItems[$i]['quantity'])) {

				$stockItems[$i]['quantity'] -= (floatval($reservation['quantity']) - floatval($reservation['quantity_filled']));

				// update reservation item
				array_push($reservation['items'], [
					'qty' => (floatval($reservation['quantity']) - floatval($reservation['quantity_filled'])),
					'inventory_item' => $stockItems[$i]['id'],
					'location' => $stockItems[$i]['location']
				]);
				array_push($reservation['inventory_item_ids'], $stockItems[$i]['id']);

				$reservation['quantity_filled'] = $reservation['quantity'];

				if ($activeState === 1) {
					$reservation['filled'] = 1;
				}

				// if there is not enough in this stock item, take some and use the next one too
			} else {

				// update reservation item
				array_push($reservation['items'], [
					'qty' => $stockItems[$i]['quantity'],
					'inventory_item' => $stockItems[$i]['id'],
					'location' => $stockItems[$i]['location']
				]);
				array_push($reservation['inventory_item_ids'], $stockItems[$i]['id']);

				$reservation['quantity_filled'] += $stockItems[$i]['quantity'];
				$stockItems[$i]['quantity'] = 0;
			}

			// only update the stock of perishable items (non-perishables act as rentals)
			if ($resType === 'use') {

				array_push($toUpdate, [
					'id' => $stockItems[$i]['id'],
					'quantity' => $stockItems[$i]['quantity'],
					'object_bp_type' => 'inventory_items'
				]);
			}

			$i++;
		}

		// 		}

		$response = [];
		$response['reservation'] = $this->pgObjects->create(
			'item_reservation',
			$reservation,
			0,
			[
				'filled' => true, 'inventory_group' => [
					'name' => true, 'uom' => true
				], 'quantity' => true, 'quantity_filled' => true, 'type' => true, 'uom' => true
			]
		);

		if ($activeState) {

			$response['toUpdate'] = $this->pgObjects->update('inventory_items', $toUpdate, 0);
		}

		return $response;
	}

	public function get_full_recipe($item)
	{

		// get next level of recipes
		$ingredientIds = [];
		if (is_array($item['items'])) {
			foreach ($item['items'] as $i => $ingredient) {

				if (is_array($ingredient['choices']) and count($ingredient['choices']) > 0) {

					foreach ($ingredient['choices'] as $j => $choiceIngredient) {

						if (intval($choiceIngredient['inventory_group']) > 0) {

							array_push($ingredientIds, intval($choiceIngredient['inventory_group']));
						}
					}
				} elseif (intval($ingredient['inventory_group']) > 0) {

					array_push($ingredientIds, intval($ingredient['inventory_group']));
				}
			}
		}

		$ingredientsObjs = $this->pgObjects->getById(null, $ingredientIds, [
			'name' => true,
			'items' => true,
			'stock_type' => true,
			'selection' => true,
			'description' => true,
			'uom' => true
		]);

		// merge into this layer of recipe, and crawl through all layers
		if (is_array($item['items'])) {
			foreach ($item['items'] as $i => $ingredient) {

				if (is_array($ingredient['choices']) and count($ingredient['choices']) > 0) {

					foreach ($ingredient['choices'] as $j => $choiceIngredient) {

						if (intval($choiceIngredient['inventory_group']) > 0) {

							$item['items'][$i]['choices'][$j]['inventory_group'] = $this->get_full_recipe(__::find($ingredientsObjs, function ($ingrObj) use ($choiceIngredient) {
								return $ingrObj['id'] === $choiceIngredient['inventory_group'];
							}));

							// remove null vals
							if ($item['items'][$i]['choices'][$j]['inventory_group'] === false) {
								unset($item['items'][$i]['choices'][$j]);
							}
						}
					}

					// reset indexes to keep from casting as an obj
					$item['items'][$i]['choices'] = array_values($item['items'][$i]['choices']);
				} elseif (intval($ingredient['inventory_group']) > 0) {

					$item['items'][$i]['inventory_group'] = $this->get_full_recipe(__::find($ingredientsObjs, function ($ingrObj) use ($ingredient) {

						return $ingrObj['id'] === $ingredient['inventory_group'];
					}));

					// remove null vals
					if ($item['items'][$i]['inventory_group'] === false) {

						unset($item['items'][$i]['choices'][$j]);
					}
				}

				// reset indexes to keep from casting as an obj
				$item['items'] = array_values($item['items']);
			}
		}

		return $item;
	}

	private function prioritizeStockItems($stockItems, $reservation)
	{

		$atSameLocation = __::filter($stockItems, function ($item) use ($reservation) {

			return $item['location'] === $reservation['location'];
		});

		$atSeperateLocation = __::filter($stockItems, function ($item) use ($reservation) {

			return $item['location'] !== $reservation['location'];
		});

		$atSameLocation = __::sortBy($atSameLocation, function ($item) {

			return strtotime($item['expiration_date']);
		});

		$atSeperateLocation = __::sortBy($atSeperateLocation, function ($item) {

			return strtotime($item['expiration_date']);
		});

		return array_merge($atSameLocation, $atSeperateLocation);
	}

	// public

	public function canNestItem($itemId = 0, $nestItemId = null, $is_external_call = 1)
	{

		// if an outside request, get data from $_POST
		if ($is_external_call) {

			$itemId = intval($_POST->itemId);
			$nestItemId = intval($_POST->nestItemId);
		}

		// return out of func if inputs are not valid
		if (
			!is_int($itemId) or
			$itemId === 0 or
			!is_int($nestItemId) or
			$nestItemId === 0
		) {

			return false;
		}

		// get main item
		$item = $this->pgObjects->getById(null, $itemId, [
			'id' => true,
			'items' => true
		]);

		// check if item can be nested and return response
		$ret = $this->can_nest_item($item, [$nestItemId]);

		return $this->sendData($ret, $is_external_call);
	}

	public function createReservation($serverRequest = 1)
	{

		// 		error_reporting(E_ALL);
		// 		ini_set('display_errors', '1');

		//!TODO: these should become arguments
		$groupId = 0;
		$qty = 0;
		$memo = '';
		$startDate = '';
		$endDate = '';

		if ($serverRequest == 1) {

			$request = $this->objToArr($_POST);

			$groupId = intval($request['group']);
			$qty = floatval($request['qty']);
			$memo = (string)$request['memo'];
			$startDate = (string)$request['start_date'];
			$endDate = (string)$request['end_date'];

			// for overriding rental reservation
			$remove_forever = $request['remove_forever'];
		}

		$groupObj = $this->pgObjects->getById('inventory_groups', $groupId);

		$ret = $this->assignStockItem(

			// not attached to a menu
			null,

			// $section => needs start and end date
			[
				'from' => $startDate,
				'to' => $endDate
			],
			null,

			// $ingredient
			[
				'id' => 0,
				'inventory_group' => $groupObj
			],

			// $itemId, from menu (no menu, so 0)
			0,

			// $itemQty
			$qty,

			// no choice meta-data
			0,
			null,

			// user memo
			$memo,

			// is active?
			true,

			// override rental type
			$remove_forever

		);

		// 		assignStockItem($menu, $section, $billableGroup, $ingredient, $itemId, $itemQty, $choiceId = 0, $parentIngredient = null);

		return $this->sendData($ret, $serverRequest);
	}

    public function getFullRecipes($itemsId = [], $external_call = 1, $childObjs = null)
    {

        if ($external_call) {
            $itemsId = $_POST->itemsId;
        }

        $fullItems = [];
        foreach($itemsId as $itemId) {

            if ($childObjs == null) {

                $childObjs = [
                    'name' => true,
                    'items' => true,
                    'stock_type' => true,
                    'selection' => true,
                    'description' => true,
                    'uom' => true
                ];
            }
            $item = $this->pgObjects->getById(null, $itemId, $childObjs);
            $item = $this->get_full_recipe($item);
            $fullItems[] = $item;
        }

        return $this->sendData($fullItems, $external_call);
    }

	public function getFullRecipe($itemId = null, $external_call = 1, $childObjs = null)
	{

		if ($external_call) {
			$itemId = intval($_POST->itemId);
		}

		if ($childObjs == null) {

			$childObjs = [
				'name' => true,
				'items' => true,
				'stock_type' => true,
				'selection' => true,
				'description' => true,
				'uom' => true
			];
		}

		$item = $this->pgObjects->getById(null, $itemId, $childObjs);
		$item = $this->get_full_recipe($item);

		return $this->sendData($item, $external_call);
	}

	public function setMenuReservations($menuId = null, $json = 1)
	{

		$Response = [
			'items' => []
		];

		// Verify input.
		if ($menuId == null) {
			$menuId = (int) $_POST->menuId;
		}
		if ($menuId < 1) {
			return $this->sendData(false, $json);
		}

		// !TODO: Create a way to easily lock processes down
		// 			to keep processes from stepping on each
		// 			others' toes.
		/*
$que = true;
		$i = 0;
		while ($que) {

			// Check that no menus are currently in the process of being
			// reserved.
			if (!empty($this->pgObjects->getById('inventory_menu', $menuId, ))) {

				$que = false;
				$this->pgObjects->queue(
					'inventory_menu'
					, $menuId
					, 'reserve'
					)

			}

			// Stop trying
			$i++;
			if ($i > 0) {

				$que = false;
				return $this->sendData(
					[
						'msg' => 'Try again later.'
					]
					, $json
				);

			}

		}
*/



		// Wait, and try again.

		// Claim spot in line.
		// 		$this->pgObjects->

		// Get menu object.
		$menu = $this->pgObjects->getById(
			'inventory_menu',
			$menuId,
			[
				'active' => true, 'sections' => [
					'start' => true, 'end' => true, 'items' => 'id'
				], 'guest_count' => true
			]
		);

		if (empty($menu) or $menu['object_bp_type'] !== 'inventory_menu') {
			return $this->sendData(false, $json);
		}

		// get old reservations
		$reservationsToDelete = $this->pgObjects->where('item_reservation', array(
			'menu' => $menu['id']
		));

		foreach ($reservationsToDelete as $i => $res) {

			$this->pgObjects->delete('item_reservation', $res['id']);
		}

		// Generate reservations, and update stock quantities.
		foreach ($menu['sections'] as $i => $section) {

			foreach ($section['items'] as $j => $itemId) {

				$groupRoot = $this->pgObjects->getById(
					'inventory_billable_groups',
					$itemId['item']['id'],
					[
						'name' => true,
						'items' => true,
						'price' => true,
						'price_per_person' => true,
						'price_per_hour' => true,
						'price_per_hour_per_person' => true,
						'description' => true,
						'category' => [
							'name' => true, 'chart_of_account' => true, 'default_tax_rate' => true, 'surcharges' => true
						],
						'chart_of_account' => ['id' => true],
						'tax_rates' => true,
						'default_tax_rate' => ['id' => true]
					]
				);
				$fullRecipe = $this->get_full_recipe($groupRoot);

				array_push(
					$Response['items'],
					$this->assignMenuItem(
						$menu,
						$section,
						$fullRecipe,
						$itemId['qty'],
						$itemId['id'],
						$itemId['choices'],
						$itemId['qty_type']
					)
				);
			}
		}

		// flag as updating

		return $this->sendData($Response, $json);
	}

	public function setTestEnvironment($json = 1)
	{

		// Check if in dev env
		if (getenv('TEST_ENVIRONMENT') === 'ON') {

			// Check for 'TEST-ENV' instance
			$testInstance = $this->pgObjects->whereAcrossInstances(
				'instances',
				[
					'instance' => 'dev_test_env'
				],
				'',
				0,
				false,
				0,
				'null',
				'asc',
				1,
				null,
				[],
				'string'
			)[0];

			// If it doesn't exist, return false
			if (empty($testInstance)) {

				return $this->sendData(false, $json);

				// Otherwise, just check for token and create if it isn't set
			} else {

				return $this->sendData(true, $json);
			}
		} else {

			return $this->sendData(false, $json);
		}
	}

	public function updateMenuSections($menuId = null, $sections = null, $type = 'edit-section', $json = 1)
	{

		$updated = [];
		$ret = false;

		if ($menuId == null) {
			$menuId = $_POST->menuId;
			$sections = $_POST->sections;
		}

		if (count($sections) > 0) {

			$ret = true;

			foreach ($sections as $section) {

				$updateObject = array(
					"type" => $type,
					"menu" => $menuId,
					"section" => $section
				);

				$updated[] = $this->updateInventoryMenu(json_decode(json_encode($updateObject)), false);
			}
		}

		return $this->sendData($updated[(count($sections) - 1)], $json);
	}

	public function updateRecipeIngredients($group = null, $json = 1)
	{

		if ($json === 1) {
			$itemId = intval($_POST->itemId);
			$ingredients = $this->objToArr($_POST->ingredients);
			$rootSelection = $this->objToArr($_POST->selection);
		}

		// get main item
		$item = $this->pgObjects->getById(null, $itemId, [
			'id' => true,
			'items' => true
		]);

		// get ingredient item ids
		$ingredientIds = [];
		if (is_array($ingredients)) {
			foreach ($ingredients as $ingredient) {

				if (is_array($ingredient['inventory_group'])) {

					array_push($ingredientIds, intval($ingredient['inventory_group']['id']));
				} elseif (is_array($ingredient['choices'])) {

					foreach ($ingredient['choices'] as $choiceIngredient) {

						if (is_array($choiceIngredient['inventory_group'])) {

							array_push($ingredientIds, intval($choiceIngredient['inventory_group']['id']));
						}
					}
				}
			}
		}

		// check that each ingredient can be nested
		$canNestResp = [];
		$canNest = true;

		$canNestResp = $this->can_nest_item($item, $ingredientIds);

		// if ingredient can be nested, update item in db and respond w/updated item
		if ($canNestResp['can_be_nested']) {

			$response = $this->pgObjects->update($item['object_bp_type'], [
				'id' => $item['id'],
				'items' => $ingredients,
				'selection' => $rootSelection
			]);

			$response['saved'] = true;
			return $this->sendData($response, $json);

			// otherwise, reply w/message as to why updates cannot be made
		} else {

			return $this->sendData($canNestResp, $json);
		}
	}

	// UNIT SYSTEM

	public function get_unit($unit_sys = 'uscu', $unit_type = 'quantity')
	{

		$units_of_measure = [
			'uscu' => [
				'quantity' => [
					'display_name' => 'Quantity',
					'base' => [
						'name' => 'single',
						'abbr' => ''
					],
					'measurements' => [[
						'id' => 1,
						'name' => 'dozen',
						'abbr' => 'dz',
						'divisor' => 1,
						'multiplier' => 12,
						'base_reference' => 0
					]]
				],
				'weight' => [
					'display_name' => 'Weight',
					'base' => [
						'name' => 'pound',
						'abbr' => 'lb'
					],
					'measurements' => [[
						'id' => 1,
						'name' => 'ounce',
						'abbr' => 'oz',
						'divisor' => 16,
						'multiplier' => 1,
						'base_reference' => 0
					]]
				],
				'dry_volume' => [
					'display_name' => 'Dry volume',
					'base' => [
						'name' => 'pint',
						'abbr' => 'pt'
					],
					'measurements' => [
						/*
[
					    'id' => 5,
					    'name' => 'dash',
					    'abbr' => 'dash',
					    'divisor' => 16,
					    'multiplier' => 1,
					    'base_reference' => 7
				    ], [
					    'id' => 6,
					    'name' => 'pinch',
					    'abbr' => 'pinch',
					    'divisor' => 8,
					    'multiplier' => 1,
					    'base_reference' => 7
				    ],
*/
						[
							'id' => 7,
							'name' => 'teaspoon',
							'abbr' => 'tsp',
							'divisor' => 3,
							'multiplier' => 1,
							'base_reference' => 8
						], [
							'id' => 8,
							'name' => 'tablespoon',
							'abbr' => 'Tbsp',
							'divisor' => 16,
							'multiplier' => 1,
							'base_reference' => 9
						], [
							'id' => 9,
							'name' => 'dry cup',
							'abbr' => 'cup',
							'divisor' => 2.33,
							'multiplier' => 1,
							'base_reference' => 0
						], [
							'id' => 1,
							'name' => 'quart',
							'abbr' => 'qt',
							'divisor' => 1,
							'multiplier' => 2,
							'base_reference' => 0
						], [
							'id' => 2,
							'name' => 'gallon',
							'abbr' => 'gal',
							'divisor' => 1,
							'multiplier' => 4,
							'base_reference' => 1
						], [
							'id' => 3,
							'name' => 'peck',
							'abbr' => 'pk',
							'divisor' => 1,
							'multiplier' => 2,
							'base_reference' => 2
						], [
							'id' => 4,
							'name' => 'bushel',
							'abbr' => 'bu',
							'divisor' => 1,
							'multiplier' => 4,
							'base_reference' => 3
						]
					]
				],
				'liquid_volume' => [
					'display_name' => 'Liquid volume',
					'base' => [
						'name' => 'teaspoon',
						'abbr' => 'tsp'
					],
					'measurements' => [[
						'id' => 1,
						'name' => 'tablespoon',
						'abbr' => 'Tbsp',
						'divisor' => 1,
						'multiplier' => 3,
						'base_reference' => 0
					], [
						'id' => 2,
						'name' => 'US fluid ounce',
						'abbr' => 'fl oz',
						'divisor' => 1,
						'multiplier' => 2,
						'base_reference' => 1
					], [
						'id' => 3,
						'name' => 'US cup',
						'abbr' => 'fl oz',
						'divisor' => 1,
						'multiplier' => 8,
						'base_reference' => 2
					], [
						'id' => 4,
						'name' => 'liquid US pint',
						'abbr' => 'pt',
						'divisor' => 1,
						'multiplier' => 2,
						'base_reference' => 3
					], [
						'id' => 5,
						'name' => 'liquid US quart',
						'abbr' => 'qt',
						'divisor' => 1,
						'multiplier' => 2,
						'base_reference' => 4
					], [
						'id' => 6,
						'name' => 'liquid US gallon',
						'abbr' => 'gal',
						'divisor' => 1,
						'multiplier' => 4,
						'base_reference' => 5
					], [
						'id' => 7,
						'name' => 'liquid barrel',
						'abbr' => 'bbl',
						'divisor' => 1,
						'multiplier' => 31.5,
						'base_reference' => 6
					]]
				]
			],
			'metric' => []
		];

		return $units_of_measure[$unit_sys][$unit_type];
	}

	private function getBaseUnitMultiplier($measurements, $unit, $multiplier)
	{

		if ($multiplier === null) {
			$multiplier = [
				'num' => 1,
				'div' => 1
			];
		}

		if ($unit > 0) {

			$measurement = __::find($measurements, function ($measurement) use ($unit) {

				return $measurement['id'] === $unit;
			});
		} else {

			return $multiplier;
		}

		$multiplier['num'] = $multiplier['num'] * intval($measurement['multiplier']);
		$multiplier['div'] = $multiplier['div'] * intval($measurement['divisor']);

		if ($measurement['base_reference'] > 0) {

			$multiplier = $this->getBaseUnitMultiplier($measurements, intval($measurement['base_reference']), $multiplier);
		}

		return $multiplier;
	}

	private function to_base_units($unit_sys = 'uscu', $unit_type = 'quantity', $measurement = 0, $value = 1)
	{

		if (intval($measurement) > 0) {

			$step = __::find(

				// measurements
				$this->get_unit($unit_sys, $unit_type)['measurements'],

				// select measurement
				function ($unit = []) use ($measurement) {
					return intval($measurement) === $unit['id'];
				}

			);

			$value = $value * ($step['multiplier'] / $step['divisor']);
			return $this->to_base_units($unit_sys, $unit_type, $step['base_reference'], $value);
		} else {

			return $value;
		}
	}

	private function toBaseUnits($item, $measurements)
	{

		$baseRef = $this->getBaseUnitMultiplier($measurements, $item['units']);

		return ($item['multiplier'] / $item['divisor']) * ($baseRef['num'] / $baseRef['div']);
	}

	// INVENTORY MENU MANIPULATION

	private function parseMenu($menu)
	{

		if (
			is_array($menu)
			&& is_array($menu['sections'])
		) {

			foreach ($menu['sections'] as $i => $section) {

				if (is_array($section['items'])) {

					$menu['sections'][$i]['items'] = __::filter($section['items'], function ($item) {

						return $item['object_bp_type'] === 'inventory_menu_line_item';
					});
				}
			}
		}

		return $menu;
	}

	public function getLineItemAbsolutePrice($item, $menu)
	{

		$absolutePrice = 0;
		$section = __::find($menu['sections'], function ($sec) use ($item) {
			return $sec['id'] === $item['section'];
		});

		// Custom items that don't have all the pricing options set
		if (
			$item['price_type'] !== 'price'
			&& empty($item['item'][$item['price_type']])
			&& empty($item['item']['id'])
		) {
			$item['item'][$item['price_type']] = $item['item']['price'];
		}

		switch ($item['price_type']) {

			case 'price':
				$absolutePrice = $item['item']['price'];
				break;

			case 'price_per_person':
				$absolutePrice = $item['item']['price_per_person'] * $menu['guest_count'];
				break;

			case 'price_per_hour_per_person':
				if ($section['from'] === '' || $section['to'] === '') {

					$hours = 1;
				} else {

					$date1 = new DateTime($section['from']);
					$date2 = new DateTime($section['to']);

					$diff = ($date2->getTimestamp() - $date1->getTimestamp()) - ($date1->getOffset() - $date2->getOffset());
					$hours = $diff / 3600;
				}

				$absolutePrice = $item['item']['price_per_hour_per_person'] * $menu['guest_count'] * $hours;
				break;

			case 'price_per_hour':
				if ($section['from'] === '' || $section['to'] === '') {

					$hours = 1;
				} else {

					$date1 = new DateTime($section['from']);
					$date2 = new DateTime($section['to']);

					$diff = ($date2->getTimestamp() - $date1->getTimestamp()) - ($date1->getOffset() - $date2->getOffset());
					$hours = $diff / 3600;
				}

				$absolutePrice = $item['item']['price_per_hour'] * $hours;
				break;

			default:
				break;
		}

		return $absolutePrice * $item['qty'];
	}

	private function generateNestedChoiceNames($nestedChoices)
	{

		static $choiceNames = array();

		foreach ($nestedChoices as $choice) {

			$currentChoice = (array)$choice;

			if (count($currentChoice['childChoices']) > 0) {

				array_push($choiceNames, $currentChoice['name']);

				$currentChoice['name'][0] = $this->generateNestedChoiceNames($currentChoice['childChoices']);
			} else {

				array_push($choiceNames, $currentChoice['name']);
			}
		}

		return $choiceNames;
	}

	private function getnestedChildChoice($choice)
	{

		$choiceNames = array();

		//$currentChoice = (array)$choice;



		/*
if (count($currentChoice['childChoices']) > 0) {

			array_push($choiceNames, $this->getnestedChildChoice($currentChoice));

		} else {

			array_push($choiceNames, $currentChoice['name']);

		}
*/

		return $choiceNames;
	}

	private function addItemToMenu($event = null, $sysSettings = null)
	{

		// 		error_reporting(E_ALL);
		// 		ini_set('display_errors', '1');

		if ($event === null) {
			return $this->sendData(false, 1);
		}

		$updateData = $event;

		$itemQty = floatval($updateData->qty->quantity);
		$yieldType = $updateData->qty->unitType;
		$measurement = intval($updateData->qty->measurement);
		$servings = 0;
		$sectionObj = null;
		$lineItemCategory = null;
		$chartOfAcct = 0;

		$itemNotes = $updateData->note;

		if ($measurement > 0 and $yieldType !== 'servings') {
			$itemQty = $this->to_base_units('uscu', $yieldType, $measurement, $itemQty);
		}

		if ($yieldType === 'servings') {
			$servings = $itemQty;
			$itemQty = $itemQty * ($updateData->item->item_yield->quantity / $updateData->item->item_yield->servings);
		}

		$single_unit = $sysSettings['single_unit'];
		$sortId = intval($event->sortId);
		$response = [];

		//!TESTING
		$sortId = 1;

		// get menu and add item to it
		$menu = $this->pgObjects->getById('inventory_menu', $updateData->menu, 0);
		$choiceSelection = $this->objToArr($updateData->choiceSelection);
		$nestedIngredients = $this->objToArr($updateData->item->nestedIngredients);

		$isProductReference = $updateData->isProductReference;

		if ($isProductReference) {

			$groupRoot = $this->pgObjects->getById(
				'inventory_billable_groups',
				$updateData->item->id,
				[
					'name' => true,
					'items' => true,
					'price' => true,
					'price_per_person' => true,
					'price_per_hour' => true,
					'price_per_hour_per_person' => true,
					'description' => true,
					'category' => [
						'name' => true, 'chart_of_account' => true, 'default_tax_rate' => true, 'surcharges' => true
					],
					'chart_of_account' => ['id' => true],
					'tax_rates' => true,
					'default_tax_rate' => ['id' => true]
				]
			);
			$fullRecipe = $this->get_full_recipe($groupRoot);

			$lineItemCategory = $groupRoot['category'];

			// get latest ID for item in menu
			$itemId = 1;

			if (is_array($menu['sections'])) {
				foreach ($menu['sections'] as $i => $section) {

					// get section obj to use later
					if (intval($updateData->section) == intval($section['id'])) {
						$sectionObj = $section;
					}

					if (is_array($section['items'])) {
						foreach ($section['items'] as $j => $item) {

							// update counter for next item
							if ($item['id'] >= $itemId) {
								$itemId = $item['id'] + 1;
							}
						}
					}
				}
			}

			$lineItemState = 'selections_open';
			$reservations = [];

			// 			$groupRoot['description'] = '';

		} else {

			$groupRoot = [
				'name' => $updateData->item->name,
				'items' => [],
				'price' => $updateData->item->price,
				'description' => $updateData->item->description,
				'category' => $updateData->item->category,
				'is_estimate' => $updateData->item->is_estimate
			];

			$catToUseId = $updateData->item->category;
			if (is_object($catToUseId)) {
				$catToUseId = $catToUseId->id;
			}

			$lineItemCategory = $this->pgObjects->getById(
				'inventory_billable_categories',
				intval($catToUseId),
				[
					'name' => true, 'chart_of_account' => true, 'default_tax_rate' => true, 'surcharges' => true
				]
			);
		}

		// Validate account
		$chartOfAcct = $groupRoot['chart_of_account'];
		if (!$chartOfAcct) {

			if ($lineItemCategory) {
				$chartOfAcct = $lineItemCategory['chart_of_account'];
			}
		}
		if (is_array($chartOfAcct)) {
			$chartOfAcct = $chartOfAcct['id'];
		}
		// If no chart account on item or on category
		// return error message
		if (!$chartOfAcct) {

			$response['added'] = false;
			$response['msg'] = 'No chart of account selected for associated category ' . $lineItemCategory['name'] . '.';

			return $response;
		}
		// If the company for the account is not set (since archived), also
		// through return error message.
		$chartOfAcct = $this->pgObjects->getById(
			'chart_of_accounts',
			$chartOfAcct,
			[
				'chart_of_accounts_company' => [
					'name' => true
				]
			]
		);
		if (empty($chartOfAcct['chart_of_accounts_company'])) {

			$response['added'] = false;
			$response['msg'] = 'No active company selected for the associated account.';
			return $response;
		}


		// default to not overriding category default tax rates on create
		$overrideTaxRate = 0;
		$taxRates = [];

		// set child objs to just the ids needed
		if (is_array($groupRoot['category'])) {
			$groupRoot['category'] = intval($groupRoot['category']['id']);
		} elseif (is_object($groupRoot['category'])) {
			$groupRoot['category'] = intval($groupRoot['category']->id);
		}

		if ($updateData->item->estimate) {
			$groupRoot['estimate'] = intval($updateData->item->estimate);
		} else {
			$groupRoot['estimate'] = 0;
		}

		if ($updateData->item->is_exclusive_to_cat) {
			$groupRoot['is_exclusive_to_cat'] = true;
		}

		// test for chart of accounts on tax rates
		if (count($taxRates) > 0) {

			$taxRateObjs = $this->pgObjects->getById(
				'tax_rates',
				$taxRates,
				[
					'name' => true, 'chart_of_account' => true
				]
			);
		} else {

			$taxRateObjs = $lineItemCategory['default_tax_rate'];
		}

		// Validate that tax rates have a valid chart of account set
		foreach ($taxRateObjs as $i => $taxRateObj) {

			if ($taxRateObj['chart_of_account'] == null) {

				$response['added'] = false;
				$response['msg'] = 'No chart of account selected for associated tax rate ' . $taxRateObj['name'] . '.';
				return $response;
			}
		}

		// default surcharges on category
		$surcharges = [];
		if (
			is_array($lineItemCategory)
			and is_array($lineItemCategory['surcharges'])
		) {
			$surcharges = __::pluck($lineItemCategory['surcharges'], 'id');
		}

		// Set 'date_of_use' value
		$dateOfUse = date('Y-m-d h:i:s');
		if ($sectionObj['from'] != null) {
			$dateOfUse = $sectionObj['from'];
		} elseif ($sectionObj['to'] != null) {
			$dateOfUse = $sectionObj['to'];
		}

		// create item
		$absolutePrice = $this->getLineItemAbsolutePrice(
			[
				'price_type' => 	$updateData->price_type, 'section' => 		intval($updateData->section), 'qty' => 			$itemQty, 'item' => 		$groupRoot
			],
			$menu
		);

		$newLineItem = $this->pgObjects->create(
			'inventory_menu_line_item',
			[
				'absolute_price' => 	$absolutePrice,
				'coa' 		   => 		$chartOfAcct,
				'date_of_use'  => 		$dateOfUse,
				'qty' 		   => 		$itemQty,
				'qty_type' 	   => 		$updateData->qty_type,
				'absolute_qty' => 		$itemQty,
				'price_type'   => 		$updateData->price_type,
				'sortId' 	   => 		$sortId,
				'item' 		   => 		$groupRoot,
				'product' 	   => 		$groupRoot['id'],
				'choices' 	   => 		$choiceSelection,
				'state' 	   => 		$lineItemState,
				'menu'         => 		$menu['id'],
				'owner'        => 		$menu['owner'],
				'section' 	   => 		intval($updateData->section),
				'tax_rates'    => 		$taxRates,
				'type' 		   => 		$updateData->item->type,
				'yield_type'   => 		$yieldType,
				'servings' 	   => 	 	$servings,
				'vendor'       => 		intval($updateData->item->vendor),
				'vendor_approval_status' => ($updateData->item->type == 'vendor' ? 'PENDING' : null),
				'measurement'  => 		$measurement,
				'surcharges'   => 		$surcharges,
				'note'		   =>		$itemNotes
			]
		);

		if ($updateData->item->type == 'vendor') {
			require_once APP_ROOT . '/services/VendorService.php';
			$vendorService = new VendorService($this);
			$vendorService->notifyApproversOfPendingVendor($newLineItem);
		}

		// if choices have yet to be made, do not try to make reservations and set item state to "selections_open"
		if ($isProductReference and is_array($choiceSelection)) {

			$lineItemState = 'selections_completed';
			$section = [];
			$section = __::find($menu['sections'], function ($sec) use ($updateData) {
				return $sec['id'] === $updateData->section;
			});

			// get reservations to create
			$assignData = $this->assignMenuItem(
				$menu,
				$section,
				$fullRecipe,
				$itemQty,
				$newLineItem['id'],
				$choiceSelection,
				$updateData->qty_type
			);


			$reservations = $assignData['reservations'];

			// use pricing that reflects additional prices from choices
			$priceTypeStr = $newLineItem['price_type'];
			$groupRoot['price_per_person'] = $assignData['item_price'];
			$groupRoot['price_per_hour'] = $assignData['item_price'];
			$groupRoot['price_per_hour_per_person'] = $assignData['item_price'];
			$groupRoot['price'] = $assignData['item_price'];
			$groupRoot['beo_html'] = $assignData['HTML'];

			if (is_array($assignData['selections_strings']) and count($assignData['selections_strings']) > 0) {
				//$groupRoot['description'] .= ' w/' . implode(', ', $assignData['selections_strings']);
				$groupRoot['ingredient_names'] = $assignData['selections_strings'];
			} else {
				// 				$groupRoot['description'] = '';
				$groupRoot['ingredient_names'] = [];
			}

			$groupRoot['nestedIngredients'] = $nestedIngredients;

			$newLineItem = $this->pgObjects->update('inventory_menu_line_item', [
				'id' => $newLineItem['id'],
				'state' => $lineItemState,
				'item' => $groupRoot,
				'absolute_qty' => $assignData['absolute_qty']
			]);

			// create an empty reservation (just used for display/packlist) for one-off line items in menu
		} elseif ($updateData->item->type === 'item') {

			$newReservation = array(

				'object_bp_type' => 'item_reservation',

				// related objs
				'menu' => $menu['id'],
				'related' => $menu['related'],

				// item identity
				'menu_section' => intval($updateData->section),
				'menu_item' => $newLineItem['id'],
				'inventory_group' => $newLineItem['id'],
				'ingredient' => 0,
				'menu_item_choice' => 0,

				// display info for user
				'details' => $newLineItem['item']['description'],

				// packlist meta data
				'location' => $menu['venue'],
				'start_date' => $section['from'],
				'end_date' => $section['to'],
				'quantity' => $itemQty,
				'quantity_filled' => 0,
				'units' => 0,
				'is_active' => 0,
				'items' => [],
				'inventory_item_ids' => [],
				'filled' => 0,
				'type' => 'untracked'

			);

			$reservations = [];
			array_push(
				$reservations,
				$this->pgObjects->create('item_reservation', $newReservation)
			);
		}

		// add item to section
		foreach ($menu['sections'] as $i => $section) {

			if ($section['id'] === intval($updateData->section)) {

				array_push(
					$menu['sections'][$i]['items'],
					$newLineItem['id']
				);

				foreach ($section['items'] as $j => $item) {

					// reset all sortId values of items in section
					$menu['sections'][$i]['items'][$j]['sortId'] = $j + 1;
				}
			}
		}

		// create split items
		if (
			intval($updateData->item->vendor)
			and count($event->splitItems) > 0
		) {

			$splitLineItems = [];

			foreach ($event->splitItems as $i => $item) {

				$splitLineItems[$i] = $newLineItem;
				$splitLineItems[$i]['item'] = [
					'name' => $item->name,
					'items' => [],
					'price' => $item->price,
					'description' => $item->description,
					'category' => $item->category,
					'is_estimate' => $updateData->item->is_estimate
				];
				$splitLineItems[$i]['price_type'] = $item->priceStyle;
			}

			$createdSplitItems = $this->pgObjects->create(
				'inventory_menu_line_item',
				$splitLineItems,
				true // batch creates
			);

			// add items to section
			foreach ($menu['sections'] as $i => $section) {

				if ($section['id'] === intval($updateData->section)) {

					foreach ($createdSplitItems as $j => $toAdd) {

						if ($toAdd['object_bp_type'] === 'inventory_menu_line_item') {

							array_push(
								$menu['sections'][$i]['items'],
								$toAdd['id']
							);
						}
					}
				}
			}
		}

		// update menu
		$response['menu'] = $this->parseMenu($this->pgObjects->update(
			'inventory_menu',
			$menu,
			2
		));

		$response['reservations'] = $reservations;
		$response['newItem'] = $newLineItem;
		$response['splitItems'] = __::pluck($createdSplitItems, 'id');
		$response['added'] = true;

		return $response;
	}

	private function addSectionToMenu($event = null, $sysSettings = null)
	{

		$response = [];

		// get menu and add section to it
		$menu = $this->pgObjects->getById(
			'inventory_menu',
			intval($event->menu),
			0
		);
		$menuSections = $menu['sections'];

		// get next section id
		$nextId = 1;
		if (is_array($menuSections)) {
			foreach ($menuSections as $i => $section) {

				if ($nextId <= intval($section['id'])) {

					$nextId = intval($section['id']) + 1;
				}
			}
		}

		$newSection = [
			'id' => $nextId, 'sortId' => count($menuSections) + 1, 'name' => 'New section', 'from' => $menu['start_date'], 'to' => $menu['end_date'], 'items' => []
		];

		array_push($menuSections, $newSection);

		$menu['sections'] = $menuSections;

		$response['menu'] = $this->parseMenu($this->pgObjects->update(
			'inventory_menu',
			[
				'id' => $menu['id'], 'sections' => $menu['sections']
			],
			2
		));

		return $response;
	}

	private function adjustMenuItemQuantity($event = null, $sysSettings = null)
	{

		if ($event === null) {
			return $this->sendData(false, 1);
		}

		$updateData = $event;
		$single_unit = $sysSettings['single_unit'];
		$response = [];

		// get menu to update
		$menu = $this->pgObjects->getById('inventory_menu', $updateData->menu, 0);

		// get applicable reservations
		$reservationsToDelete = $this->pgObjects->where('item_reservation', array(
			'menu' => $updateData->menu,
			'menu_item' => $updateData->item
		));
		$billableGroup = [];

		$lineItemToUpdate = $this->pgObjects->getById('inventory_menu_line_item', $updateData->item);
		$choiceSelection = $lineItemToUpdate['choices'];
		$sectionId = $lineItemToUpdate['section'];
		$billableGroup = $lineItemToUpdate['item'];

		// update selections, pricing, and selections string arr
		if (!$updateData->qty) {

			$choiceSelection = $this->objToArr($updateData->selection);
		}

		$groupRoot = $this->pgObjects->getById('inventory_billable_groups', $billableGroup['id'], [
			'name' => true,
			'items' => true,
			'price' => true,
			'price_per_person' => true,
			'price_per_hour' => true,
			'price_per_hour_per_person' => true,
			'description' => true,
			'category' => 'id',
			'tax_rates' => true,
			'default_tax_rate' => ['id' => true]
		]);

		$fullRecipe = $this->get_full_recipe($groupRoot);

		$stockItemIds = [];
		foreach ($reservationsToDelete as $i => $res) {
			foreach ($res['inventory_item_ids'] as $j => $id) {
				array_push($stockItemIds, intval($id));
			}
		}

		$stockItems = $this->pgObjects->where('inventory_items', [
			'id' => [
				'type' => 'or',
				'values' => $stockItemIds
			]
		]);

		// delete reservation objs
		$inventoryToUpdate = [];
		foreach ($reservationsToDelete as $i => $reservation) {

			foreach ($reservation['items'] as $j => $res) {

				$oldQty = __::find($stockItems, function ($stockItem) use ($res) {

					return $stockItem['id'] === $res['inventory_item'];
				})['quantity'];

				$oldQty = 0;
				foreach ($stockItems as $i => $stockItem) {
					if ($stockItem['id'] === $res['inventory_item']) {
						$oldQty = $stockItem['quantity'];
						$stockItems[$i]['quantity'] = $stockItems[$i]['quantity'] + $res['qty'];
					}
				}

				array_push($inventoryToUpdate, array(
					'id' => $res['inventory_item'],
					'quantity' => $oldQty + $res['qty'],
					'object_bp_type' => 'inventory_items'
				));
			}

			// delete reservation obj
			$this->pgObjects->delete('item_reservation', $reservation['id']);
		}

		// update stock items
		$response['updated_stock_items'] = $this->pgObjects->update('inventory_items', $inventoryToUpdate, 0);

		$reservations = [];
		$stockItemsToUpdate = [];

		$itemId = $updateData->item;
		if ($updateData->qty) {
			$itemQty = intval($updateData->qty);
			$newQtyType = $updateData->qty_type;
		} else {
			$itemQty = intval($lineItemToUpdate['qty']);
			$newQtyType = $lineItemToUpdate['qty_type'];
		}

		// get section for duration
		$section = __::find($menu['sections'], function ($section) use ($sectionId) {

			return $section['id'] === $sectionId;
		});

		// crawl recipe and assign out stock reservations/qty/pricing data
		$assignData = $this->assignMenuItem($menu, $section, $fullRecipe, $itemQty, $itemId, $choiceSelection, $newQtyType);

		// update menu item
		if ($updateData->qty) {

			// create item
			$absolutePrice = $this->getLineItemAbsolutePrice(
				[
					'price_type' => 	$updateData->price_type, 'section' => 		intval($updateData->section), 'qty' => 			$itemQty, 'item' => 		$groupRoot
				],
				$menu
			);

			$response['updatedItem'] = $this->pgObjects->update('inventory_menu_line_item', [
				'id' => 				$updateData->item,
				'absolute_qty' => 		$assignData['absolute_qty'],
				'absolute_price' => 	$absolutePrice,
				'qty' => 				$updateData->qty,
				'qty_type' => 			$updateData->qty_type
			]);
		}

		// if recipe selections have changed, update the details (pricing/details/ingr list)
		if (!$updateData->qty) {

			// use pricing that reflects additional prices from choices
			switch ($lineItemToUpdate['qty_type']) {

				case 'per_guest':
				case 'guest_count':
					$groupRoot['price_per_person'] = $assignData['item_price'];
					break;

				case 'per_hour':
					$groupRoot['price_per_hour'] = $assignData['item_price'];
					break;

				case 'per_hour_per_guest':
					$groupRoot['price_per_hour_per_person'] = $assignData['item_price'];
					break;

				default:
					$groupRoot['price'] = $assignData['item_price'];
					break;
			}

			if ($assignData['HTML'] != null) {
				$groupRoot['beo_html'] = $assignData['HTML'];
			}

			if (is_array($assignData['selections_strings']) and count($assignData['selections_strings']) > 0) {
				//$groupRoot['description'] .= ' w/' . implode(', ', $assignData['selections_strings']);
				$groupRoot['ingredient_names'] = $assignData['selections_strings'];
			} else {
				// 				$groupRoot['description'] = '';
				$groupRoot['ingredient_names'] = [];
			}

			// !TODO: Update absolute price here too
			// $absolutePrice = $this->getLineItemAbsolutePrice(
			// 	[
			// 		'price_type' => 	$updateData->price_type
			// 		, 'section' => 		intval($updateData->section)
			// 		, 'qty' => 			$itemQty
			// 		, 'item' => 		$groupRoot
			// 	]
			// 	, $menu
			// );

			$response['updatedItem'] = $this->pgObjects->update('inventory_menu_line_item', [
				'id' => $updateData->item,
				'item' => $groupRoot,
				'choices' => $choiceSelection,
				'absolute_qty' => $assignData['absolute_qty'],
				// 'absolute_price'=> $absolutePrice
			]);
		}

		$response['menu'] = $this->parseMenu($this->pgObjects->getById('inventory_menu', $menu['id'], 2));
		$response['reservations'] = $assignData['reservations'];

		return $response;
	}

	private function editMenuSection($event = null, $sysSettings = null)
	{

		$response = [];

		// get menu to edit
		$menu = $this->pgObjects->getById(
			'inventory_menu',
			intval($event->menu),
			0
		);

		$section = $event->section;

		if (is_array($menu['sections'])) {
			foreach ($menu['sections'] as $i => $currentSection) {

				if (intval($currentSection['id']) === intval($section->id)) {

					$menu['sections'][$i]['name'] = $section->name;
					$menu['sections'][$i]['details'] = $section->details;
					$menu['sections'][$i]['from'] = $section->from;
					$menu['sections'][$i]['to'] = $section->to;
					$menu['sections'][$i]['sortId'] = $section->sortId;
					$menu['sections'][$i]['hidden_on_invoice'] = $section->hidden_on_invoice;
				}
			}
		}

		$menu = $menu;
		$response['menu'] = $this->parseMenu($this->pgObjects->update(
			'inventory_menu',
			[
				'id' => $menu['id'], 'sections' => $menu['sections']
			],
			2
		));

		return $response;
	}

	private function removeItemFromMenu($event = null, $sysSettings = null, $menu = null)
	{

		// 		error_reporting(E_ALL);
		// 		ini_set('display_errors', '1');

		if ($event === null) {
			return $this->sendData(false, 1);
		}

		$updateData = $event;
		$single_unit = $sysSettings['single_unit'];
		$response = [];

		// get menu to update
		if ($menu === null) {
			$menu = $this->pgObjects->getById('inventory_menu', $updateData->menu, 0);
		}

		// get applicable reservations
		$reservationsToDelete = $this->pgObjects->where('item_reservation', array(
			'menu' => $updateData->menu,
			// 			'menu_section' => $updateData->section,
			'menu_item' => $updateData->item
		));

		// remove item from menu
		foreach ($menu['sections'] as $i => $section) {

			if ($section['id'] === $updateData->section) {

				if (property_exists($updateData, 'items')) {

					$menu['sections'][$i]['items'] = array();

					foreach ($updateData->items as $k => $itemToRemove) {

						foreach ($section['items'] as $j => $sectionItem) {

							if ($sectionItem == $itemToRemove) {

								unset($section['items'][$j]);
							}
						}
					}

					$menu['sections'][$i]['items'] = $section['items'];
				} else {

					$menu['sections'][$i]['items'] = __::filter($section['items'], function ($item) use ($updateData) {

						return $item !== $updateData->item;
					});
				}

				/*
oreach($menu['sections'][$i]['items'] as $j => $item){

					// reset all sortId values of items in section
					$menu['sections'][$i]['items'][$j]['sortId'] = $j + 1;

				}
*/
			}
		}

		$stockItemIds = [];
		foreach ($reservationsToDelete as $i => $res) {
			foreach ($res['inventory_item_ids'] as $j => $id) {
				array_push($stockItemIds, intval($id));
			}
		}

		$stockItems = $this->pgObjects->where('inventory_items', [
			'id' => [
				'type' => 'or',
				'values' => $stockItemIds
			]
		]);

		// delete reservation objs
		$inventoryToUpdate = [];
		foreach ($reservationsToDelete as $i => $reservation) {

			foreach ($reservation['items'] as $j => $res) {

				$oldQty = __::find($stockItems, function ($stockItem) use ($res) {

					return $stockItem['id'] === $res['inventory_item'];
				})['quantity'];

				// only update the stock of perishable items (non-perishables act as rentals)
				if ($reservation['type'] === 'use') {
					array_push($inventoryToUpdate, array(
						'id' => $res['inventory_item'],
						'quantity' => $oldQty + $res['qty'],
						'object_bp_type' => 'inventory_items'
					));
				}
			}

			// delete reservation obj
			$this->pgObjects->delete('item_reservation', $reservation['id']);
		}

		// update stock items
		$response['updated_stock_items'] = $this->pgObjects->update('inventory_items', $inventoryToUpdate, 0);

		//remove choices
        $itemWithChoices = array(
            "id" => $updateData->item,
            "choices" => [],
            "beo_servingstyle" => "",
            "beo_qty" => 1,
            "beo_note" => "",
            "beo_ingredients" => []
        );

        $this->pgObjects->update('inventory_menu_line_item', $itemWithChoices, 2);

		// update menu
		$this->pgObjects->delete('inventory_menu_line_item', $updateData->item);
		$response['menu'] = $this->parseMenu($this->pgObjects->update('inventory_menu', $menu, 2));

		return $response;
	}

	private function moveItemsBetweenSections($event = null, $sysSettings = null, $menu = null)
	{
		// error_reporting(E_ALL);
		// ini_set('display_errors', '1');

		if ($event === null) {
			return $this->sendData(false, 1);
		}

		$updateData = $event;
		$response = [];

		// get menu to update
		if ($menu === null) {
			$menu = $this->pgObjects->getById('inventory_menu', $updateData->menu, 0);
		}

		// ids to move:
		$idsToMoveArray = $updateData->itemsToMoveIds;

		// remove items from previous menu section:
		$fromSectionId = $updateData->fromSection;
		$fromSectionIndex = array_search($fromSectionId, array_column($menu["sections"], 'id'), TRUE);
		$currentFromSectionItemsArray = $menu["sections"][$fromSectionIndex]["items"];
		$updatedFromSectionItemsArray = array_diff($currentFromSectionItemsArray, $idsToMoveArray);
		$menu["sections"][$fromSectionIndex]["items"] = $updatedFromSectionItemsArray;

		// add items to new menu section:
		$toSectionId = $updateData->toSection;
		$toSectionIndex = array_search($toSectionId, array_column($menu["sections"], 'id'), TRUE);
		$currentToSectionItemsArray = $menu["sections"][$toSectionIndex]["items"];
		$updatedToSectionItemsArray = array_merge($currentToSectionItemsArray, $idsToMoveArray);
		$menu["sections"][$toSectionIndex]["items"] = $updatedToSectionItemsArray;

		// update menu object:
		$response['menu'] = $this->pgObjects->update('inventory_menu', $menu, 2);
		return $response;
	}

	private function removeSectionFromMenu($event = null, $sysSettings = null)
	{

		if ($event === null) {
			return $this->sendData(false, 1);
		}

		$response = [];
		$single_unit = $sysSettings['single_unit'];
		$menu = $this->pgObjects->getById('inventory_menu', $event->menu, 0);
		$sectionId = intval($event->section);

		$section = __::find($menu['sections'], function ($section) use ($sectionId) {
			return $section['id'] === $sectionId;
		});

		foreach ($section['items'] as $i => $item) {

			$request = (object) array(
				'menu' => $menu['id'],
				'section' => $section['id'],
				'item' => $item['id']
			);

			/*
$this->sendData(array(
				'request' => $request
			), 1);
			die();
*/
			$this->removeItemFromMenu($request, $sysSettings, $menu);
		}

		$menu['sections'] = __::filter($menu['sections'], function ($section) use ($sectionId) {
			return $section['id'] !== $sectionId;
		});

		/*
$this->sendData(array(
			'filtered' => $menu
		), 1);
		die();
*/

		$response['menu'] = $this->pgObjects->update('inventory_menu', $menu);

		return $response;
	}

	// OAUTH2 CLIENT REGISTRATION FLOW

	private function base64UrlEncode($text)
	{

		return str_replace(
			['+', '/', '='],
			['-', '_', ''],
			base64_encode($text)
		);
	}

	public function authorizeClient()
	{

		// get account info
		$userId = intval($_POST->account);
		$instanceKey = $_POST->instance;

		// get referrer data
		$referrer = [];
		parse_str(explode('?', $_SERVER['HTTP_REFERER'])[1], $referrer);

		// !TODO: 	Make this client agnostic.
		$clientId = $this->zapierClientId;
		$clientSecret = $this->zapierSecretKey;

		// create client
		$newClient = [
			'client_id' => 			$clientId, 'client_secret' => 	$clientSecret, 'redirect_uri' => 		$referrer['redirect_uri'], 'grant_types' => 		'', 'scope' => 			'', 'user_id' => 			$userId, 'instance' => 			$instanceKey
		];
		$this->pgObjects->setInstance($instanceKey);
		$created = $this->pgObjects->create(
			'oauth_client',
			$newClient
		);

		// create access code
		$newAccessCode = [
			'authorization_code' => 	bin2hex(random_bytes(16)), 'client_id' => 		$clientId, 'expires' => 			'', 'id_token' => 			'', 'redirect_uri' => 		$referrer['redirect_uri'], 'scope' => 			'', 'user_id' => 			$userId
		];

		$this->pgObjects->create(
			'oauth_authorization_code',
			$newAccessCode
		);

		return $this->sendData(
			[
				'redirect_uri' => $newClient['redirect_uri'] . '?code=' . $newAccessCode['authorization_code'] . '&state=' . $referrer['state']
			],
			1
		);
	}

	public function getEasyPDFCloudAccessToken()
	{

		// Set variables
		$clientID = $this->easyPDFCloudClientID;
		$clientSecret = $this->easyPDFCloudSecretKey;

		// Create Array
		$requestVars = array(
			'client_id' => $clientID,
			'client_secret' => $clientSecret,
			'grant_type' => 'client_credentials',
			'scope' => 'epc.api'
		);

		$ch = curl_init('https://www.easypdfcloud.com/oauth2/token');
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $requestVars);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

		$response = curl_exec($ch);

		curl_close($ch);

		return json_decode(json_encode($response));
	}

	public function createEasyPDFCloudJob()
	{

		// Set variables
		$accessToken = $_POST->accessToken;
		$fileName = $_POST->fileName;
		$fileURL = $_POST->fileURL;

		$fileContents = @\file_get_contents($fileURL);

		$ch = curl_init('https://api.easypdfcloud.com/v1/workflows/0000000006AE5CBC/jobs?file=' . $fileName . '&start=false&test=false');
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $accessToken, 'Content-Type: application/octet-stream'));
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
		curl_setopt($ch, CURLOPT_POSTFIELDS, $fileContents);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

		$response = curl_exec($ch);

		curl_close($ch);

		return json_decode(json_encode($response));
	}

	public function addFileToEasyPDFCloudJob()
	{

		// Set variables
		$accessToken = $_POST->accessToken;
		$jobID = $_POST->jobID;
		$fileName = $_POST->fileName;
		$fileURL = $_POST->fileURL;

		$fileContents = @\file_get_contents($fileURL);

		$ch = curl_init('https://api.easypdfcloud.com/v1/jobs/' . $jobID . '/input/' . $fileName);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $accessToken, 'Content-Type: application/octet-stream'));
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
		curl_setopt($ch, CURLOPT_POSTFIELDS, $fileContents);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

		$response = curl_exec($ch);

		curl_close($ch);

		return json_decode(json_encode($response));
	}

	public function startEasyPDFCloudJob()
	{

		// Set variables
		$accessToken = $_POST->accessToken;
		$jobID = $_POST->jobID;

		// Create Array
		$requestVars = json_encode(array(
			'operation' => 'start'
		));

		$ch = curl_init('https://api.easypdfcloud.com/v1/jobs/' . $jobID);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $accessToken, 'Content-Type: application/json'));
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $requestVars);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

		$response = curl_exec($ch);

		curl_close($ch);

		return json_decode(json_encode($response));
	}

	public function waitOnEasyPDFCloudJob()
	{

		// Set variables
		$accessToken = $_POST->accessToken;
		$jobID = $_POST->jobID;

		$ch = curl_init('https://api.easypdfcloud.com/v1/jobs/' . $jobID . '/event');
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $accessToken, 'Content-Type: application/json'));
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

		$response = curl_exec($ch);

		curl_close($ch);

		return json_decode(json_encode($response));
	}

	public function downloadEasyPDFCloudFile()
	{

		// Set variables
		$accessToken = $_POST->accessToken;
		$jobID = $_POST->jobID;
		$obj = $_POST->obj;
		$save = $_POST->save;

		$ch = curl_init('https://api.easypdfcloud.com/v1/jobs/' . $jobID . '/output?type=file');
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $accessToken, 'Content-type: application/pdf'));
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

		$response = curl_exec($ch);

		curl_close($ch);

		$fileName = rand(100000, 900000) . '-combined';
		file_put_contents($fileName . '.pdf', $response);

		if ($save) {

			$documentObj = array(
				'name' => $obj->name,
				'document_type' => 'upload',
				'share_link' => 'custom-file',
				'is_public' => 1,
				'tagged_with' => $obj->tagged_with
			);

			// Create a file
			if ($objectData = $this->pgObjects->create('document', $documentObj)) {

				$fileData = array(
					'fileName' => $fileName,
					'fileType' => 'pdf',
					'objectType' => 'document',
					'objectId' => $objectData['id'],
					'parent' => 0,
					'tagged_with' => $obj->tagged_with,
					'isPublic' => 1
				);

				$file = array(
					'name' => $fileName . '.pdf',
					'type' => 'application/pdf',
					'tmp_name' => $fileName . '.pdf',
					'error' => 0,
					'size' => filesize($fileName . '.pdf')
				);

				$files = new FileApi($this->pgObjects, '../_files/_instances/' . $_REQUEST['pagodaAPIKey'], $_REQUEST['pagodaAPIKey']);

				if ($fileMetaData = $files->upload($file, $fileData, 0)) {

					// update object with file id
					if ($updatedObj = $this->pgObjects->update('document', array(
						'id' => $objectData['id'],
						'file_upload' => $fileMetaData['id'],
						'file_type' => $fileMetaData['file_type'],
						'loc' => $fileMetaData['loc']
					), $childObjs)) {

						// Update PDF with related file
						if ($updatedObj = $this->pgObjects->update('pdfs', array(
							'id' => $obj->id,
							'related_file' => $objectData['id']
						), $childObjs)) {

							return json_encode(array(
								'fileName' => $fileName . '.pdf'
							));
						}
					}
				}
			}
		} else {

			return json_encode(array(
				'fileName' => $fileName . '.pdf'
			));
		}
	}

	public function deleteEasyPDFCloudJob()
	{

		// Set variables
		$accessToken = $_POST->accessToken;
		$jobID = $_POST->jobID;

		// Create Array
		$requestVars = array();

		$ch = curl_init('https://api.easypdfcloud.com/v1/jobs/' . $jobID);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $accessToken, 'Content-Type: application/json'));
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
		curl_setopt($ch, CURLOPT_POSTFIELDS, $requestVars);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

		$response = curl_exec($ch);

		curl_close($ch);

		return json_decode(json_encode($response));
	}

	public function getEasyPDFCloudJob()
	{

		// Set variables
		$accessToken = $_POST->accessToken;
		$jobID = $_POST->jobID;

		// Create Array
		$requestVars = array();

		$ch = curl_init('https://api.easypdfcloud.com/v1/jobs/' . $jobID);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $accessToken, 'Content-Type: application/json'));
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
		curl_setopt($ch, CURLOPT_POSTFIELDS, $requestVars);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

		$response = curl_exec($ch);

		curl_close($ch);

		return json_decode(json_encode($response));
	}

	public function getAccessToken()
	{

		// !check auth code
		$authCode = $this->pgObjects->whereAll(
			'oauth_authorization_code',
			[
				'client_id' => 				$_POST['client_id'], 'authorization_code' => 	$_POST['code']
			]
		)[0];

		// !TODO: 	Mark auth codes as used. If auth code is attempted to be
		// 			used a second time, treat this as an attack.
		// 				[ ] 	Revoke all tokens issued from this code.
		// 				[ ] Log event.

		// !TODO: 	Check expiration time.

		$valid = true;
		if ($valid) {

			// get user (for email address in token)
			$this->pgObjects->setInstance($authCode['instance']);
			$user = $this->pgObjects->getById(
				'users',
				$authCode['user_id'],
				[
					'email' => true
				]
			);

			// create token
			$secret = $this->zapierSecretKey;
			$header = json_encode([
				'typ' => 'JWT',
				'alg' => 'HS256'
			]);
			$payload = json_encode([
				'email' => $user['email'], 'exp' => 1593828222
			]);
			$base64UrlHeader = $this->base64UrlEncode($header);
			$base64UrlPayload = $this->base64UrlEncode($payload);

			// signature
			$signature = hash_hmac(
				'sha256',
				$base64UrlHeader . "." . $base64UrlPayload,
				$secret,
				true
			);
			$base64UrlSignature = $this->base64UrlEncode($signature);

			// Create JWT (Json Web Token)
			$jwt = $base64UrlHeader . "." . $base64UrlPayload . "." . $base64UrlSignature;

			$token = [
				'access_token' => 	$jwt, 'client_id' => 	$_POST['client_id'], 'scope' => 		'updates-feed', 'user_id' => 		$authCode['user_id']
			];

			$createdToken = $this->pgObjects->create(
				'oauth_access_token',
				$token
			);
			if ($createdToken) {

				return $this->sendData(
					[
						'access_token' => $token['access_token'], 'expires_in' => 3600
					],
					1
				);
			}
		}

		return $this->sendData(
			[
				'error' => 	'invalid_request'
			],
			1
		);
	}

	public function testToken($token, $json = true)
	{

		$token = explode(' ', $token)[1];
		$secret = $this->zapierSecretKey;

		// split the token
		$tokenSegments = explode('.', $token);
		$header = base64_decode($tokenSegments[0]);
		$payload = base64_decode($tokenSegments[1]);
		$signatureProvided = $tokenSegments[2];

		// build a signature based on the header and payload using the secret
		$base64UrlHeader = $this->base64UrlEncode($header);
		$base64UrlPayload = $this->base64UrlEncode($payload);
		$signature = hash_hmac(
			'sha256',
			$base64UrlHeader . "." . $base64UrlPayload,
			$secret,
			true
		);
		$base64UrlSignature = $this->base64UrlEncode($signature);

		// verify it matches the signature provided in the token
		$isValidSignature = ($base64UrlSignature === $signatureProvided);

		// !TODO: 	Save time in token payload, and check expiration time here.

		if ($isValidSignature) {

			// get token
			$storedToken = $this->pgObjects->whereAll(
				'oauth_access_token',
				[
					'access_token' => $token
				]
			)[0];

			// return if no record of received token
			if (empty($storedToken)) {

				return $this->sendData(
					[
						'error' => 	'invalid_request'
					],
					1
				);
			}

			// check that email matches user
			$email = json_decode($payload, 1)['email'];
			$users = $this->pgObjects->whereAll(
				'users',
				array(
					'email' => $email, 'enabled' => 1
				),
				'',
				[
					'id' => true
				]
			);

			$user = false;
			if (is_array($users)) {
				foreach ($users as $i => $usr) {

					if ($usr['id'] === $storedToken['user_id']) {
						$user = $usr;
					}
				}
			}
			if (!$user) {

				return $this->sendData(
					[
						'error' => 	'invalid_request'
					],
					1
				);
			}

			if ($json) {

				return $this->sendData(
					[
						'fname' => 		$user['fname'], 'lname' => 	$user['lname'], 'email' => 	$user['email'], 'instance' => 	$user['instance']
					],
					1
				);
			} else {

				return $this->sendData(
					[
						'user' => 		$user, 'token' => 	$storedToken
					],
					$json
				);
			}
		}

		return $this->sendData(
			[
				'error' => 	'invalid_request'
			],
			1
		);
	}

	// REST Hooks

	public function subscribeToRestHook($token, $json = 1)
	{

		// verify token, and get user from token
		$userInfo = $this->testToken($token, false);

		if ($userInfo['error']) {

			return $this->sendData(
				[
					'error' => 'invalid_request'
				],
				$json
			);
		} else {

			$subscription = [
				'event_type' => 		'state-changed', 'hook_id' => 		bin2hex(random_bytes(16)), 'hook_url' => 		getallheaders()['hookurl'], 'instance' => 		$userInfo['user']['instance'], 'is_active' => 	true, 'token' => 		$userInfo['token']['id'], 'user' => 			$userInfo['user']['id']
			];

			$this->pgObjects->setInstance($userInfo['user']['instance']);
			$created = $this->pgObjects->create(
				'hook_subscription',
				$subscription
			);

			if ($created) {

				return $this->sendData(
					[
						'subscription' => $subscription['hook_id']
					],
					1
				);
			}
		}
	}

	public function testSubscription($token, $json = 1)
	{

		// verify token, and get user from token
		$userInfo = $this->testToken($token, false);

		if ($userInfo['error']) {

			return $this->sendData(
				[
					[
						'error' => 'invalid_token'
					]
				],
				$json
			);
		} else {

			// get subscription
			$this->pgObjects->setInstance($userInfo['user']['instance']);
			$subscription = $this->pgObjects->where(
				'hook_subscription',
				[
					'is_active' => 	true, 'token' => 	$userInfo['token']['id'], 'user' => 		$userInfo['user']['id']
				]
			)[0];

			// If it is a valid subscription, return test data that matches
			// that type of event. (This should always be an array.)
			if ($subscription) {

				$ret = [];
				switch ($subscription['event_type']) {

					case 'state-changed':
						$ret = [
							[
								'eventType' => 		'state-changed', 'objectName' => 	'Test Object', 'newState' => 		'Active', 'timestamp' => 	'date'
							]
						];
						break;
				}

				return $this->sendData(
					$ret,
					$json
				);
			}
		}

		return $this->sendData(
			[
				[
					'error' => 'invalid_subscription', 'test' => [
						'is_active' => 	true, 'token' => 	$userInfo['token']['id'], 'user' => 		$userInfo['user']['id']
					]
				]
			],
			$json
		);
	}
}
