/**
 * <PERSON><PERSON><PERSON> to find original records and their "(*)" duplicates from a master list of IDs
 * Uses getById to determine object type automatically, then finds duplicates
 */

// Your master list of original record IDs (40 items)
const masterIds = [1752151,1198321,1751611,1699713,1693976,1697388,1699865,1699776,1699749,1699810,1696310,1695773,1705485,1697110,1697180,1697097,1699907,1705510,5064004,1699579,1699613,1697291,1701412,1723117,1723124,1705542,1959541,1959918,5631445,5533443,5743696,5743694,5743695,5747310,5747311,5747314,5747317,1701892,18240121, 18363168];

// Global arrays to collect results
var originalRecords = [];
var duplicateRecords = [];
var missingDuplicates = [];
var errorRecords = [];
var allRecordsByType = {};

/**
 * Main function to find original records and their "(*)" duplicates
 * Automatically detects object types and finds duplicates
 */
function findOriginalAndDuplicateRecords() {
  console.log(`\n========== FINDING ORIGINAL RECORDS AND THEIR "(*)" DUPLICATES ==========`);
  console.log(`Master IDs to check: ${masterIds.length}`);
  console.log(`Expected total records to find: ${masterIds.length * 2} (${masterIds.length} originals + ${masterIds.length} duplicates)`);
  console.log(`========================================================================\n`);

  // Reset global arrays
  originalRecords = [];
  duplicateRecords = [];
  missingDuplicates = [];
  errorRecords = [];
  allRecordsByType = {};

  // Get all records by ID first to determine their types
  console.log(`Step 1: Getting original records by ID...`);
  getOriginalRecordsByIds(0);
}

/**
 * Get original records by ID to determine their object types
 */
function getOriginalRecordsByIds(index) {
  if (index >= masterIds.length) {
    console.log(`\nStep 2: Getting all records for each object type to find duplicates...`);
    findDuplicatesForAllTypes();
    return;
  }

  const recordId = masterIds[index];
  console.log(`Getting record ${index + 1}/${masterIds.length}: ID ${recordId}`);

  // Use empty string to let getById determine the object type
  databaseConnection.obj.getById('', recordId, function(record) {
    if (record && record.name && record.object_bp_type) {
      console.log(`  Found: "${record.name}" (ID: ${record.id}) [Type: ${record.object_bp_type}]`);

      originalRecords.push({
        id: record.id,
        name: record.name,
        object_bp_type: record.object_bp_type,
        category: record.category || 'N/A'
      });

      // Track object types we need to query
      if (!allRecordsByType[record.object_bp_type]) {
        allRecordsByType[record.object_bp_type] = [];
      }
    } else {
      console.log(`  ⚠️ ERROR: Record not found or missing data`);
      errorRecords.push({
        id: recordId,
        error: 'Record not found or missing name/object_bp_type'
      });
    }

    // Process next record
    setTimeout(function() {
      getOriginalRecordsByIds(index + 1);
    }, 50);
  });
}

/**
 * Get all records for each object type and find duplicates
 */
function findDuplicatesForAllTypes() {
  const objectTypes = Object.keys(allRecordsByType);
  console.log(`\nFound ${objectTypes.length} object types: ${objectTypes.join(', ')}`);

  if (objectTypes.length === 0) {
    console.log(`No valid object types found!`);
    showResults();
    return;
  }

  // Process each object type
  processNextObjectType(objectTypes, 0);
}

/**
 * Process each object type to get all records and find duplicates
 */
function processNextObjectType(objectTypes, typeIndex) {
  if (typeIndex >= objectTypes.length) {
    console.log(`\nStep 3: Matching originals to duplicates...`);
    matchOriginalsToTheirDuplicates();
    return;
  }

  const objectType = objectTypes[typeIndex];
  console.log(`\nGetting all records for type: ${objectType}`);

  databaseConnection.obj.getAll(objectType, function(allRecords) {
    console.log(`  Retrieved ${allRecords.length} total ${objectType} records`);
    allRecordsByType[objectType] = allRecords;

    // Process next object type
    setTimeout(function() {
      processNextObjectType(objectTypes, typeIndex + 1);
    }, 100);
  });
}

/**
 * Match each original record to its duplicate
 */
function matchOriginalsToTheirDuplicates() {
  originalRecords.forEach((original, index) => {
    console.log(`\nMatching ${index + 1}/${originalRecords.length}: "${original.name}" (ID: ${original.id})`);

    const expectedDuplicateName = original.name + ' (*)';
    const allRecordsOfThisType = allRecordsByType[original.object_bp_type] || [];

    // Find exact match for the duplicate name
    const duplicates = allRecordsOfThisType.filter(r =>
      r.name === expectedDuplicateName ||
      r.name.trim() === expectedDuplicateName.trim()
    );

    if (duplicates.length > 0) {
      const duplicate = duplicates[0];
      console.log(`  ✅ DUPLICATE FOUND: "${duplicate.name}" (ID: ${duplicate.id})`);

      duplicateRecords.push({
        id: duplicate.id,
        name: duplicate.name,
        object_bp_type: duplicate.object_bp_type,
        category: duplicate.category || 'N/A',
        originalId: original.id,
        originalName: original.name
      });
    } else {
      console.log(`  ⚠️ DUPLICATE MISSING: "${expectedDuplicateName}"`);

      missingDuplicates.push({
        originalId: original.id,
        originalName: original.name,
        expectedDuplicateName: expectedDuplicateName,
        objectType: original.object_bp_type
      });
    }
  });

  showResults();
}

/**
 * Show the final results
 */
function showResults() {
  console.log(`\n========== FINAL RESULTS SUMMARY ==========`);
  console.log(`Master records processed: ${masterIds.length}`);
  console.log(`Original records found: ${originalRecords.length}`);
  console.log(`Duplicate records found: ${duplicateRecords.length}`);
  console.log(`Missing duplicates: ${missingDuplicates.length}`);
  console.log(`Error records: ${errorRecords.length}`);
  console.log(`Total records found: ${originalRecords.length + duplicateRecords.length} (Expected: ${masterIds.length * 2})`);
  console.log(`==========================================\n`);

  if (originalRecords.length > 0) {
    console.log(`\n--- ORIGINAL RECORDS FOUND (${originalRecords.length}) ---`);
    originalRecords.forEach((record, index) => {
      console.log(`${index + 1}. "${record.name}" (ID: ${record.id}) [Category: ${record.category}]`);
    });
  }

  if (duplicateRecords.length > 0) {
    console.log(`\n--- DUPLICATE RECORDS FOUND (${duplicateRecords.length}) ---`);
    duplicateRecords.forEach((record, index) => {
      console.log(`${index + 1}. "${record.name}" (ID: ${record.id}) [Category: ${record.category}]`);
      console.log(`    → Duplicate of: "${record.originalName}" (ID: ${record.originalId})`);
    });
  }

  if (missingDuplicates.length > 0) {
    console.log(`\n--- MISSING DUPLICATES (${missingDuplicates.length}) ---`);
    missingDuplicates.forEach((missing, index) => {
      console.log(`${index + 1}. Expected: "${missing.expectedDuplicateName}"`);
      console.log(`    → Missing duplicate of: "${missing.originalName}" (ID: ${missing.originalId})`);
    });
  }

  if (errorRecords.length > 0) {
    console.log(`\n--- ERROR RECORDS (${errorRecords.length}) ---`);
    errorRecords.forEach((record, index) => {
      console.log(`${index + 1}. ID: ${record.id} - ${record.error}`);
    });
  }

  // Show all IDs for easy copying
  if (originalRecords.length > 0) {
    console.log(`\n--- ALL ORIGINAL IDS (for easy copying) ---`);
    const originalIds = originalRecords.map(r => r.id);
    console.log(`[${originalIds.join(', ')}]`);
  }

  if (duplicateRecords.length > 0) {
    console.log(`\n--- ALL DUPLICATE IDS (for easy copying) ---`);
    const duplicateIds = duplicateRecords.map(r => r.id);
    console.log(`[${duplicateIds.join(', ')}]`);
  }

  if (originalRecords.length > 0 && duplicateRecords.length > 0) {
    console.log(`\n--- ALL 80 IDS COMBINED (for easy copying) ---`);
    const allIds = [...originalRecords.map(r => r.id), ...duplicateRecords.map(r => r.id)];
    console.log(`[${allIds.join(', ')}]`);
  }
}

/**
 * Quick function to get just the original record IDs
 */
function getOriginalIds() {
  return originalRecords.map(r => r.id);
}

/**
 * Quick function to get just the duplicate record IDs
 */
function getDuplicateIds() {
  return duplicateRecords.map(r => r.id);
}

/**
 * Quick function to get all 80 IDs combined
 */
function getAllIds() {
  return [...originalRecords.map(r => r.id), ...duplicateRecords.map(r => r.id)];
}

/**
 * Quick function to get missing duplicate info
 */
function getMissingDuplicates() {
  return missingDuplicates;
}

// Auto-run the function
findOriginalAndDuplicateRecords();

console.log(`Script loaded and running automatically!`);
console.log(`\nAfter completion, you can use these helper functions:`);
console.log(`getOriginalIds() - Returns array of original record IDs`);
console.log(`getDuplicateIds() - Returns array of duplicate record IDs`);
console.log(`getAllIds() - Returns all 80 IDs combined`);
console.log(`getMissingDuplicates() - Returns info about missing duplicates`);
